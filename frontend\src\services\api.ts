import axios from 'axios';

// API基础配置
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // 处理认证失败
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 数据类型定义
export interface Token {
  id: number;
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  chain: string;
  current_price: number;
  price_change_24h: number;
  volume_24h: number;
  market_cap: number;
  liquidity_usd: number;
  holders_count: number;
  is_active: boolean;
  is_verified: boolean;
  risk_score: number;
  created_at: string;
  updated_at: string;
}

export interface TradingOrder {
  id: number;
  order_id: string;
  strategy_name: string;
  chain: string;
  token_address: string;
  token_symbol: string;
  order_type: 'buy' | 'sell';
  amount: string;
  price: number;
  filled_amount: string;
  average_price: number;
  status: string;
  tx_hash?: string;
  executed_at?: string;
  cancelled_at?: string;
  created_at: string;
  updated_at: string;
}

export interface TradingSignal {
  id: number;
  signal_id: string;
  strategy_name: string;
  chain: string;
  token_address: string;
  token_symbol: string;
  signal_type: string;
  confidence: number;
  current_price: number;
  target_price?: number;
  stop_loss?: number;
  analysis_data?: string;
  is_executed: boolean;
  executed_at?: string;
  order_id?: string;
  created_at: string;
}

export interface Portfolio {
  id: number;
  chain: string;
  token_address: string;
  token_symbol: string;
  balance: string;
  average_cost: number;
  total_invested: number;
  current_value: number;
  unrealized_pnl: number;
  realized_pnl: number;
  last_updated: string;
  created_at: string;
}

// API服务类
export class ApiService {
  // 代币相关API
  static async getTokens(params?: {
    chain?: string;
    is_active?: boolean;
    skip?: number;
    limit?: number;
  }) {
    const response = await api.get('/tokens', { params });
    return response.data;
  }

  static async createToken(tokenData: Partial<Token>) {
    const response = await api.post('/tokens', tokenData);
    return response.data;
  }

  static async getToken(tokenId: number) {
    const response = await api.get(`/tokens/${tokenId}`);
    return response.data;
  }

  static async toggleTokenMonitoring(tokenId: number) {
    const response = await api.put(`/tokens/${tokenId}/toggle`);
    return response.data;
  }

  // 订单相关API
  static async getOrders(params?: {
    status?: string;
    strategy?: string;
    skip?: number;
    limit?: number;
  }) {
    const response = await api.get('/orders', { params });
    return response.data;
  }

  static async createOrder(orderData: {
    strategy_name: string;
    chain: string;
    token_address: string;
    token_symbol: string;
    order_type: 'buy' | 'sell';
    amount: string;
    price: number;
  }) {
    const response = await api.post('/orders', orderData);
    return response.data;
  }

  static async getOrder(orderId: string) {
    const response = await api.get(`/orders/${orderId}`);
    return response.data;
  }

  static async cancelOrder(orderId: string) {
    const response = await api.post(`/orders/${orderId}/cancel`);
    return response.data;
  }

  // 交易信号相关API
  static async getSignals(params?: {
    strategy?: string;
    signal_type?: string;
    is_executed?: boolean;
    skip?: number;
    limit?: number;
  }) {
    const response = await api.get('/signals', { params });
    return response.data;
  }

  static async getSignal(signalId: string) {
    const response = await api.get(`/signals/${signalId}`);
    return response.data;
  }

  // 投资组合相关API
  static async getPortfolio(chain?: string) {
    const response = await api.get('/portfolio', { params: { chain } });
    return response.data;
  }

  static async getPortfolioSummary() {
    const response = await api.get('/portfolio/summary');
    return response.data;
  }

  // 策略相关API
  static async getStrategies() {
    const response = await api.get('/strategies');
    return response.data;
  }

  static async getStrategyConfig(strategyName: string) {
    const response = await api.get(`/strategies/${strategyName}/config`);
    return response.data;
  }

  static async updateStrategyConfig(strategyName: string, config: any) {
    const response = await api.put(`/strategies/${strategyName}/config`, { config });
    return response.data;
  }

  // 系统状态相关API
  static async getSystemStatus() {
    const response = await api.get('/status');
    return response.data;
  }

  static async getStats() {
    const response = await api.get('/stats');
    return response.data;
  }
}

export default api;
