import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Switch,
  FormControlLabel,
  Grid,
  Alert,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import toast from 'react-hot-toast';
import { Helmet } from 'react-helmet-async';

// Mock data
const mockTokens = [
  {
    id: 1,
    address: '******************************************',
    symbol: 'UNI',
    name: 'Uniswap',
    chain: 'ethereum',
    current_price: 6.45,
    price_change_24h: 2.3,
    volume_24h: 125000000,
    market_cap: 4850000000,
    liquidity_usd: 85000000,
    is_active: true,
    is_verified: true,
    risk_score: 0.3,
  },
  {
    id: 2,
    address: '******************************************',
    symbol: 'DAI',
    name: 'Dai Stablecoin',
    chain: 'ethereum',
    current_price: 1.0,
    price_change_24h: 0.1,
    volume_24h: 45000000,
    market_cap: 5200000000,
    liquidity_usd: 120000000,
    is_active: true,
    is_verified: true,
    risk_score: 0.1,
  },
];

const schema = yup.object({
  address: yup.string().required('代币地址是必填项'),
  symbol: yup.string().required('代币符号是必填项'),
  name: yup.string().required('代币名称是必填项'),
  chain: yup.string().required('请选择区块链'),
  decimals: yup.number().min(0).max(18).required('请输入代币精度'),
});

const Tokens: React.FC = () => {
  const [tokens, setTokens] = useState(mockTokens);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingToken, setEditingToken] = useState<any>(null);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      address: '',
      symbol: '',
      name: '',
      chain: '',
      decimals: 18,
    },
  });

  const handleAddToken = () => {
    setEditingToken(null);
    reset();
    setOpenDialog(true);
  };

  const handleEditToken = (token: any) => {
    setEditingToken(token);
    reset(token);
    setOpenDialog(true);
  };

  const handleDeleteToken = (tokenId: number) => {
    setTokens(tokens.filter(token => token.id !== tokenId));
    toast.success('代币已删除');
  };

  const handleToggleMonitoring = (tokenId: number) => {
    setTokens(tokens.map(token => 
      token.id === tokenId 
        ? { ...token, is_active: !token.is_active }
        : token
    ));
    toast.success('监控状态已更新');
  };

  const onSubmit = (data: any) => {
    if (editingToken) {
      setTokens(tokens.map(token => 
        token.id === editingToken.id 
          ? { ...token, ...data }
          : token
      ));
      toast.success('代币信息已更新');
    } else {
      const newToken = {
        id: Date.now(),
        ...data,
        current_price: 0,
        price_change_24h: 0,
        volume_24h: 0,
        market_cap: 0,
        liquidity_usd: 0,
        is_active: true,
        is_verified: false,
        risk_score: 0.5,
      };
      setTokens([...tokens, newToken]);
      toast.success('代币已添加');
    }
    setOpenDialog(false);
  };

  const columns: GridColDef[] = [
    {
      field: 'symbol',
      headerName: '代币',
      width: 120,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2" sx={{ fontWeight: 600 }}>
            {params.row.symbol}
          </Typography>
          {params.row.is_verified && (
            <Chip label="已验证" size="small" color="success" variant="outlined" />
          )}
        </Box>
      ),
    },
    {
      field: 'name',
      headerName: '名称',
      width: 150,
    },
    {
      field: 'chain',
      headerName: '区块链',
      width: 100,
      renderCell: (params) => (
        <Chip 
          label={params.value.toUpperCase()} 
          size="small" 
          variant="outlined"
          color={
            params.value === 'ethereum' ? 'primary' :
            params.value === 'bsc' ? 'warning' :
            params.value === 'solana' ? 'secondary' : 'default'
          }
        />
      ),
    },
    {
      field: 'current_price',
      headerName: '价格',
      width: 120,
      renderCell: (params) => (
        <Typography variant="body2">
          ${params.value.toFixed(4)}
        </Typography>
      ),
    },
    {
      field: 'price_change_24h',
      headerName: '24h变化',
      width: 120,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          {params.value > 0 ? (
            <TrendingUpIcon sx={{ color: 'success.main', fontSize: 16 }} />
          ) : (
            <TrendingDownIcon sx={{ color: 'error.main', fontSize: 16 }} />
          )}
          <Typography
            variant="body2"
            sx={{ color: params.value > 0 ? 'success.main' : 'error.main' }}
          >
            {params.value > 0 ? '+' : ''}{params.value.toFixed(2)}%
          </Typography>
        </Box>
      ),
    },
    {
      field: 'volume_24h',
      headerName: '24h交易量',
      width: 130,
      renderCell: (params) => (
        <Typography variant="body2">
          ${(params.value / 1000000).toFixed(2)}M
        </Typography>
      ),
    },
    {
      field: 'risk_score',
      headerName: '风险评分',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value.toFixed(2)}
          size="small"
          color={
            params.value < 0.3 ? 'success' :
            params.value < 0.7 ? 'warning' : 'error'
          }
        />
      ),
    },
    {
      field: 'is_active',
      headerName: '监控状态',
      width: 120,
      renderCell: (params) => (
        <Switch
          checked={params.value}
          onChange={() => handleToggleMonitoring(params.row.id)}
          size="small"
        />
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: '操作',
      width: 120,
      getActions: (params) => [
        <GridActionsCellItem
          icon={<VisibilityIcon />}
          label="查看"
          onClick={() => {}}
        />,
        <GridActionsCellItem
          icon={<EditIcon />}
          label="编辑"
          onClick={() => handleEditToken(params.row)}
        />,
        <GridActionsCellItem
          icon={<DeleteIcon />}
          label="删除"
          onClick={() => handleDeleteToken(params.row.id)}
        />,
      ],
    },
  ];

  return (
    <>
      <Helmet>
        <title>代币监控 - AI Crypto Trading Agent</title>
      </Helmet>

      <Box>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" sx={{ fontWeight: 600 }}>
            代币监控
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddToken}
          >
            添加代币
          </Button>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom variant="body2">
                  监控代币总数
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 600 }}>
                  {tokens.length}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom variant="body2">
                  活跃监控
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 600 }}>
                  {tokens.filter(t => t.is_active).length}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom variant="body2">
                  已验证代币
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 600 }}>
                  {tokens.filter(t => t.is_verified).length}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom variant="body2">
                  高风险代币
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 600 }}>
                  {tokens.filter(t => t.risk_score > 0.7).length}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Data Grid */}
        <Card>
          <CardContent>
            <DataGrid
              rows={tokens}
              columns={columns}
              initialState={{
                pagination: {
                  paginationModel: { page: 0, pageSize: 10 },
                },
              }}
              pageSizeOptions={[10, 25, 50]}
              checkboxSelection
              disableRowSelectionOnClick
              sx={{
                border: 'none',
                '& .MuiDataGrid-cell': {
                  borderColor: '#2a2f3e',
                },
                '& .MuiDataGrid-columnHeaders': {
                  backgroundColor: 'rgba(0, 212, 255, 0.05)',
                  borderColor: '#2a2f3e',
                },
              }}
            />
          </CardContent>
        </Card>

        {/* Add/Edit Dialog */}
        <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>
            {editingToken ? '编辑代币' : '添加代币'}
          </DialogTitle>
          <form onSubmit={handleSubmit(onSubmit)}>
            <DialogContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Controller
                    name="address"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="代币地址"
                        fullWidth
                        error={!!errors.address}
                        helperText={errors.address?.message}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={6}>
                  <Controller
                    name="symbol"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="代币符号"
                        fullWidth
                        error={!!errors.symbol}
                        helperText={errors.symbol?.message}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={6}>
                  <Controller
                    name="decimals"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="精度"
                        type="number"
                        fullWidth
                        error={!!errors.decimals}
                        helperText={errors.decimals?.message}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Controller
                    name="name"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="代币名称"
                        fullWidth
                        error={!!errors.name}
                        helperText={errors.name?.message}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Controller
                    name="chain"
                    control={control}
                    render={({ field }) => (
                      <FormControl fullWidth error={!!errors.chain}>
                        <InputLabel>区块链</InputLabel>
                        <Select {...field} label="区块链">
                          <MenuItem value="ethereum">Ethereum</MenuItem>
                          <MenuItem value="bsc">Binance Smart Chain</MenuItem>
                          <MenuItem value="solana">Solana</MenuItem>
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setOpenDialog(false)}>取消</Button>
              <Button type="submit" variant="contained">
                {editingToken ? '更新' : '添加'}
              </Button>
            </DialogActions>
          </form>
        </Dialog>
      </Box>
    </>
  );
};

export default Tokens;
