# AI Crypto Trading Agent 策略配置文件

strategies:
  # 动量策略
  momentum:
    enabled: true
    description: "基于RSI和交易量的动量交易策略"
    config:
      # RSI参数
      rsi_period: 14
      rsi_oversold: 30
      rsi_overbought: 70
      
      # 交易量参数
      volume_threshold: 1.5  # 交易量放大倍数
      volume_period: 20      # 交易量平均周期
      
      # 信号参数
      min_confidence: 0.6    # 最小置信度
      signal_cooldown: 300   # 信号冷却时间(秒)
      
      # 风险参数
      max_position_ratio: 0.1  # 最大仓位比例
      stop_loss: 0.1          # 止损比例
      take_profit: 0.2        # 止盈比例

  # 均值回归策略
  mean_reversion:
    enabled: true
    description: "基于布林带的均值回归策略"
    config:
      # 布林带参数
      bollinger_period: 20
      bollinger_std: 2
      
      # 偏离参数
      min_deviation: 0.02    # 最小偏离度
      max_deviation: 0.1     # 最大偏离度
      
      # 信号参数
      min_confidence: 0.5
      signal_cooldown: 600
      
      # 风险参数
      max_position_ratio: 0.08
      stop_loss: 0.08
      take_profit: 0.15

  # 机器学习策略
  ml_strategy:
    enabled: false
    description: "基于随机森林的机器学习策略"
    config:
      # 模型参数
      model_type: "random_forest"
      n_estimators: 100
      max_depth: 10
      random_state: 42
      
      # 特征参数
      feature_period: 50     # 特征计算周期
      prediction_horizon: 12 # 预测时间范围
      
      # 信号参数
      confidence_threshold: 0.6
      min_confidence: 0.7
      signal_cooldown: 900
      
      # 训练参数
      retrain_interval: 24   # 重训练间隔(小时)
      min_training_samples: 1000
      
      # 风险参数
      max_position_ratio: 0.05
      stop_loss: 0.12
      take_profit: 0.25

  # 网格交易策略
  grid_trading:
    enabled: false
    description: "网格交易策略"
    config:
      # 网格参数
      grid_count: 10         # 网格数量
      price_range: 0.2       # 价格范围(20%)
      grid_spacing: 0.02     # 网格间距(2%)
      
      # 交易参数
      order_amount_ratio: 0.01  # 单次交易金额比例
      min_profit: 0.01       # 最小利润
      
      # 风险参数
      max_position_ratio: 0.15
      stop_loss: 0.15
      emergency_exit: 0.25   # 紧急退出阈值

  # 套利策略
  arbitrage:
    enabled: false
    description: "跨DEX套利策略"
    config:
      # 套利参数
      min_profit_threshold: 0.005  # 最小利润阈值(0.5%)
      max_slippage: 0.003         # 最大滑点
      
      # 支持的DEX
      supported_dexes:
        - "uniswap_v2"
        - "uniswap_v3"
        - "sushiswap"
        - "pancakeswap_v2"
      
      # 交易参数
      max_trade_amount: 1000  # 最大交易金额(USD)
      gas_price_limit: 50     # Gas价格限制(Gwei)
      
      # 风险参数
      max_position_ratio: 0.05
      timeout: 30            # 交易超时时间(秒)

# 全局策略设置
global_settings:
  # 执行参数
  max_concurrent_signals: 5    # 最大并发信号数
  signal_expiry: 3600         # 信号过期时间(秒)
  
  # 风险管理
  global_stop_loss: 0.2       # 全局止损
  max_daily_trades: 50        # 每日最大交易次数
  max_daily_loss: 0.05        # 每日最大损失
  
  # 市场条件
  min_market_cap: 1000000     # 最小市值(USD)
  min_liquidity: 100000       # 最小流动性(USD)
  min_volume_24h: 50000       # 最小24h交易量(USD)
  
  # 黑名单代币
  blacklist:
    - "0x0000000000000000000000000000000000000000"
  
  # 白名单代币(优先交易)
  whitelist:
    - "0x1f9840a85d5af5bf1d1762f925bdaddc4201f984"  # UNI
    - "0x6b175474e89094c44da98b954eedeac495271d0f"  # DAI

# 回测设置
backtest:
  enabled: false
  start_date: "2024-01-01"
  end_date: "2024-12-31"
  initial_balance: 10000
  commission: 0.003           # 手续费率
  slippage: 0.001            # 滑点

# 通知设置
notifications:
  # 信号通知
  signal_notifications: true
  trade_notifications: true
  error_notifications: true
  
  # 通知阈值
  min_profit_notification: 100    # 最小利润通知(USD)
  min_loss_notification: 50       # 最小损失通知(USD)
