"""
API数据模式
"""

from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from app.models.trading import ChainType, OrderType, OrderStatus, TransactionStatus


# 代币相关模式
class TokenBase(BaseModel):
    address: str = Field(..., description="代币合约地址")
    symbol: str = Field(..., description="代币符号")
    name: str = Field(..., description="代币名称")
    decimals: int = Field(..., description="代币精度")
    chain: ChainType = Field(..., description="所属区块链")


class TokenCreate(TokenBase):
    current_price: Optional[float] = Field(0.0, description="当前价格")
    is_active: Optional[bool] = Field(True, description="是否启用监控")


class TokenResponse(TokenBase):
    id: int
    current_price: float
    price_change_24h: float
    volume_24h: float
    market_cap: float
    liquidity_usd: float
    holders_count: int
    is_active: bool
    is_verified: bool
    risk_score: float
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# 订单相关模式
class OrderBase(BaseModel):
    strategy_name: str = Field(..., description="策略名称")
    chain: ChainType = Field(..., description="区块链")
    token_address: str = Field(..., description="代币地址")
    token_symbol: str = Field(..., description="代币符号")
    order_type: OrderType = Field(..., description="订单类型")
    amount: str = Field(..., description="交易数量")
    price: float = Field(..., description="交易价格")


class OrderCreate(OrderBase):
    pass


class OrderResponse(OrderBase):
    id: int
    order_id: str
    filled_amount: str
    average_price: float
    status: OrderStatus
    tx_hash: Optional[str]
    executed_at: Optional[datetime]
    cancelled_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# 交易信号相关模式
class SignalResponse(BaseModel):
    id: int
    signal_id: str
    strategy_name: str
    chain: ChainType
    token_address: str
    token_symbol: str
    signal_type: str
    confidence: float
    current_price: float
    target_price: Optional[float]
    stop_loss: Optional[float]
    analysis_data: Optional[str]
    is_executed: bool
    executed_at: Optional[datetime]
    order_id: Optional[str]
    created_at: datetime
    
    class Config:
        from_attributes = True


# 投资组合相关模式
class PortfolioResponse(BaseModel):
    id: int
    chain: ChainType
    token_address: str
    token_symbol: str
    balance: str
    average_cost: float
    total_invested: float
    current_value: float
    unrealized_pnl: float
    realized_pnl: float
    last_updated: datetime
    created_at: datetime
    
    class Config:
        from_attributes = True


# 策略配置相关模式
class StrategyConfig(BaseModel):
    config: Dict[str, Any] = Field(..., description="策略配置参数")


# 交易相关模式
class TransactionResponse(BaseModel):
    id: int
    tx_hash: str
    chain: ChainType
    block_number: Optional[int]
    from_address: str
    to_address: str
    token_address: str
    amount: str
    amount_usd: float
    gas_used: int
    gas_price: str
    status: TransactionStatus
    timestamp: datetime
    created_at: datetime
    
    class Config:
        from_attributes = True


# 钱包相关模式
class WalletBalance(BaseModel):
    chain: ChainType
    token_address: Optional[str]
    token_symbol: str
    balance: str
    balance_usd: float


class WalletInfo(BaseModel):
    address: str
    chain: ChainType
    balances: list[WalletBalance]


# 市场数据相关模式
class MarketData(BaseModel):
    token_address: str
    token_symbol: str
    chain: ChainType
    price: float
    price_change_24h: float
    volume_24h: float
    market_cap: float
    liquidity: float
    timestamp: datetime


class PriceHistory(BaseModel):
    token_address: str
    chain: ChainType
    prices: list[float]
    timestamps: list[datetime]
    interval: str  # 1m, 5m, 1h, 1d


# 风险管理相关模式
class RiskMetrics(BaseModel):
    max_position_size: float
    current_exposure: float
    daily_pnl: float
    max_daily_loss: float
    risk_score: float
    warnings: list[str]


# 系统状态相关模式
class ServiceStatus(BaseModel):
    name: str
    status: str  # running, stopped, error
    uptime: str
    last_update: datetime


class SystemStatus(BaseModel):
    overall_status: str
    services: list[ServiceStatus]
    memory_usage: float
    cpu_usage: float
    disk_usage: float


# 统计信息相关模式
class TradingStats(BaseModel):
    total_trades: int
    successful_trades: int
    failed_trades: int
    total_volume: float
    total_pnl: float
    win_rate: float
    average_profit: float
    average_loss: float


class PerformanceMetrics(BaseModel):
    period: str  # daily, weekly, monthly
    start_date: datetime
    end_date: datetime
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    volatility: float
    trades_count: int


# 配置相关模式
class TradingConfig(BaseModel):
    max_position_size: float
    max_daily_loss: float
    stop_loss_percentage: float
    take_profit_percentage: float
    max_slippage: float
    enabled_strategies: list[str]


class NotificationConfig(BaseModel):
    telegram_enabled: bool
    telegram_chat_id: Optional[str]
    email_enabled: bool
    email_address: Optional[str]
    webhook_enabled: bool
    webhook_url: Optional[str]
