"""
DEX接口服务
"""

from typing import Dict, Optional, List, Tuple
from decimal import Decimal
from web3 import Web3
from loguru import logger
import json

from app.core.config import settings, ChainConfig
from app.models.trading import ChainType, OrderType
from app.services.wallet_manager import WalletManager


class DEXInterface:
    """DEX交互接口"""
    
    def __init__(self):
        self.wallet_manager = WalletManager()
        
        # Uniswap V2 Router ABI (简化版)
        self.uniswap_v2_abi = [
            {
                "inputs": [
                    {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
                    {"internalType": "uint256", "name": "amountOutMin", "type": "uint256"},
                    {"internalType": "address[]", "name": "path", "type": "address[]"},
                    {"internalType": "address", "name": "to", "type": "address"},
                    {"internalType": "uint256", "name": "deadline", "type": "uint256"}
                ],
                "name": "swapExactTokensForTokens",
                "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}],
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "inputs": [
                    {"internalType": "uint256", "name": "amountOutMin", "type": "uint256"},
                    {"internalType": "address[]", "name": "path", "type": "address[]"},
                    {"internalType": "address", "name": "to", "type": "address"},
                    {"internalType": "uint256", "name": "deadline", "type": "uint256"}
                ],
                "name": "swapExactETHForTokens",
                "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}],
                "stateMutability": "payable",
                "type": "function"
            },
            {
                "inputs": [
                    {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
                    {"internalType": "uint256", "name": "amountOutMin", "type": "uint256"},
                    {"internalType": "address[]", "name": "path", "type": "address[]"},
                    {"internalType": "address", "name": "to", "type": "address"},
                    {"internalType": "uint256", "name": "deadline", "type": "uint256"}
                ],
                "name": "swapExactTokensForETH",
                "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}],
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "inputs": [
                    {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
                    {"internalType": "address[]", "name": "path", "type": "address[]"}
                ],
                "name": "getAmountsOut",
                "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}],
                "stateMutability": "view",
                "type": "function"
            }
        ]
        
        # ERC20 ABI (简化版)
        self.erc20_abi = [
            {
                "inputs": [
                    {"internalType": "address", "name": "spender", "type": "address"},
                    {"internalType": "uint256", "name": "amount", "type": "uint256"}
                ],
                "name": "approve",
                "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "inputs": [
                    {"internalType": "address", "name": "owner", "type": "address"},
                    {"internalType": "address", "name": "spender", "type": "address"}
                ],
                "name": "allowance",
                "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
                "stateMutability": "view",
                "type": "function"
            },
            {
                "inputs": [],
                "name": "decimals",
                "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}],
                "stateMutability": "view",
                "type": "function"
            }
        ]
    
    async def execute_trade(
        self,
        chain: ChainType,
        token_address: str,
        order_type: OrderType,
        amount: str,
        price: float,
        slippage: float = None
    ) -> Optional[str]:
        """执行交易"""
        try:
            if slippage is None:
                slippage = settings.MAX_SLIPPAGE
            
            if chain in [ChainType.ETHEREUM, ChainType.BSC]:
                return await self._execute_evm_trade(
                    chain, token_address, order_type, amount, price, slippage
                )
            elif chain == ChainType.SOLANA:
                return await self._execute_solana_trade(
                    token_address, order_type, amount, price, slippage
                )
            else:
                raise ValueError(f"不支持的链: {chain}")
                
        except Exception as e:
            logger.error(f"执行交易失败: {e}")
            return None
    
    async def _execute_evm_trade(
        self,
        chain: ChainType,
        token_address: str,
        order_type: OrderType,
        amount: str,
        price: float,
        slippage: float
    ) -> Optional[str]:
        """执行EVM链交易"""
        try:
            # 获取Web3连接
            if chain == ChainType.ETHEREUM:
                web3 = Web3(Web3.HTTPProvider(ChainConfig.ETHEREUM["rpc_url"]))
                router_address = ChainConfig.ETHEREUM["dex_routers"]["uniswap_v2"]
                weth_address = "******************************************"
            elif chain == ChainType.BSC:
                web3 = Web3(Web3.HTTPProvider(ChainConfig.BSC["rpc_url"]))
                router_address = ChainConfig.BSC["dex_routers"]["pancakeswap_v2"]
                weth_address = "******************************************"  # WBNB
            else:
                raise ValueError(f"不支持的EVM链: {chain}")
            
            # 获取钱包地址
            wallet_address = self.wallet_manager.get_wallet_address(chain)
            if not wallet_address:
                raise ValueError("钱包未初始化")
            
            # 创建路由器合约实例
            router_contract = web3.eth.contract(
                address=Web3.to_checksum_address(router_address),
                abi=self.uniswap_v2_abi
            )
            
            # 准备交易参数
            deadline = web3.eth.get_block('latest')['timestamp'] + 1200  # 20分钟后过期
            amount_wei = int(Decimal(amount) * Decimal(10**18))  # 假设18位小数
            
            if order_type == OrderType.BUY:
                # 买入代币 (ETH/BNB -> Token)
                path = [Web3.to_checksum_address(weth_address), Web3.to_checksum_address(token_address)]
                
                # 获取预期输出
                amounts_out = router_contract.functions.getAmountsOut(amount_wei, path).call()
                amount_out_min = int(amounts_out[-1] * (1 - slippage))
                
                # 构建交易
                transaction = router_contract.functions.swapExactETHForTokens(
                    amount_out_min,
                    path,
                    wallet_address,
                    deadline
                ).build_transaction({
                    'from': wallet_address,
                    'value': amount_wei,
                    'gasPrice': web3.eth.gas_price,
                    'nonce': web3.eth.get_transaction_count(wallet_address)
                })
                
            else:  # SELL
                # 卖出代币 (Token -> ETH/BNB)
                path = [Web3.to_checksum_address(token_address), Web3.to_checksum_address(weth_address)]
                
                # 检查并批准代币
                await self._approve_token_if_needed(
                    web3, token_address, router_address, amount_wei, wallet_address
                )
                
                # 获取预期输出
                amounts_out = router_contract.functions.getAmountsOut(amount_wei, path).call()
                amount_out_min = int(amounts_out[-1] * (1 - slippage))
                
                # 构建交易
                transaction = router_contract.functions.swapExactTokensForETH(
                    amount_wei,
                    amount_out_min,
                    path,
                    wallet_address,
                    deadline
                ).build_transaction({
                    'from': wallet_address,
                    'gasPrice': web3.eth.gas_price,
                    'nonce': web3.eth.get_transaction_count(wallet_address)
                })
            
            # 估算Gas
            gas_estimate = web3.eth.estimate_gas(transaction)
            transaction['gas'] = int(gas_estimate * 1.2)  # 添加20%安全边际
            
            # 签名并发送交易
            signed_txn = await self.wallet_manager.sign_transaction(chain, transaction)
            tx_hash = await self.wallet_manager.send_transaction(chain, signed_txn)
            
            logger.info(f"EVM交易已发送: {tx_hash}")
            return tx_hash
            
        except Exception as e:
            logger.error(f"EVM交易执行失败: {e}")
            return None
    
    async def _approve_token_if_needed(
        self,
        web3: Web3,
        token_address: str,
        spender_address: str,
        amount: int,
        wallet_address: str
    ):
        """检查并批准代币授权"""
        try:
            token_contract = web3.eth.contract(
                address=Web3.to_checksum_address(token_address),
                abi=self.erc20_abi
            )
            
            # 检查当前授权额度
            allowance = token_contract.functions.allowance(
                wallet_address, 
                spender_address
            ).call()
            
            if allowance < amount:
                # 需要批准
                approve_amount = 2**256 - 1  # 最大授权
                
                approve_txn = token_contract.functions.approve(
                    spender_address,
                    approve_amount
                ).build_transaction({
                    'from': wallet_address,
                    'gasPrice': web3.eth.gas_price,
                    'nonce': web3.eth.get_transaction_count(wallet_address)
                })
                
                # 估算Gas
                gas_estimate = web3.eth.estimate_gas(approve_txn)
                approve_txn['gas'] = int(gas_estimate * 1.2)
                
                # 签名并发送批准交易
                signed_approve = await self.wallet_manager.sign_transaction(
                    ChainType.ETHEREUM if "ethereum" in web3.provider.endpoint_uri else ChainType.BSC,
                    approve_txn
                )
                approve_hash = await self.wallet_manager.send_transaction(
                    ChainType.ETHEREUM if "ethereum" in web3.provider.endpoint_uri else ChainType.BSC,
                    signed_approve
                )
                
                logger.info(f"代币授权交易: {approve_hash}")
                
                # 等待确认
                receipt = web3.eth.wait_for_transaction_receipt(approve_hash)
                if receipt.status != 1:
                    raise Exception("代币授权失败")
                
        except Exception as e:
            logger.error(f"代币授权失败: {e}")
            raise
    
    async def _execute_solana_trade(
        self,
        token_address: str,
        order_type: OrderType,
        amount: str,
        price: float,
        slippage: float
    ) -> Optional[str]:
        """执行Solana交易"""
        try:
            # 这里需要实现Solana DEX交易逻辑
            # 可以使用Jupiter、Raydium等协议
            logger.info(f"执行Solana交易: {order_type.value} {amount} {token_address}")
            
            # 占位符实现
            return "solana_tx_hash"
            
        except Exception as e:
            logger.error(f"Solana交易执行失败: {e}")
            return None
    
    async def get_token_price(self, chain: ChainType, token_address: str) -> Optional[float]:
        """获取代币价格"""
        try:
            if chain in [ChainType.ETHEREUM, ChainType.BSC]:
                return await self._get_evm_token_price(chain, token_address)
            elif chain == ChainType.SOLANA:
                return await self._get_solana_token_price(token_address)
            else:
                raise ValueError(f"不支持的链: {chain}")
                
        except Exception as e:
            logger.error(f"获取代币价格失败: {e}")
            return None
    
    async def _get_evm_token_price(self, chain: ChainType, token_address: str) -> Optional[float]:
        """获取EVM链代币价格"""
        try:
            # 通过DEX获取价格
            if chain == ChainType.ETHEREUM:
                web3 = Web3(Web3.HTTPProvider(ChainConfig.ETHEREUM["rpc_url"]))
                router_address = ChainConfig.ETHEREUM["dex_routers"]["uniswap_v2"]
                weth_address = "******************************************"
            else:  # BSC
                web3 = Web3(Web3.HTTPProvider(ChainConfig.BSC["rpc_url"]))
                router_address = ChainConfig.BSC["dex_routers"]["pancakeswap_v2"]
                weth_address = "******************************************"
            
            router_contract = web3.eth.contract(
                address=Web3.to_checksum_address(router_address),
                abi=self.uniswap_v2_abi
            )
            
            # 获取1个代币能换多少ETH/BNB
            amount_in = 10**18  # 1个代币 (假设18位小数)
            path = [Web3.to_checksum_address(token_address), Web3.to_checksum_address(weth_address)]
            
            amounts_out = router_contract.functions.getAmountsOut(amount_in, path).call()
            eth_amount = amounts_out[-1] / 10**18
            
            # 这里需要ETH/BNB的USD价格来计算最终价格
            # 简化处理，假设ETH=$2000, BNB=$300
            eth_price_usd = 2000 if chain == ChainType.ETHEREUM else 300
            token_price_usd = eth_amount * eth_price_usd
            
            return token_price_usd
            
        except Exception as e:
            logger.error(f"获取EVM代币价格失败: {e}")
            return None
    
    async def _get_solana_token_price(self, token_address: str) -> Optional[float]:
        """获取Solana代币价格"""
        try:
            # 这里需要实现Solana价格查询
            # 可以使用Jupiter API或其他价格源
            return None
            
        except Exception as e:
            logger.error(f"获取Solana代币价格失败: {e}")
            return None
    
    async def get_liquidity_info(self, chain: ChainType, token_address: str) -> Dict:
        """获取流动性信息"""
        try:
            # 实现流动性信息查询
            return {
                "liquidity_usd": 0.0,
                "volume_24h": 0.0,
                "price_impact": 0.0
            }
            
        except Exception as e:
            logger.error(f"获取流动性信息失败: {e}")
            return {}
