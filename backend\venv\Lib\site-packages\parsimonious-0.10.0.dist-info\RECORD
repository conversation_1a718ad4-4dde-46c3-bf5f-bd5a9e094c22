parsimonious-0.10.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
parsimonious-0.10.0.dist-info/LICENSE,sha256=CfHIyelBrz5YTVlkHqm4fYPAyw_QB-te85Gn4mQ8GkY,1053
parsimonious-0.10.0.dist-info/METADATA,sha256=BlaX6moSCEMsK-FSg0Zcevoy9x5R___T4_yzUhK552A,25800
parsimonious-0.10.0.dist-info/RECORD,,
parsimonious-0.10.0.dist-info/WHEEL,sha256=G16H4A3IeoQmnOrYV4ueZGKSjhipXx8zc8nu9FGlvMA,92
parsimonious-0.10.0.dist-info/top_level.txt,sha256=Z9b3Lv-b0kulA9L4ccUzJ8Wuxmuhmk1hrzEkLx2Zegg,13
parsimonious/__init__.py,sha256=SiJTjNnxnd29qHBtZaF7IMveMPOGbFj4KrPEpMgVx78,421
parsimonious/__pycache__/__init__.cpython-313.pyc,,
parsimonious/__pycache__/adhoc_expression.cpython-313.pyc,,
parsimonious/__pycache__/exceptions.cpython-313.pyc,,
parsimonious/__pycache__/expressions.cpython-313.pyc,,
parsimonious/__pycache__/grammar.cpython-313.pyc,,
parsimonious/__pycache__/nodes.cpython-313.pyc,,
parsimonious/__pycache__/utils.cpython-313.pyc,,
parsimonious/adhoc_expression.py,sha256=E8RiBs8fYzz2XbEtGswq77JAyfKTfxLyMX0C81ZUgxc,2828
parsimonious/exceptions.py,sha256=T5hDYdnwhKOrdicsgRQ30eloXZOFata4BhIAsU5QadI,4360
parsimonious/expressions.py,sha256=btQz3TpPSeVUk-abk-kBk2LhH7KoGyPpblINJXcNi0c,16790
parsimonious/grammar.py,sha256=jaIcE8whDhQ7UwIQgEGjJM6FKMvwbQCuz_B2--MDg1Y,20342
parsimonious/nodes.py,sha256=WbJWxwiKdRKwLBOgJdYU2sp1zhDT1pyzO3dNnaIQFro,13141
parsimonious/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
parsimonious/tests/__pycache__/__init__.cpython-313.pyc,,
parsimonious/tests/__pycache__/benchmarks.cpython-313.pyc,,
parsimonious/tests/__pycache__/test_benchmarks.cpython-313.pyc,,
parsimonious/tests/__pycache__/test_expressions.cpython-313.pyc,,
parsimonious/tests/__pycache__/test_grammar.cpython-313.pyc,,
parsimonious/tests/__pycache__/test_nodes.cpython-313.pyc,,
parsimonious/tests/benchmarks.py,sha256=jSxggW-_qlgtrI5iHsxQwoEPK0evHQFuZntY2J9iKt4,3188
parsimonious/tests/test_benchmarks.py,sha256=S97iRrad06ufx_2dG7Ms5PFah-EJL6IWoYDJ8ycnB_I,1776
parsimonious/tests/test_expressions.py,sha256=5fPsWqUpDVNKzizp6KWw-mkeAmJLLks9VUiOAUza91g,13988
parsimonious/tests/test_grammar.py,sha256=2JNxvJ61NJjdAn9IhRbLPoL4b381RjtPo0KPmQjwE8M,25994
parsimonious/tests/test_nodes.py,sha256=a8MhkEXuX7cnkjf77yTHdow_HlC8oqtiChmFwvK9mpY,6513
parsimonious/utils.py,sha256=C0wgkYk4JL7HKxDtoGN6l4Pu4I78YPV1hG-t-USZlLY,1150
