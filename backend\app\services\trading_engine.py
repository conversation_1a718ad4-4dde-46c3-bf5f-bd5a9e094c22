"""
交易引擎服务
"""

import asyncio
import uuid
from typing import Dict, List, Optional
from datetime import datetime
from decimal import Decimal
from web3 import Web3
from eth_account import Account
from loguru import logger

from app.core.config import settings, ChainConfig
from app.models.trading import (
    TradingOrder, OrderType, OrderStatus, ChainType,
    TradingSignal, Portfolio
)
from app.models.base import SessionLocal
# 避免循环导入，在需要时动态导入
# from app.services.strategy_manager import StrategyManager
# from app.services.wallet_manager import WalletManager
# from app.services.dex_interface import DEXInterface


class TradingEngine:
    """交易引擎"""

    def __init__(self, strategy_manager=None):
        self.is_running = False
        self.strategy_manager = strategy_manager
        self.wallet_manager = None  # 延迟初始化
        self.dex_interface = None   # 延迟初始化

        # 活跃订单
        self.active_orders: Dict[str, TradingOrder] = {}

        # 风险管理参数
        self.max_position_size = settings.MAX_POSITION_SIZE
        self.max_daily_loss = settings.MAX_DAILY_LOSS
        self.stop_loss_percentage = settings.STOP_LOSS_PERCENTAGE
        self.take_profit_percentage = settings.TAKE_PROFIT_PERCENTAGE

        # 执行任务
        self.execution_tasks = []

    async def start(self):
        """启动交易引擎"""
        if self.is_running:
            logger.warning("交易引擎已在运行")
            return

        logger.info("启动交易引擎...")
        self.is_running = True

        # 动态导入并初始化依赖
        try:
            from app.services.wallet_manager import WalletManager
            from app.services.dex_interface import DEXInterface

            self.wallet_manager = WalletManager()
            self.dex_interface = DEXInterface()

            # 初始化钱包管理器
            await self.wallet_manager.initialize()
        except ImportError as e:
            logger.warning(f"无法导入依赖模块: {e}")
            # 继续运行，但功能受限

        # 加载活跃订单
        await self._load_active_orders()

        # 启动执行任务
        self.execution_tasks = [
            asyncio.create_task(self._process_trading_signals()),
            asyncio.create_task(self._monitor_orders()),
            asyncio.create_task(self._risk_management()),
            asyncio.create_task(self._update_portfolio())
        ]

        logger.info("交易引擎启动成功")

    async def stop(self):
        """停止交易引擎"""
        if not self.is_running:
            return

        logger.info("停止交易引擎...")
        self.is_running = False

        # 取消所有执行任务
        for task in self.execution_tasks:
            task.cancel()

        await asyncio.gather(*self.execution_tasks, return_exceptions=True)

        logger.info("交易引擎已停止")

    async def create_order(
        self,
        chain: ChainType,
        token_address: str,
        token_symbol: str,
        order_type: OrderType,
        amount: str,
        price: float,
        strategy_name: str = "manual"
    ) -> str:
        """创建交易订单"""

        # 风险检查
        if not await self._risk_check(chain, token_address, amount, order_type):
            raise ValueError("订单未通过风险检查")

        # 创建订单ID
        order_id = str(uuid.uuid4())

        # 创建订单对象
        order = TradingOrder(
            order_id=order_id,
            strategy_name=strategy_name,
            chain=chain,
            token_address=token_address,
            token_symbol=token_symbol,
            order_type=order_type,
            amount=amount,
            price=price,
            status=OrderStatus.PENDING
        )

        # 保存到数据库
        db = SessionLocal()
        try:
            db.add(order)
            db.commit()

            # 添加到活跃订单
            self.active_orders[order_id] = order

            logger.info(f"创建订单: {order_id} - {order_type.value} {amount} {token_symbol}")

            return order_id

        finally:
            db.close()

    async def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        if order_id not in self.active_orders:
            return False

        order = self.active_orders[order_id]

        # 如果订单已在执行中，尝试取消链上交易
        if order.tx_hash:
            # 这里可以实现取消链上交易的逻辑
            pass

        # 更新订单状态
        order.status = OrderStatus.CANCELLED
        order.cancelled_at = datetime.utcnow()

        # 从活跃订单中移除
        del self.active_orders[order_id]

        # 更新数据库
        db = SessionLocal()
        try:
            db.merge(order)
            db.commit()
        finally:
            db.close()

        logger.info(f"订单已取消: {order_id}")
        return True

    async def _load_active_orders(self):
        """加载活跃订单"""
        db = SessionLocal()
        try:
            orders = db.query(TradingOrder).filter(
                TradingOrder.status.in_([OrderStatus.PENDING, OrderStatus.PARTIAL_FILLED])
            ).all()

            for order in orders:
                self.active_orders[order.order_id] = order

            logger.info(f"加载了 {len(orders)} 个活跃订单")

        finally:
            db.close()

    async def _process_trading_signals(self):
        """处理交易信号"""
        while self.is_running:
            try:
                db = SessionLocal()
                try:
                    # 获取未执行的交易信号
                    signals = db.query(TradingSignal).filter(
                        TradingSignal.is_executed == False
                    ).all()

                    for signal in signals:
                        await self._execute_signal(signal)

                finally:
                    db.close()

            except Exception as e:
                logger.error(f"处理交易信号失败: {e}")

            await asyncio.sleep(5)  # 每5秒检查一次

    async def _execute_signal(self, signal: TradingSignal):
        """执行交易信号"""
        try:
            # 根据信号类型创建订单
            if signal.signal_type == "buy":
                order_type = OrderType.BUY
            elif signal.signal_type == "sell":
                order_type = OrderType.SELL
            else:
                return  # 忽略hold信号

            # 计算交易数量
            amount = await self._calculate_position_size(
                signal.chain,
                signal.token_address,
                signal.current_price,
                signal.confidence
            )

            if amount > 0:
                order_id = await self.create_order(
                    chain=signal.chain,
                    token_address=signal.token_address,
                    token_symbol=signal.token_symbol,
                    order_type=order_type,
                    amount=str(amount),
                    price=signal.current_price,
                    strategy_name=signal.strategy_name
                )

                # 更新信号状态
                signal.is_executed = True
                signal.executed_at = datetime.utcnow()
                signal.order_id = order_id

                db = SessionLocal()
                try:
                    db.merge(signal)
                    db.commit()
                finally:
                    db.close()

        except Exception as e:
            logger.error(f"执行交易信号失败: {e}")

    async def _monitor_orders(self):
        """监控订单执行"""
        while self.is_running:
            try:
                for order_id, order in list(self.active_orders.items()):
                    await self._check_order_status(order)

            except Exception as e:
                logger.error(f"监控订单失败: {e}")

            await asyncio.sleep(10)  # 每10秒检查一次

    async def _check_order_status(self, order: TradingOrder):
        """检查订单状态"""
        try:
            if order.status == OrderStatus.PENDING:
                # 尝试执行订单
                await self._execute_order(order)
            elif order.tx_hash:
                # 检查链上交易状态
                await self._check_transaction_status(order)

        except Exception as e:
            logger.error(f"检查订单状态失败: {e}")

    async def _execute_order(self, order: TradingOrder):
        """执行订单"""
        try:
            # 检查余额
            if not await self._check_balance(order):
                logger.warning(f"余额不足，无法执行订单: {order.order_id}")
                return

            # 执行DEX交易
            tx_hash = await self.dex_interface.execute_trade(
                chain=order.chain,
                token_address=order.token_address,
                order_type=order.order_type,
                amount=order.amount,
                price=order.price
            )

            if tx_hash:
                order.tx_hash = tx_hash
                order.executed_at = datetime.utcnow()

                # 更新数据库
                db = SessionLocal()
                try:
                    db.merge(order)
                    db.commit()
                finally:
                    db.close()

                logger.info(f"订单已提交到链上: {order.order_id} - {tx_hash}")

        except Exception as e:
            logger.error(f"执行订单失败: {e}")
            # 标记订单为失败
            order.status = OrderStatus.FAILED

    async def _check_transaction_status(self, order: TradingOrder):
        """检查交易状态"""
        try:
            # 根据链类型检查交易状态
            if order.chain in [ChainType.ETHEREUM, ChainType.BSC]:
                web3 = Web3(Web3.HTTPProvider(
                    ChainConfig.ETHEREUM["rpc_url"] if order.chain == ChainType.ETHEREUM
                    else ChainConfig.BSC["rpc_url"]
                ))

                receipt = web3.eth.get_transaction_receipt(order.tx_hash)

                if receipt.status == 1:  # 成功
                    order.status = OrderStatus.FILLED
                    order.filled_amount = order.amount

                    # 从活跃订单中移除
                    if order.order_id in self.active_orders:
                        del self.active_orders[order.order_id]

                    logger.info(f"订单执行成功: {order.order_id}")

                else:  # 失败
                    order.status = OrderStatus.FAILED

                    if order.order_id in self.active_orders:
                        del self.active_orders[order.order_id]

                    logger.warning(f"订单执行失败: {order.order_id}")

                # 更新数据库
                db = SessionLocal()
                try:
                    db.merge(order)
                    db.commit()
                finally:
                    db.close()

        except Exception as e:
            logger.error(f"检查交易状态失败: {e}")

    async def _risk_management(self):
        """风险管理"""
        while self.is_running:
            try:
                # 检查日损失限制
                await self._check_daily_loss_limit()

                # 检查仓位限制
                await self._check_position_limits()

                # 检查止损止盈
                await self._check_stop_loss_take_profit()

            except Exception as e:
                logger.error(f"风险管理检查失败: {e}")

            await asyncio.sleep(30)  # 每30秒检查一次

    async def _update_portfolio(self):
        """更新投资组合"""
        while self.is_running:
            try:
                # 更新投资组合价值
                await self._calculate_portfolio_value()

            except Exception as e:
                logger.error(f"更新投资组合失败: {e}")

            await asyncio.sleep(60)  # 每分钟更新一次

    async def _risk_check(self, chain: ChainType, token_address: str, amount: str, order_type: OrderType) -> bool:
        """风险检查"""
        # 实现风险检查逻辑
        return True

    async def _calculate_position_size(self, chain: ChainType, token_address: str, price: float, confidence: float) -> float:
        """计算仓位大小"""
        # 基于置信度和风险参数计算仓位
        base_position = self.max_position_size * confidence
        return base_position

    async def _check_balance(self, order: TradingOrder) -> bool:
        """检查余额"""
        # 实现余额检查逻辑
        return True

    async def _check_daily_loss_limit(self):
        """检查日损失限制"""
        pass

    async def _check_position_limits(self):
        """检查仓位限制"""
        pass

    async def _check_stop_loss_take_profit(self):
        """检查止损止盈"""
        pass

    async def _calculate_portfolio_value(self):
        """计算投资组合价值"""
        pass
