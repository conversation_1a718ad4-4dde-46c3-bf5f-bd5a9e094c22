# AI Crypto Trading Agent 环境变量配置示例
# 复制此文件为 .env 并填入您的实际配置

# ==================== 钱包配置 ====================
# 警告：请妥善保管私钥，建议使用硬件钱包或多重签名
WALLET_PRIVATE_KEY=your_private_key_here
WALLET_MNEMONIC=your_mnemonic_phrase_here

# ==================== 区块链RPC配置 ====================
# Ethereum
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/YOUR_PROJECT_ID
ETHEREUM_TESTNET_RPC=https://goerli.infura.io/v3/YOUR_PROJECT_ID

# Binance Smart Chain
BSC_RPC_URL=https://bsc-dataseed.binance.org/
BSC_TESTNET_RPC=https://data-seed-prebsc-1-s1.binance.org:8545/

# Solana
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_TESTNET_RPC=https://api.testnet.solana.com

# ==================== API密钥配置 ====================
# CoinGecko API (价格数据)
COINGECKO_API_KEY=your_coingecko_api_key

# DexScreener API (DEX数据)
DEXSCREENER_API_KEY=your_dexscreener_api_key

# Moralis API (区块链数据)
MORALIS_API_KEY=your_moralis_api_key

# Alchemy API (以太坊数据)
ALCHEMY_API_KEY=your_alchemy_api_key

# ==================== 通知配置 ====================
# Telegram Bot
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# 邮件通知
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_email_password
EMAIL_TO=<EMAIL>

# Webhook通知
WEBHOOK_URL=https://your-webhook-url.com/notify
WEBHOOK_SECRET=your_webhook_secret

# ==================== 数据库配置 ====================
# SQLite (默认)
DATABASE_URL=sqlite:///./data/trading_agent.db

# PostgreSQL (生产环境推荐)
# DATABASE_URL=postgresql://username:password@localhost:5432/trading_agent

# ==================== Redis配置 ====================
REDIS_URL=redis://localhost:6379/0

# ==================== 安全配置 ====================
# 应用密钥 (请使用强密码)
SECRET_KEY=your-very-secure-secret-key-change-this-in-production

# 数据加密密钥
ENCRYPTION_KEY=your-encryption-key-32-characters

# JWT密钥
JWT_SECRET_KEY=your-jwt-secret-key

# ==================== 应用配置 ====================
# 运行模式
DEBUG=false
ENVIRONMENT=production

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 允许的主机
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# CORS配置
CORS_ORIGINS=http://localhost:3000,https://your-frontend-domain.com

# ==================== 交易配置 ====================
# 测试模式 (true=使用测试网络)
TEST_MODE=true

# 最大滑点
MAX_SLIPPAGE=0.05

# Gas配置
MAX_GAS_PRICE=100
DEFAULT_GAS_LIMIT=200000

# ==================== 风险管理配置 ====================
# 最大仓位大小 (占总资产的比例)
MAX_POSITION_SIZE=0.1

# 最大日损失 (占总资产的比例)
MAX_DAILY_LOSS=0.05

# 止损百分比
STOP_LOSS_PERCENTAGE=0.1

# 止盈百分比
TAKE_PROFIT_PERCENTAGE=0.2

# ==================== 监控配置 ====================
# 监控间隔 (秒)
MONITOR_INTERVAL=10
PRICE_CHECK_INTERVAL=5
PORTFOLIO_UPDATE_INTERVAL=60

# ==================== 日志配置 ====================
# 日志级别
LOG_LEVEL=INFO

# 日志文件路径
LOG_FILE=logs/trading_agent.log

# ==================== Web自动化配置 ====================
# Chrome驱动路径 (可选，自动下载)
CHROME_DRIVER_PATH=

# 无头模式
HEADLESS_BROWSER=true

# 代理配置 (可选)
PROXY_URL=
PROXY_USERNAME=
PROXY_PASSWORD=

# ==================== 开发配置 ====================
# 开发模式
DEV_MODE=false

# 热重载
HOT_RELOAD=false

# 调试端口
DEBUG_PORT=5678
