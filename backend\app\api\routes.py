"""
API路由
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from sqlalchemy.orm import Session

from app.models.base import get_db
from app.models.trading import Token, TradingOrder, TradingSignal, Portfolio, ChainType, OrderType
from app.api.schemas import (
    TokenCreate, TokenResponse, OrderCreate, OrderResponse, 
    SignalResponse, PortfolioResponse, StrategyConfig
)

# 创建路由器
api_router = APIRouter()

# 代币相关路由
@api_router.get("/tokens", response_model=List[TokenResponse])
async def get_tokens(
    chain: Optional[ChainType] = None,
    is_active: Optional[bool] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """获取代币列表"""
    query = db.query(Token)
    
    if chain:
        query = query.filter(Token.chain == chain)
    if is_active is not None:
        query = query.filter(Token.is_active == is_active)
    
    tokens = query.offset(skip).limit(limit).all()
    return tokens


@api_router.post("/tokens", response_model=TokenResponse)
async def create_token(token: TokenCreate, db: Session = Depends(get_db)):
    """添加代币"""
    # 检查代币是否已存在
    existing_token = db.query(Token).filter(
        Token.address == token.address,
        Token.chain == token.chain
    ).first()
    
    if existing_token:
        raise HTTPException(status_code=400, detail="代币已存在")
    
    db_token = Token(**token.dict())
    db.add(db_token)
    db.commit()
    db.refresh(db_token)
    
    return db_token


@api_router.get("/tokens/{token_id}", response_model=TokenResponse)
async def get_token(token_id: int, db: Session = Depends(get_db)):
    """获取代币详情"""
    token = db.query(Token).filter(Token.id == token_id).first()
    if not token:
        raise HTTPException(status_code=404, detail="代币不存在")
    return token


@api_router.put("/tokens/{token_id}/toggle")
async def toggle_token_monitoring(token_id: int, db: Session = Depends(get_db)):
    """切换代币监控状态"""
    token = db.query(Token).filter(Token.id == token_id).first()
    if not token:
        raise HTTPException(status_code=404, detail="代币不存在")
    
    token.is_active = not token.is_active
    db.commit()
    
    return {"message": f"代币监控状态已{'启用' if token.is_active else '禁用'}"}


# 交易订单相关路由
@api_router.get("/orders", response_model=List[OrderResponse])
async def get_orders(
    status: Optional[str] = None,
    strategy: Optional[str] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """获取交易订单列表"""
    query = db.query(TradingOrder)
    
    if status:
        query = query.filter(TradingOrder.status == status)
    if strategy:
        query = query.filter(TradingOrder.strategy_name == strategy)
    
    orders = query.order_by(TradingOrder.created_at.desc()).offset(skip).limit(limit).all()
    return orders


@api_router.post("/orders", response_model=OrderResponse)
async def create_order(order: OrderCreate, db: Session = Depends(get_db)):
    """创建交易订单"""
    # 这里应该调用交易引擎来创建订单
    # 为了演示，直接创建数据库记录
    
    import uuid
    from datetime import datetime
    from app.models.trading import OrderStatus
    
    db_order = TradingOrder(
        order_id=str(uuid.uuid4()),
        strategy_name=order.strategy_name,
        chain=order.chain,
        token_address=order.token_address,
        token_symbol=order.token_symbol,
        order_type=order.order_type,
        amount=order.amount,
        price=order.price,
        status=OrderStatus.PENDING
    )
    
    db.add(db_order)
    db.commit()
    db.refresh(db_order)
    
    return db_order


@api_router.get("/orders/{order_id}", response_model=OrderResponse)
async def get_order(order_id: str, db: Session = Depends(get_db)):
    """获取订单详情"""
    order = db.query(TradingOrder).filter(TradingOrder.order_id == order_id).first()
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    return order


@api_router.post("/orders/{order_id}/cancel")
async def cancel_order(order_id: str, db: Session = Depends(get_db)):
    """取消订单"""
    order = db.query(TradingOrder).filter(TradingOrder.order_id == order_id).first()
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    # 这里应该调用交易引擎来取消订单
    from app.models.trading import OrderStatus
    from datetime import datetime
    
    order.status = OrderStatus.CANCELLED
    order.cancelled_at = datetime.utcnow()
    db.commit()
    
    return {"message": "订单已取消"}


# 交易信号相关路由
@api_router.get("/signals", response_model=List[SignalResponse])
async def get_signals(
    strategy: Optional[str] = None,
    signal_type: Optional[str] = None,
    is_executed: Optional[bool] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """获取交易信号列表"""
    query = db.query(TradingSignal)
    
    if strategy:
        query = query.filter(TradingSignal.strategy_name == strategy)
    if signal_type:
        query = query.filter(TradingSignal.signal_type == signal_type)
    if is_executed is not None:
        query = query.filter(TradingSignal.is_executed == is_executed)
    
    signals = query.order_by(TradingSignal.created_at.desc()).offset(skip).limit(limit).all()
    return signals


@api_router.get("/signals/{signal_id}", response_model=SignalResponse)
async def get_signal(signal_id: str, db: Session = Depends(get_db)):
    """获取信号详情"""
    signal = db.query(TradingSignal).filter(TradingSignal.signal_id == signal_id).first()
    if not signal:
        raise HTTPException(status_code=404, detail="信号不存在")
    return signal


# 投资组合相关路由
@api_router.get("/portfolio", response_model=List[PortfolioResponse])
async def get_portfolio(
    chain: Optional[ChainType] = None,
    db: Session = Depends(get_db)
):
    """获取投资组合"""
    query = db.query(Portfolio)
    
    if chain:
        query = query.filter(Portfolio.chain == chain)
    
    portfolio = query.all()
    return portfolio


@api_router.get("/portfolio/summary")
async def get_portfolio_summary(db: Session = Depends(get_db)):
    """获取投资组合摘要"""
    portfolio = db.query(Portfolio).all()
    
    total_value = sum(p.current_value for p in portfolio)
    total_invested = sum(p.total_invested for p in portfolio)
    total_pnl = sum(p.unrealized_pnl + p.realized_pnl for p in portfolio)
    
    return {
        "total_value": total_value,
        "total_invested": total_invested,
        "total_pnl": total_pnl,
        "pnl_percentage": (total_pnl / total_invested * 100) if total_invested > 0 else 0,
        "positions_count": len(portfolio)
    }


# 策略相关路由
@api_router.get("/strategies")
async def get_strategies():
    """获取可用策略列表"""
    return {
        "strategies": [
            {
                "name": "momentum",
                "display_name": "动量策略",
                "description": "基于RSI和交易量的动量交易策略"
            },
            {
                "name": "mean_reversion",
                "display_name": "均值回归策略",
                "description": "基于布林带的均值回归策略"
            },
            {
                "name": "ml_strategy",
                "display_name": "机器学习策略",
                "description": "基于随机森林的机器学习策略"
            }
        ]
    }


@api_router.get("/strategies/{strategy_name}/config")
async def get_strategy_config(strategy_name: str):
    """获取策略配置"""
    # 这里应该从配置文件读取
    configs = {
        "momentum": {
            "rsi_period": 14,
            "rsi_oversold": 30,
            "rsi_overbought": 70,
            "volume_threshold": 1.5
        },
        "mean_reversion": {
            "bollinger_period": 20,
            "bollinger_std": 2,
            "min_deviation": 0.02
        },
        "ml_strategy": {
            "model_type": "random_forest",
            "n_estimators": 100,
            "confidence_threshold": 0.6
        }
    }
    
    if strategy_name not in configs:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    return configs[strategy_name]


@api_router.put("/strategies/{strategy_name}/config")
async def update_strategy_config(strategy_name: str, config: StrategyConfig):
    """更新策略配置"""
    # 这里应该更新配置文件
    return {"message": f"策略 {strategy_name} 配置已更新"}


# 系统状态相关路由
@api_router.get("/status")
async def get_system_status():
    """获取系统状态"""
    return {
        "status": "running",
        "services": {
            "blockchain_monitor": True,
            "trading_engine": True,
            "strategy_manager": True
        },
        "uptime": "1h 30m",
        "last_update": "2024-01-01T12:00:00Z"
    }


@api_router.get("/stats")
async def get_stats(db: Session = Depends(get_db)):
    """获取统计信息"""
    from sqlalchemy import func
    from datetime import datetime, timedelta
    
    # 今日统计
    today = datetime.utcnow().date()
    
    # 订单统计
    total_orders = db.query(TradingOrder).count()
    today_orders = db.query(TradingOrder).filter(
        func.date(TradingOrder.created_at) == today
    ).count()
    
    # 信号统计
    total_signals = db.query(TradingSignal).count()
    today_signals = db.query(TradingSignal).filter(
        func.date(TradingSignal.created_at) == today
    ).count()
    
    # 代币统计
    active_tokens = db.query(Token).filter(Token.is_active == True).count()
    total_tokens = db.query(Token).count()
    
    return {
        "orders": {
            "total": total_orders,
            "today": today_orders
        },
        "signals": {
            "total": total_signals,
            "today": today_signals
        },
        "tokens": {
            "active": active_tokens,
            "total": total_tokens
        }
    }
