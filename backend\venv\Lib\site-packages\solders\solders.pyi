from pathlib import Path
from typing import (
    Any,
    ClassVar,
    Dict,
    Final,
    List,
    Optional,
    Sequence,
    Set,
    Tuple,
    TypeVar,
    Union,
)

from jsonalias import Json

class Hash:
    LENGTH: ClassVar[int]
    def __init__(self, hash_bytes: bytes) -> None: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    @staticmethod
    def from_string(s: str) -> "Hash": ...
    @staticmethod
    def new_unique() -> "Hash": ...
    @staticmethod
    def default() -> "Hash": ...
    def __bytes__(self) -> bytes: ...
    def __richcmp__(self, other: "Hash", op: int) -> bool: ...
    @staticmethod
    def hash(val: bytes) -> "Hash": ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "Hash": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "Hash": ...

class ParseHashError(Exception): ...

class Pubkey:
    LENGTH: ClassVar[int]
    def __init__(self, pubkey_bytes: Union[bytes, Sequence[int]]) -> None: ...
    @staticmethod
    def new_unique() -> "Pubkey": ...
    @staticmethod
    def default() -> "Pubkey": ...
    @staticmethod
    def from_string(s: str) -> "Pubkey": ...
    @staticmethod
    def create_with_seed(
        base: "Pubkey", seed: str, program_id: "Pubkey"
    ) -> "Pubkey": ...
    @staticmethod
    def create_program_address(
        seeds: Sequence[bytes], program_id: "Pubkey"
    ) -> "Pubkey": ...
    @staticmethod
    def find_program_address(
        seeds: Sequence[bytes], program_id: "Pubkey"
    ) -> Tuple["Pubkey", int]: ...
    def is_on_curve(self) -> bool: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __richcmp__(self, other: "Pubkey", op: int) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "Pubkey": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "Pubkey": ...

class AccountMeta:
    def __init__(self, pubkey: Pubkey, is_signer: bool, is_writable: bool) -> None: ...
    @property
    def pubkey(self) -> Pubkey: ...
    @property
    def is_signer(self) -> bool: ...
    @property
    def is_writable(self) -> bool: ...
    def __repr__(self) -> str: ...
    def __str__(self) -> str: ...
    def __hash__(self) -> int: ...
    def __richcmp__(self, other: "AccountMeta", op: int) -> bool: ...
    def __bytes__(self) -> bytes: ...
    @staticmethod
    def from_bytes(data: bytes) -> "AccountMeta": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "AccountMeta": ...

class Instruction:
    def __init__(
        self, program_id: Pubkey, data: bytes, accounts: Sequence[AccountMeta]
    ) -> None: ...
    @property
    def program_id(self) -> Pubkey: ...
    @property
    def data(self) -> bytes: ...
    @property
    def accounts(self) -> List[AccountMeta]: ...
    @accounts.setter
    def accounts(self, accounts: List[AccountMeta]) -> None: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "Instruction", op: int) -> bool: ...
    def __bytes__(self) -> bytes: ...
    @staticmethod
    def from_bytes(data: bytes) -> "Instruction": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "Instruction": ...

class CompiledInstruction:
    def __init__(self, program_id_index: int, data: bytes, accounts: bytes) -> None: ...
    def program_id(self, program_ids: Sequence[Pubkey]) -> Pubkey: ...
    @property
    def program_id_index(self) -> int: ...
    @property
    def accounts(self) -> bytes: ...
    @accounts.setter
    def accounts(self, accounts: Union[bytes, Sequence[int]]) -> None: ...
    @property
    def data(self) -> bytes: ...
    def __repr__(self) -> str: ...
    def __str__(self) -> str: ...
    def __richcmp__(self, other: "CompiledInstruction", op: int) -> bool: ...
    def __bytes__(self) -> bytes: ...
    @staticmethod
    def from_bytes(data: bytes) -> "CompiledInstruction": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "CompiledInstruction": ...

SYSTEM_PROGRAM_ID: Final[Pubkey]

def create_account(params: Dict[str, Any]) -> Instruction: ...
def decode_create_account(instruction: Instruction) -> Dict[str, Any]: ...
def create_account_with_seed(params: Dict[str, Any]) -> Instruction: ...
def decode_create_account_with_seed(instruction: Instruction) -> Dict[str, Any]: ...
def assign(params: Dict[str, Any]) -> Instruction: ...
def decode_assign(instruction: Instruction) -> Dict[str, Any]: ...
def assign_with_seed(params: Dict[str, Any]) -> Instruction: ...
def decode_assign_with_seed(instruction: Instruction) -> Dict[str, Any]: ...
def transfer(params: Dict[str, Any]) -> Instruction: ...
def decode_transfer(instruction: Instruction) -> Dict[str, Any]: ...
def transfer_with_seed(params: Dict[str, Any]) -> Instruction: ...
def decode_transfer_with_seed(instruction: Instruction) -> Dict[str, Any]: ...
def allocate(params: Dict[str, Any]) -> Instruction: ...
def decode_allocate(instruction: Instruction) -> Dict[str, Any]: ...
def allocate_with_seed(params: Dict[str, Any]) -> Instruction: ...
def decode_allocate_with_seed(instruction: Instruction) -> Dict[str, Any]: ...
def transfer_many(
    from_pubkey: Pubkey,
    to_lamports: Sequence[Tuple[Pubkey, int]],
) -> List[Instruction]: ...
def create_nonce_account_with_seed(
    from_pubkey: Pubkey,
    nonce_pubkey: Pubkey,
    base: Pubkey,
    seed: str,
    authority: Pubkey,
    lamports: int,
) -> Tuple[Instruction, Instruction]: ...
def create_nonce_account(
    from_pubkey: Pubkey,
    nonce_pubkey: Pubkey,
    authority: Pubkey,
    lamports: int,
) -> Tuple[Instruction, Instruction]: ...
def initialize_nonce_account(params: Dict[str, Any]) -> Instruction: ...
def decode_initialize_nonce_account(instruction: Instruction) -> Dict[str, Any]: ...
def advance_nonce_account(params: Dict[str, Any]) -> Instruction: ...
def decode_advance_nonce_account(instruction: Instruction) -> Dict[str, Any]: ...
def withdraw_nonce_account(params: Dict[str, Any]) -> Instruction: ...
def decode_withdraw_nonce_account(instruction: Instruction) -> Dict[str, Any]: ...
def authorize_nonce_account(params: Dict[str, Any]) -> Instruction: ...
def decode_authorize_nonce_account(instruction: Instruction) -> Dict[str, Any]: ...
def close_lookup_table(params: Dict[str, Any]) -> Instruction: ...
def create_lookup_table(params: Dict[str, Any]) -> Tuple[Instruction, Pubkey]: ...
def create_lookup_table_signed(
    params: Dict[str, Any],
) -> Tuple[Instruction, Pubkey]: ...
def deactivate_lookup_table(params: Dict[str, Any]) -> Instruction: ...
def extend_lookup_table(params: Dict[str, Any]) -> Instruction: ...
def freeze_lookup_table(params: Dict[str, Any]) -> Instruction: ...

ADDRESS_LOOKUP_TABLE_ID: Final[Pubkey]
LOOKUP_TABLE_MAX_ADDRESSES: Final[int]
LOOKUP_TABLE_META_SIZE: Final[int]

class AddressLookupTableAccount:
    def __init__(
        self,
        key: Pubkey,
        addresses: Sequence[Pubkey],
    ) -> None: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "AddressLookupTableAccount", op: int) -> bool: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "AddressLookupTableAccount": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "AddressLookupTableAccount": ...
    @property
    def key(self) -> Pubkey: ...
    @property
    def addresses(self) -> List[Pubkey]: ...

class AddressLookupTable:
    def __init__(self, key: Pubkey, addresses: Sequence[Pubkey]) -> None: ...
    def get_active_addresses_len(
        self, current_slot: int, slot_hashes: SlotHashes
    ) -> int: ...
    def lookup(
        self,
        current_slot: int,
        indexes: Sequence[int],
        slot_hashes: SlotHashes,
    ) -> List[Pubkey]: ...
    @staticmethod
    def deserialize(data: bytes) -> AddressLookupTable: ...
    @property
    def meta(self) -> LookupTableMeta: ...
    @property
    def addresses(self) -> List[Pubkey]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "AddressLookupTable", op: int) -> bool: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "AddressLookupTable": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "AddressLookupTable": ...

# not really defined here
LookupTableStatusType = Union[LookupTableStatusFieldless, LookupTableStatusDeactivating]

class LookupTableMeta:
    def __init__(
        self,
        deactivation_slot: int = 18446744073709551615,  # u64::MAX
        last_extended_slot: int = 0,
        last_extended_slot_start_index: int = 0,
        authority: Optional[Pubkey] = None,
        padding: int = 0,
    ) -> None: ...
    def is_active(self, current_slot: int, slot_hashes: SlotHashes) -> bool: ...
    def status(
        self, current_slot: int, slot_hashes: SlotHashes
    ) -> LookupTableStatusType: ...
    @property
    def deactivation_slot(self) -> int: ...
    @property
    def last_extended_slot(self) -> int: ...
    @property
    def last_extended_slot_start_index(self) -> int: ...
    @property
    def authority(self) -> Optional[Pubkey]: ...
    @property
    def padding(self) -> int: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "LookupTableMeta", op: int) -> bool: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "LookupTableMeta": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "LookupTableMeta": ...

class LookupTableStatusFieldless:
    Activated: "LookupTableStatusFieldless"
    Deactivated: "LookupTableStatusFieldless"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...

class LookupTableStatusDeactivating:
    def __init__(self, remaining_slots: int) -> None: ...
    @property
    def remaining_slots(self) -> int: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "LookupTableStatusDeactivating", op: int) -> bool: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "LookupTableStatusDeactivating": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "LookupTableStatusDeactivating": ...

class SlotHashes:
    def __init__(self, slot_hashes: Sequence[Tuple[int, Hash]]) -> None: ...
    @property
    def slot_hashes(self) -> List[Tuple[int, Hash]]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "SlotHashes", op: int) -> bool: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "SlotHashes": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "SlotHashes": ...

def derive_lookup_table_address(
    authority_address: Pubkey, recent_block_slot: int
) -> Tuple[Pubkey, int]: ...

class MessageHeader:
    LENGTH: ClassVar[int]
    def __init__(
        self,
        num_required_signatures: int,
        num_readonly_signed_accounts: int,
        num_readonly_unsigned_accounts: int,
    ) -> None: ...
    @staticmethod
    def default() -> "MessageHeader": ...
    @property
    def num_required_signatures(self) -> int: ...
    @property
    def num_readonly_signed_accounts(self) -> int: ...
    @property
    def num_readonly_unsigned_accounts(self) -> int: ...
    def __repr__(self) -> str: ...
    def __str__(self) -> str: ...
    def __richcmp__(self, other: "MessageHeader", op: int) -> bool: ...
    def __bytes__(self) -> bytes: ...
    @staticmethod
    def from_bytes(data: bytes) -> "MessageHeader": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "MessageHeader": ...

class Message:
    def __init__(
        self,
        instructions: Sequence[Instruction],
        payer: Optional[Pubkey] = None,
    ) -> None: ...
    @property
    def header(self) -> MessageHeader: ...
    @property
    def account_keys(self) -> List[Pubkey]: ...
    @property
    def recent_blockhash(self) -> Hash: ...
    @property
    def instructions(self) -> List[CompiledInstruction]: ...
    @staticmethod
    def new_with_blockhash(
        instructions: Sequence[Instruction], payer: Optional[Pubkey], blockhash: Hash
    ) -> "Message": ...
    @staticmethod
    def new_with_nonce(
        instructions: Sequence[Instruction],
        payer: Optional[Pubkey],
        nonce_account_pubkey: Pubkey,
        nonce_authority_pubkey: Pubkey,
    ) -> "Message": ...
    @staticmethod
    def new_with_compiled_instructions(
        num_required_signatures: int,
        num_readonly_signed_accounts: int,
        num_readonly_unsigned_accounts: int,
        account_keys: Sequence[Pubkey],
        recent_blockhash: Hash,
        instructions: Sequence[CompiledInstruction],
    ) -> "Message": ...
    def hash(self) -> Hash: ...
    @staticmethod
    def hash_raw_message(message_bytes: bytes) -> Hash: ...
    def compile_instruction(self, ix: Instruction) -> CompiledInstruction: ...
    def __bytes__(self) -> bytes: ...
    def program_id(self, instruction_index: int) -> Optional[Pubkey]: ...
    def program_index(self, instruction_index: int) -> Optional[int]: ...
    def program_ids(self) -> List[Pubkey]: ...
    def is_key_called_as_program(self, key_index: int) -> bool: ...
    def program_position(self, index: int) -> Optional[int]: ...
    def maybe_executable(self, i: int) -> bool: ...
    def is_signer(self, i: int) -> bool: ...
    def signer_keys(self) -> List[Pubkey]: ...
    def has_duplicates(self) -> bool: ...
    @staticmethod
    def default() -> "Message": ...
    @staticmethod
    def from_bytes(data: bytes) -> "Message": ...
    def __richcmp__(self, other: "Message", op: int) -> bool: ...
    def __repr__(self) -> str: ...
    def __str__(self) -> str: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "Message": ...

class MessageAddressTableLookup:
    def __init__(
        self, account_key: Pubkey, writable_indexes: bytes, readonly_indexes: bytes
    ) -> None: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "MessageAddressTableLookup", op: int) -> bool: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "MessageAddressTableLookup": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "MessageAddressTableLookup": ...
    @property
    def account_key(self) -> Pubkey: ...
    @property
    def writable_indexes(self) -> bytes: ...
    @property
    def readonly_indexes(self) -> bytes: ...

class MessageV0:
    def __init__(
        self,
        header: MessageHeader,
        account_keys: Sequence[Pubkey],
        recent_blockhash: Hash,
        instructions: Sequence[CompiledInstruction],
        address_table_lookups: Sequence[MessageAddressTableLookup],
    ) -> None: ...
    @staticmethod
    def try_compile(
        payer: Pubkey,
        instructions: Sequence[Instruction],
        address_lookup_table_accounts: Sequence[AddressLookupTableAccount],
        recent_blockhash: Hash,
    ) -> "MessageV0": ...
    @property
    def header(self) -> MessageHeader: ...
    @property
    def account_keys(self) -> List[Pubkey]: ...
    @property
    def recent_blockhash(self) -> Hash: ...
    @property
    def instructions(self) -> List[CompiledInstruction]: ...
    @property
    def address_table_lookups(self) -> List[MessageAddressTableLookup]: ...
    def sanitize(self) -> None: ...
    def hash(self) -> Hash: ...
    @staticmethod
    def hash_raw_message(message_bytes: bytes) -> Hash: ...
    def __bytes__(self) -> bytes: ...
    def is_key_called_as_program(self, key_index: int) -> bool: ...
    def is_maybe_writable(self, key_index: int) -> bool: ...
    def is_non_loader_key(self, key_index: int) -> bool: ...
    def is_signer(self, index: int) -> bool: ...
    @staticmethod
    def default() -> "MessageV0": ...
    @staticmethod
    def from_bytes(data: bytes) -> "MessageV0": ...
    def __richcmp__(self, other: "MessageV0", op: int) -> bool: ...
    def __repr__(self) -> str: ...
    def __str__(self) -> str: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "MessageV0": ...

# VersionedMessage is not really defined here, defined in message.py
VersionedMessage = Union[Message, MessageV0]

def to_bytes_versioned(msg: VersionedMessage) -> bytes: ...
def from_bytes_versioned(raw: bytes) -> VersionedMessage: ...

CLOCK: Final[Pubkey]
RECENT_BLOCKHASHES: Final[Pubkey]
RENT: Final[Pubkey]
REWARDS: Final[Pubkey]
STAKE_HISTORY: Final[Pubkey]
EPOCH_SCHEDULE: Final[Pubkey]
INSTRUCTIONS: Final[Pubkey]
SLOT_HASHES: Final[Pubkey]

class UiDataSliceConfig:
    def __init__(self, offset: int, length: int) -> None: ...
    @property
    def offset(self) -> int: ...
    @property
    def length(self) -> int: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "UiDataSliceConfig", op: int) -> bool: ...

class UiAccountEncoding:
    Binary: "UiAccountEncoding"
    Base58: "UiAccountEncoding"
    Base64: "UiAccountEncoding"
    JsonParsed: "UiAccountEncoding"
    Base64Zstd: "UiAccountEncoding"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...

class ParsedAccount:
    def __init__(self, program: str, parsed: Dict[str, Json], space: int) -> None: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "ParsedAccount", op: int) -> bool: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "ParsedAccount": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "ParsedAccount": ...
    @property
    def program(self) -> str: ...
    @property
    def parsed(self) -> Dict[str, Json]: ...
    @property
    def space(self) -> int: ...

class UiTokenAmount:
    def __init__(
        self,
        ui_amount: Optional[float],
        decimals: int,
        amount: str,
        ui_amount_string: str,
    ) -> None: ...
    @property
    def ui_amount(self) -> Optional[float]: ...
    @property
    def decimals(self) -> int: ...
    @property
    def amount(self) -> str: ...
    @property
    def ui_amount_string(self) -> str: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "UiTokenAmount": ...
    @staticmethod
    def from_bytes(data: bytes) -> "UiTokenAmount": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class Account:
    def __init__(
        self,
        lamports: int,
        data: bytes,
        owner: Pubkey,
        executable: bool = False,
        rent_epoch: int = 0,
    ) -> None: ...
    @staticmethod
    def default() -> "Account": ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "Account", op: int) -> bool: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "Account": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "Account": ...
    @property
    def lamports(self) -> int: ...
    @property
    def data(self) -> bytes: ...
    @property
    def owner(self) -> Pubkey: ...
    @property
    def executable(self) -> bool: ...
    @property
    def rent_epoch(self) -> int: ...

class AccountJSON:
    def __init__(
        self,
        lamports: int,
        data: ParsedAccount,
        owner: Pubkey,
        executable: bool = False,
        rent_epoch: int = 0,
    ) -> None: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "AccountJSON", op: int) -> bool: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "AccountJSON": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "Account": ...
    @property
    def lamports(self) -> int: ...
    @property
    def data(self) -> ParsedAccount: ...
    @property
    def owner(self) -> Pubkey: ...
    @property
    def executable(self) -> bool: ...
    @property
    def rent_epoch(self) -> int: ...

TOKEN_PROGRAM_ID: Final[Pubkey]

def get_associated_token_address(
    wallet_address: Pubkey,
    token_mint_address: Pubkey,
    token_program_id: Optional[Pubkey] = None,
) -> Pubkey: ...

class Mint:
    decimals: int
    freeze_authority: Optional[Pubkey]
    is_initialized: bool
    mint_authority: Optional[Pubkey]
    supply: int
    def __init__(
        self,
        mint_authority: Optional[Pubkey],
        supply: int,
        decimals: int,
        is_initialized: bool,
        freeze_authority: Optional[Pubkey] = None,
    ) -> None: ...
    @staticmethod
    def default() -> "Mint": ...
    @staticmethod
    def from_bytes(raw: bytes) -> "Mint": ...
    def __bytes__(self) -> bytes: ...
    def __richcmp__(self, other: "Mint", op: int) -> bool: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...

class Multisig:
    is_initialized: bool
    m: int
    n: int
    signers: List[Pubkey]
    def __init__(
        self, m: int, n: int, is_initialized: bool, signers: Sequence[Pubkey]
    ) -> None: ...
    @staticmethod
    def default() -> "Multisig": ...
    @staticmethod
    def from_bytes(raw: bytes) -> "Multisig": ...
    def __richcmp__(self, other: "Multisig", op: int) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...

class TokenAccountState:
    Uninitialized: "TokenAccountState"
    Initialized: "TokenAccountState"
    Frozen: "TokenAccountState"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...

class TokenAccount:
    amount: int
    close_authority: Optional[Pubkey]
    delegate: Optional[Pubkey]
    delegated_amount: int
    is_native: Optional[int]
    mint: Pubkey
    owner: Pubkey
    state: TokenAccountState
    def __init__(
        self,
        mint: Pubkey,
        owner: Pubkey,
        amount: int,
        delegate: Optional[Pubkey],
        state: TokenAccountState,
        is_native: Optional[int],
        delegated_amount: int,
        close_authority: Optional[int] = None,
    ) -> None: ...
    @staticmethod
    def default() -> "TokenAccount": ...
    @staticmethod
    def from_bytes(raw: bytes) -> "TokenAccount": ...
    def __richcmp__(self, other: "TokenAccount", op: int) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...

class CommitmentLevel:
    Processed: "CommitmentLevel"
    Confirmed: "CommitmentLevel"
    Finalized: "CommitmentLevel"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    @staticmethod
    def from_string(s: str) -> "CommitmentLevel": ...
    @staticmethod
    def default() -> "CommitmentLevel": ...

class CommitmentConfig:
    def __init__(self, commitment: "CommitmentLevel") -> None: ...
    @property
    def commitment(self) -> "CommitmentLevel": ...
    @staticmethod
    def from_string(s: str) -> "CommitmentConfig": ...
    @staticmethod
    def processed() -> "CommitmentConfig": ...
    @staticmethod
    def confirmed() -> "CommitmentConfig": ...
    @staticmethod
    def finalized() -> "CommitmentConfig": ...
    @staticmethod
    def default() -> "CommitmentConfig": ...
    def is_finalized(self) -> bool: ...
    def is_confirmed(self) -> bool: ...
    def is_at_least_confirmed(self) -> bool: ...

class EpochInfo:
    epoch: int
    slot_index: int
    slots_in_epoch: int
    absolute_slot: int
    block_height: int
    transaction_count: Optional[int]
    def __init__(
        self,
        epoch: int,
        slot_index: int,
        slots_in_epoch: int,
        absolute_slot: int,
        block_height: int,
        transaction_count: Optional[int] = None,
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "EpochInfo": ...
    @staticmethod
    def from_bytes(data: bytes) -> "EpochInfo": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class EpochRewards:
    def __init__(
        self,
        distribution_starting_block_height: int,
        num_partitions: int,
        parent_blockhash: Hash,
        total_points: int,
        total_rewards: int,
        distributed_rewards: int,
        active: bool,
    ) -> None: ...
    @property
    def distribution_starting_block_height(self) -> int: ...
    @distribution_starting_block_height.setter
    def distribution_starting_block_height(self, val: int) -> None: ...
    @property
    def num_partitions(self) -> int: ...
    @num_partitions.setter
    def num_partitions(self, val: int) -> None: ...
    @property
    def parent_blockhash(self) -> Hash: ...
    @parent_blockhash.setter
    def parent_blockhash(self, val: Hash) -> None: ...
    @property
    def total_points(self) -> int: ...
    @total_points.setter
    def total_points(self, val: int) -> None: ...
    @property
    def total_rewards(self) -> int: ...
    @total_rewards.setter
    def total_rewards(self, val: int) -> None: ...
    @property
    def distributed_rewards(self) -> int: ...
    @distributed_rewards.setter
    def distributed_rewards(self, val: int) -> None: ...
    @property
    def active(self) -> bool: ...
    @active.setter
    def active(self, val: bool) -> None: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "EpochRewards": ...
    @staticmethod
    def from_json(raw: str) -> "EpochRewards": ...
    def to_json(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "EpochRewards", op: int) -> bool: ...

class EpochSchedule:
    def __init__(self, slots_per_epoch: int) -> None: ...
    @property
    def slots_per_epoch(self) -> int: ...
    @property
    def leader_schedule_slot_offset(self) -> int: ...
    @property
    def warmup(self) -> bool: ...
    @property
    def first_normal_epoch(self) -> int: ...
    @property
    def first_normal_slot(self) -> int: ...
    @staticmethod
    def default() -> "EpochSchedule": ...
    @staticmethod
    def without_warmup() -> "EpochSchedule": ...
    @staticmethod
    def custom(
        slots_per_epoch: int, leader_schedule_slot_offset: int, warmup: bool
    ) -> "EpochSchedule": ...
    def get_slots_in_epoch(self, epoch: int) -> int: ...
    def get_leader_schedule_epoch(self, slot: int) -> int: ...
    def get_epoch(self, slot: int) -> int: ...
    def get_epoch_and_slot_index(self, slot: int) -> Tuple[int, int]: ...
    def get_first_slot_in_epoch(self, epoch: int) -> int: ...
    def get_last_slot_in_epoch(self, epoch: int) -> int: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "EpochSchedule", op: int) -> bool: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "EpochSchedule": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "EpochSchedule": ...

class SignerError(Exception): ...
class BincodeError(Exception): ...
class CborError(Exception): ...
class SerdeJSONError(Exception): ...

class Signature:
    LENGTH: ClassVar[int]
    def __init__(self, signature_slice: bytes) -> None: ...
    @staticmethod
    def new_unique() -> "Signature": ...
    @staticmethod
    def default() -> "Signature": ...
    @staticmethod
    def from_string(s: str) -> "Signature": ...
    def verify(self, pubkey: Pubkey, message_bytes: bytes) -> bool: ...
    def to_bytes(self) -> bytes: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "Signature", op: int) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "Signature": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "Signature": ...

class Keypair:
    LENGTH: ClassVar[int]
    def __init__(self) -> None: ...
    @staticmethod
    def from_bytes(raw_bytes: Union[bytes, Sequence[int]]) -> "Keypair": ...
    @staticmethod
    def from_seed(seed: Union[bytes, Sequence[int]]) -> "Keypair": ...
    @staticmethod
    def from_seed_and_derivation_path(
        seed: Union[bytes, Sequence[int]], dpath: str
    ) -> "Keypair": ...
    @staticmethod
    def from_base58_string(s: str) -> "Keypair": ...
    @staticmethod
    def from_seed_phrase_and_passphrase(
        seed_phrase: str, passphrase: str
    ) -> "Keypair": ...
    def secret(self) -> bytes: ...
    def pubkey(self) -> Pubkey: ...
    def sign_message(self, message: bytes) -> Signature: ...
    def to_bytes(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __richcmp__(self, other: Union[Presigner, "Keypair"], op: int) -> bool: ...
    def __hash__(self) -> int: ...
    def is_interactive(self) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "Keypair": ...

class Presigner:
    def __init__(self, pubkey: Pubkey, signature: Signature) -> None: ...
    def pubkey(self) -> Pubkey: ...
    def sign_message(self, message: Union[bytes, Sequence[int]]) -> Signature: ...
    def __richcmp__(self, other: Union["Presigner", Keypair], op: int) -> bool: ...
    @staticmethod
    def default() -> "Presigner": ...
    def __repr__(self) -> str: ...
    def __hash__(self) -> int: ...

class NullSigner:
    def __init__(self, pubkey: Pubkey) -> None: ...
    def pubkey(self) -> Pubkey: ...
    def sign_message(self, message: Union[bytes, Sequence[int]]) -> Signature: ...
    def __richcmp__(self, other: Union["NullSigner", Keypair], op: int) -> bool: ...
    @staticmethod
    def default() -> "NullSigner": ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(data: bytes) -> "NullSigner": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "NullSigner": ...

class SlotHistoryCheck:
    Future: "SlotHistoryCheck"
    TooOld: "SlotHistoryCheck"
    Found: "SlotHistoryCheck"
    NotFound: "SlotHistoryCheck"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...

class SlotHistory:
    def __init__(self, bits: Sequence[int], next_slot: int) -> None: ...
    @staticmethod
    def default() -> "SlotHistory": ...
    @property
    def bits(self) -> List[int]: ...
    @bits.setter
    def bits(self, val: Sequence[int]) -> None: ...
    @property
    def next_slot(self) -> int: ...
    @next_slot.setter
    def next_slot(self, val: int) -> None: ...
    def add(self, slot: int) -> None: ...
    def check(self, slot: int) -> SlotHistoryCheck: ...
    def oldest(self) -> int: ...
    def newest(self) -> int: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "SlotHistory": ...
    @staticmethod
    def from_json(raw: str) -> "SlotHistory": ...
    def to_json(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "SlotHistory", op: int) -> bool: ...

class StakeHistoryEntry:
    def __init__(
        self,
        effective: int,
        activating: int,
        deactivating: int,
    ) -> None: ...
    @property
    def effective(self) -> int: ...
    @effective.setter
    def effective(self, val: int) -> None: ...
    @property
    def activating(self) -> int: ...
    @activating.setter
    def activating(self, val: int) -> None: ...
    @property
    def deactivating(self) -> int: ...
    @deactivating.setter
    def deactivating(self, val: int) -> None: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "StakeHistoryEntry": ...
    @staticmethod
    def from_json(raw: str) -> "StakeHistoryEntry": ...
    def to_json(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "StakeHistoryEntry", op: int) -> bool: ...

class StakeHistory:
    def __init__(
        self,
    ) -> None: ...
    def get(self, epoch: int) -> Optional[StakeHistoryEntry]: ...
    def add(self, epoch: int, entry: StakeHistoryEntry) -> None: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "StakeHistory": ...
    @staticmethod
    def from_json(raw: str) -> "StakeHistory": ...
    def to_json(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "StakeHistory", op: int) -> bool: ...

Signer = Union[Keypair, Presigner, NullSigner]

class Transaction:
    def __init__(
        self,
        from_keypairs: Sequence[Signer],
        message: Message,
        recent_blockhash: Hash,
    ) -> None: ...
    @property
    def signatures(self) -> List[Signature]: ...
    @signatures.setter
    def signatures(self, signatures: Sequence[Signature]) -> None: ...
    @property
    def message(self) -> Message: ...
    @staticmethod
    def new_unsigned(message: Message) -> "Transaction": ...
    @staticmethod
    def new_with_payer(
        instructions: Sequence[Instruction],
        payer: Optional[Pubkey] = None,
    ) -> "Transaction": ...
    @staticmethod
    def new_signed_with_payer(
        instructions: Sequence[Instruction],
        payer: Optional[Pubkey],
        signing_keypairs: Sequence[Signer],
        recent_blockhash: Hash,
    ) -> "Transaction": ...
    @staticmethod
    def new_with_compiled_instructions(
        from_keypairs: Sequence[Signer],
        keys: Sequence[Pubkey],
        recent_blockhash: Hash,
        program_ids: Sequence[Pubkey],
        instructions: Sequence[CompiledInstruction],
    ) -> "Transaction": ...
    @staticmethod
    def populate(
        message: Message, signatures: Sequence[Signature]
    ) -> "Transaction": ...
    def data(self, instruction_index: int) -> bytes: ...
    def key(self, instruction_index: int, accounts_index: int) -> Optional[Pubkey]: ...
    def signer_key(
        self, instruction_index: int, accounts_index: int
    ) -> Optional[Pubkey]: ...
    def message_data(self) -> bytes: ...
    def sign(self, keypairs: Sequence[Signer], recent_blockhash: Hash) -> None: ...
    def partial_sign(
        self,
        keypairs: Sequence[Signer],
        recent_blockhash: Hash,
    ) -> None: ...
    def verify(self) -> None: ...
    def verify_and_hash_message(self) -> Hash: ...
    def verify_with_results(self) -> List[bool]: ...
    def get_signing_keypair_positions(
        self,
        pubkeys: Sequence[Pubkey],
    ) -> List[Optional[int]]: ...
    def replace_signatures(
        self, signers: Sequence[Tuple[Pubkey, Signature]]
    ) -> None: ...
    def is_signed(self) -> bool: ...
    def uses_durable_nonce(self) -> Optional[CompiledInstruction]: ...
    def sanitize(self) -> None: ...
    def __bytes__(self) -> bytes: ...
    @staticmethod
    def default() -> "Transaction": ...
    @staticmethod
    def from_bytes(data: bytes) -> "Transaction": ...
    def __richcmp__(self, other: "Transaction", op: int) -> bool: ...
    def __repr__(self) -> str: ...
    def __str__(self) -> str: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "Transaction": ...

class Legacy:
    Legacy: "Legacy"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...

class VersionedTransaction:
    def __init__(
        self,
        message: Union[Message, MessageV0],
        keypairs: Sequence[Signer],
    ) -> None: ...
    @property
    def signatures(self) -> List[Signature]: ...
    @signatures.setter
    def signatures(self, signatures: Sequence[Signature]) -> None: ...
    @property
    def message(self) -> Union[Message, MessageV0]: ...
    @staticmethod
    def populate(
        message: Union[Message, MessageV0], signatures: Sequence[Signature]
    ) -> "VersionedTransaction": ...
    def verify_and_hash_message(self) -> Hash: ...
    def verify_with_results(self) -> List[bool]: ...
    def sanitize(self) -> None: ...
    def version(self) -> TransactionVersion: ...
    def into_legacy_transaction(self) -> Optional[Transaction]: ...
    def __bytes__(self) -> bytes: ...
    @staticmethod
    def default() -> "VersionedTransaction": ...
    @staticmethod
    def from_bytes(data: bytes) -> "VersionedTransaction": ...
    def __richcmp__(self, other: "VersionedTransaction", op: int) -> bool: ...
    def __repr__(self) -> str: ...
    def __str__(self) -> str: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "VersionedTransaction": ...
    @staticmethod
    def from_legacy(tx: Transaction) -> "VersionedTransaction": ...
    def uses_durable_nonce(self) -> bool: ...

class SanitizeError(Exception): ...
class TransactionError(Exception): ...

TransactionVersion = Union[Legacy, int]

class UiTransactionEncoding:
    Binary: "UiTransactionEncoding"
    Base64: "UiTransactionEncoding"
    Base58: "UiTransactionEncoding"
    Json: "UiTransactionEncoding"
    JsonParsed: "UiTransactionEncoding"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...

class TransactionDetails:
    Full: "TransactionDetails"
    Signatures: "TransactionDetails"
    None_: "TransactionDetails"
    Accounts: "TransactionDetails"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...

class TransactionBinaryEncoding:
    Base58: "TransactionBinaryEncoding"
    Base64: "TransactionBinaryEncoding"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...

class UiCompiledInstruction:
    def __init__(
        self,
        program_id_index: int,
        accounts: bytes,
        data: str,
        stack_height: Optional[int] = None,
    ) -> None: ...
    @property
    def program_id_index(self) -> int: ...
    @property
    def accounts(self) -> bytes: ...
    @property
    def data(self) -> str: ...
    @property
    def stack_height(self) -> Optional[int]: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "UiCompiledInstruction": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "UiCompiledInstruction": ...

class UiAddressTableLookup:
    def __init__(
        self, account_key: Pubkey, writable_indexes: bytes, readonly_indexes: bytes
    ) -> None: ...
    @property
    def account_key(self) -> Pubkey: ...
    @property
    def writable_indexes(self) -> bytes: ...
    @property
    def readonly_indexes(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "UiAddressTableLookup": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "UiAddressTableLookup": ...

class UiRawMessage:
    def __init__(
        self,
        header: MessageHeader,
        account_keys: Sequence[Pubkey],
        recent_blockhash: Hash,
        instructions: Sequence[UiCompiledInstruction],
        address_table_lookups: Optional[Sequence[UiAddressTableLookup]] = None,
    ) -> None: ...
    @property
    def header(self) -> MessageHeader: ...
    @property
    def account_keys(self) -> List[Pubkey]: ...
    @property
    def recent_blockhash(self) -> Hash: ...
    @property
    def instructions(self) -> List[UiCompiledInstruction]: ...
    @property
    def address_table_lookups(self) -> Optional[List[UiAddressTableLookup]]: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "UiRawMessage": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "UiRawMessage": ...

class ParsedAccountSource:
    Transaction: "ParsedAccountSource"
    LookupTable: "ParsedAccountSource"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...

class ParsedAccountTxStatus:
    def __init__(
        self,
        pubkey: Pubkey,
        writable: bool,
        signer: bool,
        source: Optional[ParsedAccountSource] = None,
    ) -> None: ...
    @property
    def pubkey(self) -> Pubkey: ...
    @property
    def writable(self) -> bool: ...
    @property
    def signer(self) -> bool: ...
    @property
    def source(self) -> Optional[ParsedAccountSource]: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "ParsedAccount": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "ParsedAccount": ...

class ParsedInstruction:
    def __init__(
        self,
        program: str,
        program_id: Pubkey,
        parsed: Dict[str, Json],
        stack_height: Optional[int] = None,
    ) -> None: ...
    @property
    def program(self) -> str: ...
    @property
    def program_id(self) -> Pubkey: ...
    @property
    def parsed(self) -> Dict[str, Json]: ...
    @property
    def stack_height(self) -> Optional[int]: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "ParsedInstruction": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "ParsedInstruction": ...

class UiPartiallyDecodedInstruction:
    def __init__(
        self,
        program_id: Pubkey,
        accounts: Sequence[Pubkey],
        data: str,
        stack_height: Optional[int] = None,
    ) -> None: ...
    @property
    def program_id(self) -> Pubkey: ...
    @property
    def accounts(self) -> List[Pubkey]: ...
    @property
    def data(self) -> str: ...
    @property
    def stack_height(self) -> Optional[int]: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "UiPartiallyDecodedInstruction": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "UiPartiallyDecodedInstruction": ...

class UiAccountsList:
    def __init__(
        self,
        signatures: Sequence[Signature],
        account_keys: Sequence[ParsedAccount],
    ) -> None: ...
    @property
    def signatures(self) -> List[Signature]: ...
    @property
    def account_keys(self) -> List[ParsedAccount]: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "UiAccountsList": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "UiAccountsList": ...

UiParsedInstruction = Union[ParsedInstruction, UiPartiallyDecodedInstruction]
UiInstruction = Union[UiParsedInstruction, UiCompiledInstruction]
UiMessage = Union[UiParsedMessage, UiRawMessage]
EncodedVersionedTransaction = Union[VersionedTransaction, UiTransaction, UiAccountsList]
InstructionErrorType = Union[
    InstructionErrorFieldless,
    InstructionErrorCustom,
    InstructionErrorBorshIO,
]
TransactionErrorType = Union[
    TransactionErrorFieldless,
    TransactionErrorInstructionError,
    TransactionErrorDuplicateInstruction,
    TransactionErrorInsufficientFundsForRent,
    TransactionErrorProgramExecutionTemporarilyRestricted,
]

class UiParsedMessage:
    def __init__(
        self,
        account_keys: Sequence[ParsedAccount],
        recent_blockhash: Hash,
        instructions: Sequence[UiInstruction],
        address_table_lookups: Optional[Sequence[UiAddressTableLookup]],
    ) -> None: ...
    @property
    def account_keys(self) -> List[ParsedAccount]: ...
    @property
    def recent_blockhash(self) -> Hash: ...
    @property
    def instructions(self) -> List[UiInstruction]: ...
    @property
    def address_table_lookups(self) -> Optional[Sequence[UiAddressTableLookup]]: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "UiParsedMessage": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "UiParsedMessage": ...

class UiTransaction:
    def __init__(self, signatures: Sequence[Signature], message: UiMessage) -> None: ...
    @property
    def signatures(self) -> List[Signature]: ...
    @property
    def message(self) -> UiMessage: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "UiTransaction": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "UiTransaction": ...

class UiInnerInstructions:
    def __init__(self, index: int, instructions: Sequence[UiInstruction]) -> None: ...
    @property
    def index(self) -> int: ...
    @property
    def instructions(self) -> List[UiInstruction]: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "UiInnerInstructions": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "UiInnerInstructions": ...

class UiLoadedAddresses:
    def __init__(
        self, writable: Sequence[Pubkey], readonly: Sequence[Pubkey]
    ) -> None: ...
    @property
    def writable(self) -> List[Pubkey]: ...
    @property
    def readonly(self) -> List[Pubkey]: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "UiLoadedAddresses": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "UiLoadedAddresses": ...

class UiTransactionTokenBalance:
    def __init__(
        self,
        account_index: int,
        mint: Pubkey,
        ui_token_amount: UiTokenAmount,
        owner: Optional[Pubkey],
        program_id: Optional[Pubkey],
    ) -> None: ...
    @property
    def account_index(self) -> int: ...
    @property
    def mint(self) -> Pubkey: ...
    @property
    def ui_token_amount(self) -> UiTokenAmount: ...
    @property
    def owner(self) -> Optional[Pubkey]: ...
    @property
    def program_id(self) -> Optional[Pubkey]: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "UiTransactionTokenBalance": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "UiTransactionTokenBalance": ...

class RewardType:
    Fee: "RewardType"
    Rent: "RewardType"
    Staking: "RewardType"
    Voting: "RewardType"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...

class TransactionReturnData:
    def __init__(self, program_id: Pubkey, data: Sequence[int]) -> None: ...
    @property
    def program_id(self) -> Pubkey: ...
    @property
    def data(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "TransactionReturnData": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "TransactionReturnData": ...

class UiTransactionStatusMeta:
    def __init__(
        self,
        err: Optional[TransactionErrorType],
        fee: int,
        pre_balances: Sequence[int],
        post_balances: Sequence[int],
        inner_instructions: Optional[Sequence[UiInnerInstructions]] = None,
        log_messages: Optional[Sequence[str]] = None,
        pre_token_balances: Optional[Sequence[UiTransactionTokenBalance]] = None,
        post_token_balances: Optional[Sequence[UiTransactionTokenBalance]] = None,
        rewards: Optional[Sequence[Reward]] = None,
        loaded_addresses: Optional[UiLoadedAddresses] = None,
        return_data: Optional[TransactionReturnData] = None,
        compute_units_consumed: Optional[int] = None,
    ) -> None: ...
    @property
    def err(self) -> Optional[TransactionErrorType]: ...
    @property
    def fee(self) -> int: ...
    @property
    def pre_balances(self) -> List[int]: ...
    @property
    def post_balances(self) -> List[int]: ...
    @property
    def inner_instructions(self) -> Optional[List[UiInnerInstructions]]: ...
    @property
    def log_messages(self) -> Optional[List[str]]: ...
    @property
    def pre_token_balances(self) -> Optional[List[UiTransactionTokenBalance]]: ...
    @property
    def post_token_balances(self) -> Optional[List[UiTransactionTokenBalance]]: ...
    @property
    def rewards(self) -> Optional[List[Reward]]: ...
    @property
    def loaded_addresses(self) -> Optional[UiLoadedAddresses]: ...
    @property
    def return_data(self) -> Optional[TransactionReturnData]: ...
    @property
    def compute_units_consumed(self) -> Optional[int]: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "UiTransactionStatusMeta": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "UiTransactionStatusMeta": ...

class EncodedTransactionWithStatusMeta:
    def __init__(
        self,
        transaction: EncodedVersionedTransaction,
        meta: Optional[UiTransactionStatusMeta],
        version: Optional[TransactionVersion],
    ) -> None: ...
    @property
    def transaction(self) -> EncodedVersionedTransaction: ...
    @property
    def meta(self) -> Optional[UiTransactionStatusMeta]: ...
    @property
    def version(self) -> Optional[TransactionVersion]: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "EncodedTransactionWithStatusMeta": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "EncodedTransactionWithStatusMeta": ...

class InstructionErrorCustom:
    def __init__(self, code: int) -> None: ...
    @property
    def code(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "InstructionErrorCustom": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "InstructionErrorCustom": ...

class InstructionErrorBorshIO:
    def __init__(self, value: str) -> None: ...
    @property
    def value(self) -> str: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "InstructionErrorBorshIO": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "InstructionErrorBorshIO": ...

class InstructionErrorFieldless:
    GenericError: "InstructionErrorFieldless"
    InvalidArgument: "InstructionErrorFieldless"
    InvalidInstructionData: "InstructionErrorFieldless"
    InvalidAccountData: "InstructionErrorFieldless"
    AccountDataTooSmall: "InstructionErrorFieldless"
    InsufficientFunds: "InstructionErrorFieldless"
    IncorrectProgramId: "InstructionErrorFieldless"
    MissingRequiredSignature: "InstructionErrorFieldless"
    AccountAlreadyInitialized: "InstructionErrorFieldless"
    UninitializedAccount: "InstructionErrorFieldless"
    UnbalancedInstruction: "InstructionErrorFieldless"
    ModifiedProgramId: "InstructionErrorFieldless"
    ExternalAccountLamportSpend: "InstructionErrorFieldless"
    ExternalAccountDataModified: "InstructionErrorFieldless"
    ReadonlyLamportChange: "InstructionErrorFieldless"
    ReadonlyDataModified: "InstructionErrorFieldless"
    DuplicateAccountIndex: "InstructionErrorFieldless"
    ExecutableModified: "InstructionErrorFieldless"
    RentEpochModified: "InstructionErrorFieldless"
    NotEnoughAccountKeys: "InstructionErrorFieldless"
    AccountDataSizeChanged: "InstructionErrorFieldless"
    AccountNotExecutable: "InstructionErrorFieldless"
    AccountBorrowFailed: "InstructionErrorFieldless"
    AccountBorrowOutstanding: "InstructionErrorFieldless"
    DuplicateAccountOutOfSync: "InstructionErrorFieldless"
    InvalidError: "InstructionErrorFieldless"
    ExecutableDataModified: "InstructionErrorFieldless"
    ExecutableLamportChange: "InstructionErrorFieldless"
    ExecutableAccountNotRentExempt: "InstructionErrorFieldless"
    UnsupportedProgramId: "InstructionErrorFieldless"
    CallDepth: "InstructionErrorFieldless"
    MissingAccount: "InstructionErrorFieldless"
    ReentrancyNotAllowed: "InstructionErrorFieldless"
    MaxSeedLengthExceeded: "InstructionErrorFieldless"
    InvalidSeeds: "InstructionErrorFieldless"
    InvalidRealloc: "InstructionErrorFieldless"
    ComputationalBudgetExceeded: "InstructionErrorFieldless"
    PrivilegeEscalation: "InstructionErrorFieldless"
    ProgramEnvironmentSetupFailure: "InstructionErrorFieldless"
    ProgramFailedToComplete: "InstructionErrorFieldless"
    ProgramFailedToCompile: "InstructionErrorFieldless"
    Immutable: "InstructionErrorFieldless"
    IncorrectAuthority: "InstructionErrorFieldless"
    AccountNotRentExempt: "InstructionErrorFieldless"
    InvalidAccountOwner: "InstructionErrorFieldless"
    ArithmeticOverflow: "InstructionErrorFieldless"
    UnsupportedSysvar: "InstructionErrorFieldless"
    IllegalOwner: "InstructionErrorFieldless"
    MaxAccountsDataAllocationsExceeded: "InstructionErrorFieldless"
    ActiveVoteAccountClose: "InstructionErrorFieldless"
    MaxInstructionTraceLengthExceeded: "InstructionErrorFieldless"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...

class TransactionErrorInstructionError:
    def __init__(self, index: int, err: InstructionErrorType) -> None: ...
    @property
    def index(self) -> int: ...
    @property
    def err(self) -> InstructionErrorType: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "TransactionErrorInstructionError": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "TransactionErrorInstructionError": ...

class TransactionErrorDuplicateInstruction:
    def __init__(self, index: int) -> None: ...
    @property
    def index(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "TransactionErrorDuplicateInstruction": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "TransactionErrorDuplicateInstruction": ...

class TransactionErrorInsufficientFundsForRent:
    account_index: int
    def __init__(self, account_index: int) -> None: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "TransactionErrorInsufficientFundsForRent": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "TransactionErrorInsufficientFundsForRent": ...

class TransactionErrorProgramExecutionTemporarilyRestricted:
    account_index: int
    def __init__(self, account_index: int) -> None: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(
        raw: bytes,
    ) -> "TransactionErrorProgramExecutionTemporarilyRestricted": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(
        raw: str,
    ) -> "TransactionErrorProgramExecutionTemporarilyRestricted": ...

class TransactionErrorFieldless:
    AccountInUse: "TransactionErrorFieldless"
    AccountLoadedTwice: "TransactionErrorFieldless"
    AccountNotFound: "TransactionErrorFieldless"
    ProgramAccountNotFound: "TransactionErrorFieldless"
    InsufficientFundsForFee: "TransactionErrorFieldless"
    InvalidAccountForFee: "TransactionErrorFieldless"
    AlreadyProcessed: "TransactionErrorFieldless"
    BlockhashNotFound: "TransactionErrorFieldless"
    CallChainTooDeep: "TransactionErrorFieldless"
    MissingSignatureForFee: "TransactionErrorFieldless"
    InvalidAccountIndex: "TransactionErrorFieldless"
    SignatureFailure: "TransactionErrorFieldless"
    InvalidProgramForExecution: "TransactionErrorFieldless"
    SanitizeFailure: "TransactionErrorFieldless"
    ClusterMaintenance: "TransactionErrorFieldless"
    AccountBorrowOutstanding: "TransactionErrorFieldless"
    WouldExceedMaxBlockCostLimit: "TransactionErrorFieldless"
    UnsupportedVersion: "TransactionErrorFieldless"
    InvalidWritableAccount: "TransactionErrorFieldless"
    WouldExceedMaxAccountCostLimit: "TransactionErrorFieldless"
    WouldExceedAccountDataBlockLimit: "TransactionErrorFieldless"
    TooManyAccountLocks: "TransactionErrorFieldless"
    AddressLookupTableNotFound: "TransactionErrorFieldless"
    InvalidAddressLookupTableOwner: "TransactionErrorFieldless"
    InvalidAddressLookupTableData: "TransactionErrorFieldless"
    InvalidAddressLookupTableIndex: "TransactionErrorFieldless"
    InvalidRentPayingAccount: "TransactionErrorFieldless"
    WouldExceedMaxVotefCostLimit: "TransactionErrorFieldless"
    WouldExceedAccountDataTotalLimit: "TransactionErrorFieldless"
    MaxLoadedAccountsDataSizeExceeded: "TransactionErrorFieldless"
    ResanitizationNeeded: "TransactionErrorFieldless"
    InvalidLoadedAccountsDataSizeLimit: "TransactionErrorFieldless"
    UnbalancedTransaction: "TransactionErrorFieldless"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...

class Reward:
    def __init__(
        self,
        pubkey: Pubkey,
        lamports: int,
        post_balance: int,
        reward_type: Optional[RewardType] = None,
        commission: Optional[int] = None,
    ) -> None: ...
    @property
    def pubkey(self) -> Pubkey: ...
    @property
    def lamports(self) -> int: ...
    @property
    def post_balance(self) -> int: ...
    @property
    def reward_type(self) -> Optional[RewardType]: ...
    @property
    def commission(self) -> Optional[int]: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "Reward": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "Reward": ...

class TransactionConfirmationStatus:
    Processed: "TransactionConfirmationStatus"
    Confirmed: "TransactionConfirmationStatus"
    Finalized: "TransactionConfirmationStatus"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...

class TransactionStatus:
    def __init__(
        self,
        slot: int,
        confirmations: Optional[int] = None,
        status: Optional[TransactionErrorType] = None,
        err: Optional[TransactionErrorType] = None,
        confirmation_status: Optional[TransactionConfirmationStatus] = None,
    ) -> None: ...
    @property
    def slot(self) -> int: ...
    @property
    def confirmations(self) -> Optional[int]: ...
    @property
    def status(self) -> Optional[TransactionErrorType]: ...
    @property
    def err(self) -> Optional[TransactionErrorType]: ...
    @property
    def confirmation_status(self) -> Optional[TransactionConfirmationStatus]: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "TransactionStatus": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "TransactionStatus": ...
    def satisfies_commitment(self, commitment_config: CommitmentConfig) -> bool: ...
    def get_confirmation_status(self) -> TransactionConfirmationStatus: ...

class EncodedConfirmedTransactionWithStatusMeta:
    def __init__(
        self,
        slot: int,
        transaction: EncodedTransactionWithStatusMeta,
        block_time: Optional[int] = None,
    ) -> None: ...
    @property
    def slot(self) -> int: ...
    @property
    def transaction(self) -> EncodedTransactionWithStatusMeta: ...
    @property
    def block_time(self) -> Optional[int]: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __eq__(
        self,
        other: object,
    ) -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def from_bytes(raw: bytes) -> "EncodedConfirmedTransactionWithStatusMeta": ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "EncodedConfirmedTransactionWithStatusMeta": ...

class UiConfirmedBlock:
    previous_blockhash: Hash
    blockhash: Hash
    parent_slot: int
    transactions: Optional[List[EncodedTransactionWithStatusMeta]]
    signatures: Optional[List[Signature]]
    rewards: Optional[List[Reward]]
    block_time: Optional[int]
    block_height: Optional[int]
    num_reward_partitions: Optional[int]
    def __init__(
        self,
        previous_blockhash: Hash,
        blockhash: Hash,
        parent_slot: int,
        transactions: Optional[Sequence[EncodedTransactionWithStatusMeta]] = None,
        signatures: Optional[Sequence[Signature]] = None,
        rewards: Optional[Sequence[Reward]] = None,
        block_time: Optional[int] = None,
        block_height: Optional[int] = None,
        num_reward_partitions: Optional[int] = None,
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> UiConfirmedBlock: ...
    @staticmethod
    def from_bytes(data: bytes) -> UiConfirmedBlock: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class InnerInstruction:
    def instruction(self) -> CompiledInstruction: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "InnerInstruction": ...
    @staticmethod
    def from_json(raw: str) -> "InnerInstruction": ...
    def to_json(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "InnerInstruction", op: int) -> bool: ...

class TransactionMetadata:
    def signature(self) -> Signature: ...
    def logs(self) -> List[str]: ...
    def inner_instructions(self) -> List[List[InnerInstruction]]: ...
    def compute_units_consumed(self) -> int: ...
    def transaction_return_data(self) -> TransactionReturnData: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "TransactionMetadata": ...
    @staticmethod
    def from_json(raw: str) -> "TransactionMetadata": ...
    def to_json(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "TransactionMetadata", op: int) -> bool: ...

class FailedTransactionMetadata:
    def err(self) -> TransactionErrorType: ...
    def meta(self) -> TransactionMetadata: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "FailedTransactionMetadata": ...
    @staticmethod
    def from_json(raw: str) -> "FailedTransactionMetadata": ...
    def to_json(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "FailedTransactionMetadata", op: int) -> bool: ...

class SimulatedTransactionInfo:
    def meta(self) -> TransactionMetadata: ...
    def post_accounts(self) -> List[Tuple[Pubkey, Account]]: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "SimulatedTransactionInfo": ...
    @staticmethod
    def from_json(raw: str) -> "SimulatedTransactionInfo": ...
    def to_json(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "SimulatedTransactionInfo", op: int) -> bool: ...

TransactionResult = Union[TransactionMetadata, FailedTransactionMetadata]
SimulateResult = Union[SimulatedTransactionInfo, FailedTransactionMetadata]

DEFAULT_DEV_SLOTS_PER_EPOCH: Final[int]
DEFAULT_HASHES_PER_SECOND: Final[int]
DEFAULT_HASHES_PER_TICK: Final[int]
DEFAULT_MS_PER_SLOT: Final[int]
DEFAULT_SLOTS_PER_EPOCH: Final[int]
DEFAULT_S_PER_SLOT: Final[float]
DEFAULT_TICKS_PER_SECOND: Final[int]
DEFAULT_TICKS_PER_SLOT: Final[int]
FORWARD_TRANSACTIONS_TO_LEADER_AT_SLOT_OFFSET: Final[int]
GENESIS_EPOCH: Final[int]
HOLD_TRANSACTIONS_SLOT_OFFSET: Final[int]
INITIAL_RENT_EPOCH: Final[int]
MAX_HASH_AGE_IN_SECONDS: Final[int]
MAX_PROCESSING_AGE: Final[int]
MAX_RECENT_BLOCKHASHES: Final[int]
MAX_TRANSACTION_FORWARDING_DELAY: Final[int]
MAX_TRANSACTION_FORWARDING_DELAY_GPU: Final[int]
MS_PER_TICK: Final[int]
NUM_CONSECUTIVE_LEADER_SLOTS: Final[int]
SECONDS_PER_DAY: Final[int]
TICKS_PER_DAY: Final[int]

class Clock:
    def __init__(
        self,
        slot: int,
        epoch_start_timestamp: int,
        epoch: int,
        leader_schedule_epoch: int,
        unix_timestamp: int,
    ) -> None: ...
    @property
    def epoch(self) -> int: ...
    @epoch.setter
    def epoch(self, epoch: int) -> None: ...
    @property
    def epoch_start_timestamp(self) -> int: ...
    @epoch_start_timestamp.setter
    def epoch_start_timestamp(self, timestamp: int) -> None: ...
    @property
    def slot(self) -> int: ...
    @slot.setter
    def slot(self, slot: int) -> None: ...
    @property
    def leader_schedule_epoch(self) -> int: ...
    @leader_schedule_epoch.setter
    def leader_schedule_epoch(self, epoch: int) -> None: ...
    @property
    def unix_timestamp(self) -> int: ...
    @unix_timestamp.setter
    def unix_timestamp(self, timestamp: int) -> None: ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "Clock": ...
    @staticmethod
    def from_json(raw: str) -> "Clock": ...
    def to_json(self) -> str: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "Clock", op: int) -> bool: ...

COMPUTE_BUDGET_ID: Final[Pubkey]

def request_heap_frame(bytes_: int) -> Instruction: ...
def set_compute_unit_limit(units: int) -> Instruction: ...
def set_compute_unit_price(micro_lamports: int) -> Instruction: ...

class ComputeBudget:
    def __init__(self) -> None: ...
    @property
    def compute_unit_limit(self) -> int: ...
    @compute_unit_limit.setter
    def compute_unit_limit(self, val: int) -> None: ...
    @property
    def log_64_units(self) -> int: ...
    @log_64_units.setter
    def log_64_units(self, val: int) -> None: ...
    @property
    def create_program_address_units(self) -> int: ...
    @create_program_address_units.setter
    def create_program_address_units(self, val: int) -> None: ...
    @property
    def invoke_units(self) -> int: ...
    @invoke_units.setter
    def invoke_units(self, val: int) -> None: ...
    @property
    def max_instruction_stack_depth(self) -> int: ...
    @max_instruction_stack_depth.setter
    def max_instruction_stack_depth(self, val: int) -> None: ...
    @property
    def max_instruction_trace_length(self) -> int: ...
    @max_instruction_trace_length.setter
    def max_instruction_trace_length(self, val: int) -> None: ...
    @property
    def sha256_base_cost(self) -> int: ...
    @sha256_base_cost.setter
    def sha256_base_cost(self, val: int) -> None: ...
    @property
    def sha256_byte_cost(self) -> int: ...
    @sha256_byte_cost.setter
    def sha256_byte_cost(self, val: int) -> None: ...
    @property
    def sha256_max_slices(self) -> int: ...
    @sha256_max_slices.setter
    def sha256_max_slices(self, val: int) -> None: ...
    @property
    def max_call_depth(self) -> int: ...
    @max_call_depth.setter
    def max_call_depth(self, val: int) -> None: ...
    @property
    def stack_frame_size(self) -> int: ...
    @stack_frame_size.setter
    def stack_frame_size(self, val: int) -> None: ...
    @property
    def log_pubkey_units(self) -> int: ...
    @log_pubkey_units.setter
    def log_pubkey_units(self, val: int) -> None: ...
    @property
    def max_cpi_instruction_size(self) -> int: ...
    @max_cpi_instruction_size.setter
    def max_cpi_instruction_size(self, val: int) -> None: ...
    @property
    def cpi_bytes_per_unit(self) -> int: ...
    @cpi_bytes_per_unit.setter
    def cpi_bytes_per_unit(self, val: int) -> None: ...
    @property
    def sysvar_base_cost(self) -> int: ...
    @sysvar_base_cost.setter
    def sysvar_base_cost(self, val: int) -> None: ...
    @property
    def secp256k1_recover_cost(self) -> int: ...
    @secp256k1_recover_cost.setter
    def secp256k1_recover_cost(self, val: int) -> None: ...
    @property
    def syscall_base_cost(self) -> int: ...
    @syscall_base_cost.setter
    def syscall_base_cost(self, val: int) -> None: ...
    @property
    def curve25519_edwards_validate_point_cost(self) -> int: ...
    @curve25519_edwards_validate_point_cost.setter
    def curve25519_edwards_validate_point_cost(self, val: int) -> None: ...
    @property
    def curve25519_edwards_add_cost(self) -> int: ...
    @curve25519_edwards_add_cost.setter
    def curve25519_edwards_add_cost(self, val: int) -> None: ...
    @property
    def curve25519_edwards_subtract_cost(self) -> int: ...
    @curve25519_edwards_subtract_cost.setter
    def curve25519_edwards_subtract_cost(self, val: int) -> None: ...
    @property
    def curve25519_edwards_multiply_cost(self) -> int: ...
    @curve25519_edwards_multiply_cost.setter
    def curve25519_edwards_multiply_cost(self, val: int) -> None: ...
    @property
    def curve25519_edwards_msm_base_cost(self) -> int: ...
    @curve25519_edwards_msm_base_cost.setter
    def curve25519_edwards_msm_base_cost(self, val: int) -> None: ...
    @property
    def curve25519_edwards_msm_incremental_cost(self) -> int: ...
    @curve25519_edwards_msm_incremental_cost.setter
    def curve25519_edwards_msm_incremental_cost(self, val: int) -> None: ...
    @property
    def curve25519_ristretto_validate_point_cost(self) -> int: ...
    @curve25519_ristretto_validate_point_cost.setter
    def curve25519_ristretto_validate_point_cost(self, val: int) -> None: ...
    @property
    def curve25519_ristretto_add_cost(self) -> int: ...
    @curve25519_ristretto_add_cost.setter
    def curve25519_ristretto_add_cost(self, val: int) -> None: ...
    @property
    def curve25519_ristretto_subtract_cost(self) -> int: ...
    @curve25519_ristretto_subtract_cost.setter
    def curve25519_ristretto_subtract_cost(self, val: int) -> None: ...
    @property
    def curve25519_ristretto_multiply_cost(self) -> int: ...
    @curve25519_ristretto_multiply_cost.setter
    def curve25519_ristretto_multiply_cost(self, val: int) -> None: ...
    @property
    def curve25519_ristretto_msm_base_cost(self) -> int: ...
    @curve25519_ristretto_msm_base_cost.setter
    def curve25519_ristretto_msm_base_cost(self, val: int) -> None: ...
    @property
    def curve25519_ristretto_msm_incremental_cost(self) -> int: ...
    @curve25519_ristretto_msm_incremental_cost.setter
    def curve25519_ristretto_msm_incremental_cost(self, val: int) -> None: ...
    @property
    def heap_size(self) -> int: ...
    @heap_size.setter
    def heap_size(self, val: int) -> None: ...
    @property
    def heap_cost(self) -> int: ...
    @heap_cost.setter
    def heap_cost(self, val: int) -> None: ...
    @property
    def mem_op_base_cost(self) -> int: ...
    @mem_op_base_cost.setter
    def mem_op_base_cost(self, val: int) -> None: ...
    @property
    def alt_bn128_addition_cost(self) -> int: ...
    @alt_bn128_addition_cost.setter
    def alt_bn128_addition_cost(self, val: int) -> None: ...
    @property
    def alt_bn128_multiplication_cost(self) -> int: ...
    @alt_bn128_multiplication_cost.setter
    def alt_bn128_multiplication_cost(self, val: int) -> None: ...
    @property
    def alt_bn128_pairing_one_pair_cost_first(self) -> int: ...
    @alt_bn128_pairing_one_pair_cost_first.setter
    def alt_bn128_pairing_one_pair_cost_first(self, val: int) -> None: ...
    @property
    def alt_bn128_pairing_one_pair_cost_other(self) -> int: ...
    @alt_bn128_pairing_one_pair_cost_other.setter
    def alt_bn128_pairing_one_pair_cost_other(self, val: int) -> None: ...
    @property
    def big_modular_exponentiation_base_cost(self) -> int: ...
    @big_modular_exponentiation_base_cost.setter
    def big_modular_exponentiation_base_cost(self, val: int) -> None: ...
    @property
    def big_modular_exponentiation_cost_divisor(self) -> int: ...
    @big_modular_exponentiation_cost_divisor.setter
    def big_modular_exponentiation_cost_divisor(self, val: int) -> None: ...
    @property
    def poseidon_cost_coefficient_a(self) -> int: ...
    @poseidon_cost_coefficient_a.setter
    def poseidon_cost_coefficient_a(self, val: int) -> None: ...
    @property
    def poseidon_cost_coefficient_c(self) -> int: ...
    @poseidon_cost_coefficient_c.setter
    def poseidon_cost_coefficient_c(self, val: int) -> None: ...
    @property
    def remaining_compute_units_cost(self) -> int: ...
    @remaining_compute_units_cost.setter
    def remaining_compute_units_cost(self, val: int) -> None: ...
    @property
    def alt_bn128_g1_compress(self) -> int: ...
    @alt_bn128_g1_compress.setter
    def alt_bn128_g1_compress(self, val: int) -> None: ...
    @property
    def alt_bn128_g1_decompress(self) -> int: ...
    @alt_bn128_g1_decompress.setter
    def alt_bn128_g1_decompress(self, val: int) -> None: ...
    @property
    def alt_bn128_g2_compress(self) -> int: ...
    @alt_bn128_g2_compress.setter
    def alt_bn128_g2_compress(self, val: int) -> None: ...
    @property
    def alt_bn128_g2_decompress(self) -> int: ...
    @alt_bn128_g2_decompress.setter
    def alt_bn128_g2_decompress(self, val: int) -> None: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "ComputeBudget", op: int) -> bool: ...

class Memcmp:
    def __init__(
        self,
        offset: int,
        bytes_: Union[str, Sequence[int], bytes],
    ): ...
    @property
    def offset(self) -> int: ...
    def __repr__(self) -> str: ...
    def __str__(self) -> str: ...
    def __richcmp__(self, other: "Memcmp", op: int) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "Memcmp": ...

class RpcFilterTypeFieldless:
    TokenAccountState: "RpcFilterTypeFieldless"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    @staticmethod
    def from_string(s: str) -> "RpcFilterTypeFieldless": ...
    @staticmethod
    def default() -> "RpcFilterTypeFieldless": ...

RpcFilterType = Union[int, Memcmp, RpcFilterTypeFieldless]

class RpcSignatureStatusConfig:
    def __init__(self, search_transaction_history: bool): ...
    @property
    def search_transaction_history(self) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcSignatureStatusConfig", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcSignatureStatusConfig": ...

class RpcSendTransactionConfig:
    def __init__(
        self,
        skip_preflight: bool = False,
        preflight_commitment: Optional[CommitmentLevel] = None,
        encoding: Optional[UiTransactionEncoding] = None,
        max_retries: Optional[int] = None,
        min_context_slot: Optional[int] = None,
    ): ...
    @property
    def skip_preflight(self) -> bool: ...
    @property
    def preflight_commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def encoding(self) -> Optional[UiTransactionEncoding]: ...
    @property
    def max_retries(self) -> Optional[int]: ...
    @property
    def min_context_slot(self) -> Optional[int]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcSendTransactionConfig", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcSendTransactionConfig": ...
    @staticmethod
    def default() -> "RpcSendTransactionConfig": ...

class RpcSimulateTransactionAccountsConfig:
    def __init__(
        self, addresses: Sequence[Pubkey], encoding: Optional[UiAccountEncoding] = None
    ): ...
    @property
    def addresses(self) -> List[Pubkey]: ...
    @property
    def encoding(self) -> Optional[UiAccountEncoding]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(
        self, other: "RpcSimulateTransactionAccountsConfig", op: int
    ) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcSimulateTransactionAccountsConfig": ...
    @staticmethod
    def default() -> "RpcSimulateTransactionAccountsConfig": ...

class RpcSimulateTransactionConfig:
    def __init__(
        self,
        sig_verify: bool = False,
        replace_recent_blockhash: bool = False,
        commitment: Optional[CommitmentLevel] = None,
        accounts: Optional[RpcSimulateTransactionAccountsConfig] = None,
        min_context_slot: Optional[int] = None,
        inner_instructions: bool = False,
    ): ...
    @property
    def sig_verify(self) -> bool: ...
    @property
    def replace_recent_blockhash(self) -> bool: ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def accounts(self) -> Optional[RpcSimulateTransactionAccountsConfig]: ...
    @property
    def min_context_slot(self) -> Optional[int]: ...
    @property
    def inner_instructions(self) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcSimulateTransactionConfig", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcSimulateTransactionConfig": ...
    @staticmethod
    def default() -> "RpcSimulateTransactionConfig": ...

class RpcRequestAirdropConfig:
    def __init__(
        self,
        recent_blockhash: Optional[Hash] = None,
        commitment: Optional[CommitmentLevel] = None,
    ): ...
    @property
    def recent_blockhash(self) -> Optional[Hash]: ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcRequestAirdropConfig", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcRequestAirdropConfig": ...
    @staticmethod
    def default() -> "RpcRequestAirdropConfig": ...

class RpcLeaderScheduleConfig:
    def __init__(
        self,
        identity: Optional[Pubkey] = None,
        commitment: Optional[CommitmentLevel] = None,
    ): ...
    @property
    def identity(self) -> Optional[Pubkey]: ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcLeaderScheduleConfig", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcLeaderScheduleConfig": ...
    @staticmethod
    def default() -> "RpcLeaderScheduleConfig": ...

class RpcBlockProductionConfigRange:
    def __init__(self, first_slot: int, last_slot: Optional[int]): ...
    @property
    def first_slot(self) -> int: ...
    @property
    def last_slot(self) -> Optional[int]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcBlockProductionConfigRange", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcBlockProductionConfigRange": ...

class RpcBlockProductionConfig:
    def __init__(
        self,
        identity: Optional[Pubkey] = None,
        range: Optional[RpcBlockProductionConfigRange] = None,
        commitment: Optional[CommitmentLevel] = None,
    ): ...
    @property
    def identity(self) -> Optional[Pubkey]: ...
    @property
    def range(self) -> Optional[RpcBlockProductionConfigRange]: ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcBlockProductionConfig", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcBlockProductionConfig": ...
    @staticmethod
    def default() -> "RpcBlockProductionConfig": ...

class RpcGetVoteAccountsConfig:
    def __init__(
        self,
        vote_pubkey: Optional[Pubkey] = None,
        commitment: Optional[CommitmentLevel] = None,
        keep_unstaked_delinquents: Optional[bool] = None,
        delinquent_slot_distance: Optional[int] = None,
    ): ...
    @property
    def vote_pubkey(self) -> Optional[Pubkey]: ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def keep_unstaked_delinquents(self) -> Optional[bool]: ...
    @property
    def delinquent_slot_distance(self) -> Optional[int]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcGetVoteAccountsConfig", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcGetVoteAccountsConfig": ...
    @staticmethod
    def default() -> "RpcGetVoteAccountsConfig": ...

class RpcLargestAccountsFilter:
    Circulating: "RpcLargestAccountsFilter"
    NonCirculating: "RpcLargestAccountsFilter"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...

class RpcLargestAccountsConfig:
    def __init__(
        self,
        commitment: Optional[CommitmentLevel] = None,
        filter: Optional[RpcLargestAccountsFilter] = None,
    ): ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def filter(self) -> Optional[RpcLargestAccountsFilter]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcLargestAccountsConfig", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcLargestAccountsConfig": ...
    @staticmethod
    def default() -> "RpcLargestAccountsConfig": ...

class RpcSupplyConfig:
    def __init__(
        self,
        exclude_non_circulating_accounts_list: bool,
        commitment: Optional[CommitmentLevel] = None,
    ): ...
    @property
    def exclude_non_circulating_accounts_list(self) -> bool: ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcSupplyConfig", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcSupplyConfig": ...
    @staticmethod
    def default() -> "RpcSupplyConfig": ...

class RpcEpochConfig:
    def __init__(
        self,
        epoch: Optional[int] = None,
        commitment: Optional[CommitmentLevel] = None,
        min_context_slot: Optional[int] = None,
    ): ...
    @property
    def epoch(self) -> Optional[int]: ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def min_context_slot(self) -> Optional[int]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcEpochConfig", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcEpochConfig": ...
    @staticmethod
    def default() -> "RpcEpochConfig": ...

class RpcAccountInfoConfig:
    def __init__(
        self,
        encoding: Optional[UiAccountEncoding] = None,
        data_slice: Optional[UiDataSliceConfig] = None,
        commitment: Optional[CommitmentLevel] = None,
        min_context_slot: Optional[int] = None,
    ): ...
    @property
    def encoding(self) -> Optional[UiAccountEncoding]: ...
    @property
    def data_slice(self) -> Optional[UiDataSliceConfig]: ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def min_context_slot(self) -> Optional[int]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcAccountInfoConfig", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcAccountInfoConfig": ...
    @staticmethod
    def default() -> "RpcAccountInfoConfig": ...

class RpcProgramAccountsConfig:
    def __init__(
        self,
        account_config: RpcAccountInfoConfig,
        filters: Optional[Sequence[Union[int, Memcmp]]] = None,
        with_context: Optional[bool] = None,
    ): ...
    @property
    def account_config(self) -> RpcAccountInfoConfig: ...
    @property
    def filters(self) -> Optional[Sequence[Union[int, Memcmp]]]: ...
    @property
    def with_context(self) -> Optional[bool]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcProgramAccountsConfig", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcProgramAccountsConfig": ...
    @staticmethod
    def default() -> "RpcProgramAccountsConfig": ...

class RpcTransactionLogsFilter:
    All: "RpcTransactionLogsFilter"
    AllWithVotes: "RpcTransactionLogsFilter"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...

class RpcTransactionLogsFilterMentions:
    def __init__(self, pubkey: Pubkey): ...
    def __richcmp__(
        self, other: "RpcTransactionLogsFilterMentions", op: int
    ) -> bool: ...
    def __repr__(self) -> str: ...
    @property
    def pubkey(self) -> Pubkey: ...

class RpcTransactionLogsConfig:
    def __init__(self, commitment: Optional[CommitmentLevel] = None): ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcTransactionLogsConfig", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcTransactionLogsConfig": ...

class RpcTokenAccountsFilterMint:
    def __init__(self, mint: Pubkey): ...
    def __richcmp__(self, other: "RpcTokenAccountsFilterMint", op: int) -> bool: ...
    def __repr__(self) -> str: ...
    @property
    def mint(self) -> Pubkey: ...

class RpcTokenAccountsFilterProgramId:
    def __init__(self, program_id: Pubkey): ...
    def __richcmp__(
        self, other: "RpcTokenAccountsFilterProgramId", op: int
    ) -> bool: ...
    def __repr__(self) -> str: ...
    @property
    def program_id(self) -> Pubkey: ...

class RpcSignatureSubscribeConfig:
    def __init__(
        self,
        commitment: Optional[CommitmentLevel] = None,
        enable_received_notification: Optional[bool] = None,
    ): ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def enable_received_notification(self) -> Optional[bool]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcSignatureSubscribeConfig", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcSignatureSubscribeConfig": ...
    @staticmethod
    def default() -> "RpcSignatureSubscribeConfig": ...

class RpcBlockSubscribeFilter:
    All: "RpcBlockSubscribeFilter"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...

class RpcBlockSubscribeFilterMentions:
    def __init__(self, pubkey: Pubkey): ...
    def __richcmp__(
        self, other: "RpcBlockSubscribeFilterMentions", op: int
    ) -> bool: ...
    def __repr__(self) -> str: ...
    @property
    def pubkey(self) -> Pubkey: ...

class RpcBlockSubscribeConfig:
    def __init__(
        self,
        commitment: Optional[CommitmentLevel] = None,
        encoding: Optional[UiTransactionEncoding] = None,
        transaction_details: Optional[TransactionDetails] = None,
        show_rewards: Optional[bool] = None,
        max_supported_transaction_version: Optional[int] = None,
    ): ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def encoding(self) -> Optional[UiTransactionEncoding]: ...
    @property
    def transaction_details(self) -> Optional[TransactionDetails]: ...
    @property
    def show_rewards(self) -> Optional[bool]: ...
    @property
    def max_supported_transaction_version(self) -> Optional[int]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcBlockSubscribeConfig", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcBlockSubscribeConfig": ...
    @staticmethod
    def default() -> "RpcBlockSubscribeConfig": ...

class RpcSignaturesForAddressConfig:
    def __init__(
        self,
        before: Optional[Signature] = None,
        until: Optional[Signature] = None,
        limit: Optional[int] = None,
        commitment: Optional[CommitmentLevel] = None,
        min_context_slot: Optional[int] = None,
    ): ...
    @property
    def before(self) -> Optional[Signature]: ...
    @property
    def until(self) -> Optional[Signature]: ...
    @property
    def limit(self) -> Optional[int]: ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def min_context_slot(self) -> Optional[int]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcSignaturesForAddressConfig", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcSignaturesForAddressConfig": ...
    @staticmethod
    def default() -> "RpcSignaturesForAddressConfig": ...

class RpcBlockConfig:
    def __init__(
        self,
        encoding: Optional[UiTransactionEncoding] = None,
        transaction_details: Optional[TransactionDetails] = None,
        rewards: Optional[bool] = None,
        commitment: Optional[CommitmentLevel] = None,
        max_supported_transaction_version: Optional[int] = None,
    ): ...
    @property
    def encoding(self) -> Optional[UiTransactionEncoding]: ...
    @property
    def transaction_details(self) -> Optional[TransactionDetails]: ...
    @property
    def rewards(self) -> Optional[bool]: ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def max_supported_transaction_version(self) -> Optional[int]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcBlockConfig", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcBlockConfig": ...
    @staticmethod
    def default() -> "RpcBlockConfig": ...
    @staticmethod
    def rewards_only() -> "RpcBlockConfig": ...
    @staticmethod
    def rewards_with_commitment(
        commitment: Optional[CommitmentLevel] = None,
    ) -> "RpcBlockConfig": ...

class RpcTransactionConfig:
    def __init__(
        self,
        encoding: Optional[UiTransactionEncoding] = None,
        commitment: Optional[CommitmentLevel] = None,
        max_supported_transaction_version: Optional[int] = None,
    ): ...
    @property
    def encoding(self) -> Optional[UiTransactionEncoding]: ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def max_supported_transaction_version(self) -> Optional[int]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcTransactionConfig", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcTransactionConfig": ...
    @staticmethod
    def default() -> "RpcTransactionConfig": ...

class RpcContextConfig:
    def __init__(
        self,
        commitment: Optional[CommitmentLevel] = None,
        min_context_slot: Optional[int] = None,
    ): ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def min_context_slot(self) -> Optional[int]: ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "RpcContextConfig", op: int) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "RpcContextConfig": ...
    @staticmethod
    def default() -> "RpcContextConfig": ...

RPCError = Union[
    RpcCustomErrorFieldless,
    BlockCleanedUpMessage,
    SendTransactionPreflightFailureMessage,
    BlockNotAvailableMessage,
    NodeUnhealthyMessage,
    TransactionPrecompileVerificationFailureMessage,
    SlotSkippedMessage,
    LongTermStorageSlotSkippedMessage,
    KeyExcludedFromSecondaryIndexMessage,
    ScanErrorMessage,
    BlockStatusNotAvailableYetMessage,
    MinContextSlotNotReachedMessage,
    UnsupportedTransactionVersionMessage,
    ParseErrorMessage,
    InvalidRequestMessage,
    MethodNotFoundMessage,
    InvalidParamsMessage,
    InternalErrorMessage,
]

T = TypeVar("T")
Resp = Union[RPCError, T]

class RpcResponseContext:
    slot: int
    api_version: Optional[str]
    def __init__(self, slot: int, api_version: Optional[str] = None) -> None: ...

class GetAccountInfoResp:
    context: RpcResponseContext
    value: Optional[Account]
    def __init__(
        self, value: Optional[Account], context: RpcResponseContext
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetAccountInfoResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetAccountInfoResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetAccountInfoJsonParsedResp:
    context: RpcResponseContext
    value: Optional[AccountJSON]
    def __init__(
        self, value: Optional[AccountJSON], context: RpcResponseContext
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetAccountInfoJsonParsedResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetAccountInfoJsonParsedResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetAccountInfoMaybeJsonParsedResp:
    context: RpcResponseContext
    value: Optional[Union[AccountJSON, Account]]
    def __init__(
        self, value: Optional[Union[AccountJSON, Account]], context: RpcResponseContext
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetAccountInfoMaybeJsonParsedResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetAccountInfoMaybeJsonParsedResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetBalanceResp:
    context: RpcResponseContext
    value: int
    def __init__(self, value: int, context: RpcResponseContext) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetBalanceResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetBalanceResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcBlockCommitment:
    total_stake: int
    commitment: Optional[List[int]]
    def __init__(
        self, total_stake: int, commitment: Optional[Sequence[int]]
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcBlockCommitment: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcBlockCommitment: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetBlockCommitmentResp:
    def __init__(self, value: RpcBlockCommitment) -> None: ...
    def to_json(self) -> str: ...
    @property
    def value(self) -> RpcBlockCommitment: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetBlockCommitmentResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetBlockCommitmentResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetBlockHeightResp:
    def __init__(self, value: int) -> None: ...
    @property
    def value(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetBlockHeightResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetBlockHeightResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcBlockProductionRange:
    def __init__(
        self,
        first_slot: int,
        last_slot: int,
    ) -> None: ...
    @property
    def first_slot(self) -> int: ...
    @property
    def last_slot(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcBlockProductionRange: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcBlockProductionRange: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcBlockProduction:
    def __init__(
        self,
        by_identity: Dict[Pubkey, Tuple[int, int]],
        range: RpcBlockProductionRange,
    ) -> None: ...
    @property
    def by_identity(self) -> Dict[Pubkey, Tuple[int, int]]: ...
    @property
    def range(self) -> RpcBlockProductionRange: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcBlockProduction: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcBlockProduction: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetBlockProductionResp:
    value: RpcBlockProduction
    context: RpcResponseContext
    def __init__(
        self, value: RpcBlockProduction, context: RpcResponseContext
    ) -> None: ...
    @property
    def height(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetBlockProductionResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetBlockProductionResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetBlockResp:
    def __init__(self, value: UiConfirmedBlock) -> None: ...
    @property
    def value(self) -> UiConfirmedBlock: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetBlockResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetBlockResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetBlocksResp:
    def __init__(self, value: List[int]) -> None: ...
    @property
    def value(self) -> List[int]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetBlocksResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetBlocksResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetBlocksWithLimitResp:
    def __init__(self, value: List[int]) -> None: ...
    @property
    def value(self) -> List[int]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetBlocksWithLimitResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetBlocksWithLimitResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetBlockTimeResp:
    def __init__(self, value: Optional[int] = None) -> None: ...
    @property
    def value(self) -> Optional[int]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetBlockTimeResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetBlockTimeResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcContactInfo:
    pubkey: Pubkey
    gossip: Optional[str]
    tpu: Optional[str]
    tpu_quic: Optional[str]
    rpc: Optional[str]
    pubsub: Optional[str]
    version: Optional[str]
    feature_set: Optional[int]
    shred_version: Optional[int]
    tvu: Optional[str]
    tpu_forwards: Optional[str]
    tpu_forwards_quic: Optional[str]
    tpu_vote: Optional[str]
    serve_repair: Optional[str]
    def __init__(
        self,
        pubkey: Pubkey,
        gossip: Optional[str] = None,
        tpu: Optional[str] = None,
        tpu_quic: Optional[str] = None,
        rpc: Optional[str] = None,
        pubsub: Optional[str] = None,
        version: Optional[str] = None,
        feature_set: Optional[int] = None,
        shred_version: Optional[int] = None,
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcContactInfo: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcContactInfo: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetClusterNodesResp:
    def __init__(self, value: Sequence[RpcContactInfo]) -> None: ...
    @property
    def value(self) -> List[RpcContactInfo]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetClusterNodesResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetClusterNodesResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetEpochInfoResp:
    def __init__(self, value: EpochInfo) -> None: ...
    @property
    def value(self) -> EpochInfo: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetEpochInfoResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetEpochInfoResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetEpochScheduleResp:
    def __init__(self, value: EpochSchedule) -> None: ...
    @property
    def value(self) -> EpochSchedule: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetEpochScheduleResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetEpochScheduleResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetFeeForMessageResp:
    context: RpcResponseContext
    value: Optional[int]
    def __init__(self, value: Optional[int], context: RpcResponseContext) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetFeeForMessageResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetFeeForMessageResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetFirstAvailableBlockResp:
    def __init__(self, value: int) -> None: ...
    @property
    def value(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetFirstAvailableBlockResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetFirstAvailableBlockResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetGenesisHashResp:
    def __init__(self, value: Hash) -> None: ...
    @property
    def value(self) -> Hash: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetGenesisHashResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetGenesisHashResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetHealthResp:
    def __init__(self, value: str) -> None: ...
    @property
    def value(self) -> str: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetHealthResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetHealthResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcSimulateTransactionResult:
    err: Optional[TransactionErrorType]
    logs: Optional[List[str]]
    accounts: Optional[List[Optional[Account]]]
    units_consumed: Optional[int]
    return_data: Optional[TransactionReturnData]
    inner_instructions: Optional[List[UiInnerInstructions]]
    def __init__(
        self,
        err: Optional[TransactionErrorType] = None,
        logs: Optional[Sequence[str]] = None,
        accounts: Optional[Sequence[Optional[Account]]] = None,
        units_consumed: Optional[int] = None,
        return_data: Optional[TransactionReturnData] = None,
        inner_instructions: Optional[List[UiInnerInstructions]] = None,
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcSimulateTransactionResult: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcSimulateTransactionResult: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcSnapshotSlotInfo:
    def __init__(self, full: int, incremental: Optional[int] = None) -> None: ...
    @property
    def full(self) -> int: ...
    @property
    def incremental(self) -> Optional[int]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcSnapshotSlotInfo: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcSnapshotSlotInfo: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetHighestSnapshotSlotResp:
    def __init__(self, value: RpcSnapshotSlotInfo) -> None: ...
    @property
    def value(self) -> RpcSnapshotSlotInfo: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetHighestSnapshotSlotResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetHighestSnapshotSlotResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcIdentity:
    identity: Pubkey
    def __init__(self, identity: Pubkey) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcIdentity: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcIdentity: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetIdentityResp:
    def __init__(self, value: RpcIdentity) -> None: ...
    @property
    def value(self) -> RpcIdentity: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetIdentityResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetIdentityResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcInflationGovernor:
    def __init__(
        self,
        initial: float,
        terminal: float,
        taper: float,
        foundation: float,
        foundation_term: float,
    ) -> None: ...
    @staticmethod
    def from_json(raw: str) -> RpcInflationGovernor: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcInflationGovernor: ...
    @property
    def initial(self) -> float: ...
    @property
    def terminal(self) -> float: ...
    @property
    def taper(self) -> float: ...
    @property
    def foundation(self) -> float: ...
    @property
    def foundation_term(self) -> float: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetInflationGovernorResp:
    def __init__(self, value: RpcInflationGovernor) -> None: ...
    @property
    def value(self) -> RpcInflationGovernor: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetInflationGovernorResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetInflationGovernorResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcInflationRate:
    def __init__(
        self, total: float, validator: float, foundation: float, epoch: int
    ) -> None: ...
    @staticmethod
    def from_json(raw: str) -> RpcInflationRate: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcInflationRate: ...
    @property
    def total(self) -> float: ...
    @property
    def validator(self) -> float: ...
    @property
    def foundation(self) -> float: ...
    @property
    def epoch(self) -> int: ...
    @property
    def foundation_term(self) -> float: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetInflationRateResp:
    def __init__(self, value: RpcInflationRate) -> None: ...
    @property
    def value(self) -> RpcInflationRate: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetInflationRateResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetInflationRateResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcInflationReward:
    def __init__(
        self,
        epoch: int,
        effective_slot: int,
        amount: int,
        post_balance: int,
        commission: Optional[int] = None,
    ) -> None: ...
    @staticmethod
    def from_json(raw: str) -> RpcInflationReward: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcInflationReward: ...
    @property
    def epoch(self) -> int: ...
    @property
    def effective_slot(self) -> int: ...
    @property
    def amount(self) -> int: ...
    @property
    def post_balance(self) -> int: ...
    @property
    def commission(self) -> Optional[int]: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetInflationRewardResp:
    def __init__(self, value: Sequence[Optional[RpcInflationReward]]) -> None: ...
    @property
    def value(self) -> Sequence[Optional[RpcInflationReward]]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetInflationRewardResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetInflationRewardResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcAccountBalance:
    def __init__(
        self,
        address: Pubkey,
        lamports: int,
    ) -> None: ...
    @staticmethod
    def from_json(raw: str) -> RpcAccountBalance: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcAccountBalance: ...
    @property
    def address(self) -> Pubkey: ...
    @property
    def lamports(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetLargestAccountsResp:
    context: RpcResponseContext
    value: List[RpcAccountBalance]
    def __init__(
        self, value: Sequence[RpcAccountBalance], context: RpcResponseContext
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetLargestAccountsResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetLargestAccountsResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcBlockhash:
    blockhash: Hash
    last_valid_block_height: int
    def __init__(self, blockhash: Hash, last_valid_block_height: int) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcBlockhash: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcBlockhash: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetLatestBlockhashResp:
    context: RpcResponseContext
    value: RpcBlockhash
    def __init__(self, value: RpcBlockhash, context: RpcResponseContext) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetLatestBlockhashResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetLatestBlockhashResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetLeaderScheduleResp:
    def __init__(self, value: Optional[Dict[Pubkey, Sequence[int]]] = None) -> None: ...
    @property
    def value(self) -> Optional[Dict[Pubkey, List[int]]]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetLatestBlockhashResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetLatestBlockhashResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetMaxRetransmitSlotResp:
    def __init__(self, value: int) -> None: ...
    @property
    def value(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetMaxRetransmitSlotResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetMaxRetransmitSlotResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetMaxShredInsertSlotResp:
    def __init__(self, value: int) -> None: ...
    @property
    def value(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetMaxShredInsertSlotResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetMaxShredInsertSlotResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetMinimumBalanceForRentExemptionResp:
    def __init__(self, value: int) -> None: ...
    @property
    def value(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetMinimumBalanceForRentExemptionResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetMinimumBalanceForRentExemptionResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetMultipleAccountsResp:
    context: RpcResponseContext
    value: List[Optional[Account]]
    def __init__(
        self, value: Sequence[Optional[Account]], context: RpcResponseContext
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetAccountInfoResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetAccountInfoResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetMultipleAccountsJsonParsedResp:
    context: RpcResponseContext
    value: List[Optional[AccountJSON]]
    def __init__(
        self, value: Sequence[Optional[AccountJSON]], context: RpcResponseContext
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetMultipleAccountsJsonParsedResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetMultipleAccountsJsonParsedResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetMultipleAccountsMaybeJsonParsedResp:
    context: RpcResponseContext
    value: List[Optional[Union[Account, AccountJSON]]]
    def __init__(
        self,
        value: Sequence[Optional[Union[Account, AccountJSON]]],
        context: RpcResponseContext,
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetMultipleAccountsMaybeJsonParsedResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetMultipleAccountsMaybeJsonParsedResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcKeyedAccount:
    pubkey: Pubkey
    account: Account
    def __init__(self, pubkey: Pubkey, account: Account) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcKeyedAccount: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcKeyedAccount: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcKeyedAccountJsonParsed:
    pubkey: Pubkey
    account: AccountJSON
    def __init__(self, pubkey: Pubkey, account: AccountJSON) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcKeyedAccountJsonParsed: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcKeyedAccountJsonParsed: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetProgramAccountsWithContextResp:
    context: RpcResponseContext
    value: List[RpcKeyedAccount]
    def __init__(
        self, value: Sequence[RpcKeyedAccount], context: RpcResponseContext
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetProgramAccountsWithContextResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetProgramAccountsWithContextResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetProgramAccountsWithContextJsonParsedResp:
    context: RpcResponseContext
    value: List[RpcKeyedAccountJsonParsed]
    def __init__(
        self, value: Sequence[RpcKeyedAccountJsonParsed], context: RpcResponseContext
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetProgramAccountsWithContextJsonParsedResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetProgramAccountsWithContextJsonParsedResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetProgramAccountsResp:
    def __init__(self, value: Sequence[RpcKeyedAccount]) -> None: ...
    @property
    def value(self) -> List[RpcKeyedAccount]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetProgramAccountsResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetProgramAccountsResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetProgramAccountsJsonParsedResp:
    def __init__(self, value: Sequence[RpcKeyedAccountJsonParsed]) -> None: ...
    @property
    def value(self) -> List[RpcKeyedAccountJsonParsed]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetProgramAccountsJsonParsedResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetProgramAccountsJsonParsedResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetProgramAccountsWithContextMaybeJsonParsedResp:
    context: RpcResponseContext
    value: List[Union[RpcKeyedAccountJsonParsed, RpcKeyedAccount]]
    def __init__(
        self,
        value: Sequence[Union[RpcKeyedAccountJsonParsed, RpcKeyedAccount]],
        context: RpcResponseContext,
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(
        raw: str,
    ) -> Resp[GetProgramAccountsWithContextMaybeJsonParsedResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetProgramAccountsWithContextMaybeJsonParsedResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetProgramAccountsMaybeJsonParsedResp:
    def __init__(
        self, value: Sequence[Union[RpcKeyedAccountJsonParsed, RpcKeyedAccount]]
    ) -> None: ...
    @property
    def value(self) -> List[Union[RpcKeyedAccountJsonParsed, RpcKeyedAccount]]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetProgramAccountsMaybeJsonParsedResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetProgramAccountsMaybeJsonParsedResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcPerfSample:
    def __init__(
        self,
        slot: int,
        num_transactions: int,
        num_slots: int,
        sample_period_secs: int,
        num_non_vote_transactions: Optional[int] = None,
    ) -> None: ...
    @property
    def slot(self) -> int: ...
    @property
    def num_transactions(self) -> int: ...
    @property
    def num_slots(self) -> int: ...
    @property
    def sample_period_secs(self) -> int: ...
    @property
    def num_non_vote_transactions(self) -> Optional[int]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcPerfSample: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcPerfSample: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetRecentPerformanceSamplesResp:
    def __init__(self, value: Sequence[RpcPerfSample]) -> None: ...
    @property
    def value(self) -> List[RpcPerfSample]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetRecentPerformanceSamplesResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetRecentPerformanceSamplesResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcConfirmedTransactionStatusWithSignature:
    def __init__(
        self,
        signature: Signature,
        slot: int,
        err: Optional[TransactionErrorType] = None,
        memo: Optional[str] = None,
        block_time: Optional[int] = None,
        confirmation_status: Optional[TransactionConfirmationStatus] = None,
    ) -> None: ...
    @property
    def signature(self) -> Signature: ...
    @property
    def slot(self) -> int: ...
    @property
    def err(self) -> Optional[TransactionErrorType]: ...
    @property
    def memo(self) -> Optional[str]: ...
    @property
    def block_time(self) -> Optional[int]: ...
    @property
    def confirmation_status(self) -> Optional[TransactionConfirmationStatus]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcConfirmedTransactionStatusWithSignature: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcConfirmedTransactionStatusWithSignature: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetSignaturesForAddressResp:
    def __init__(
        self, value: Sequence[RpcConfirmedTransactionStatusWithSignature]
    ) -> None: ...
    @property
    def value(self) -> List[RpcConfirmedTransactionStatusWithSignature]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetSignaturesForAddressResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetSignaturesForAddressResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetSignatureStatusesResp:
    context: RpcResponseContext
    value: List[Optional[TransactionStatus]]
    def __init__(
        self, value: Sequence[Optional[TransactionStatus]], context: RpcResponseContext
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetSignatureStatusesResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetSignatureStatusesResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetSlotResp:
    def __init__(self, value: int) -> None: ...
    @property
    def value(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetSlotResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetSlotResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetSlotLeaderResp:
    def __init__(self, value: Pubkey) -> None: ...
    @property
    def value(self) -> Pubkey: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetSlotLeaderResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetSlotLeaderResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetSlotLeadersResp:
    def __init__(self, value: Sequence[Pubkey]) -> None: ...
    @property
    def value(self) -> List[Pubkey]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetSlotLeadersResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetSlotLeadersResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcSupply:
    def __init__(
        self,
        total: int,
        circulating: int,
        non_circulating: int,
        non_circulating_accounts: Sequence[Pubkey],
    ) -> None: ...
    @property
    def total(self) -> int: ...
    @property
    def circulating(self) -> int: ...
    @property
    def non_circulating(self) -> int: ...
    @property
    def non_circulating_accounts(self) -> Sequence[Pubkey]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcSupply: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcSupply: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetSupplyResp:
    context: RpcResponseContext
    value: RpcSupply
    def __init__(
        self, value: Sequence[Optional[TransactionStatus]], context: RpcResponseContext
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetSupplyResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetSupplyResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetTokenAccountBalanceResp:
    context: RpcResponseContext
    value: UiTokenAmount
    def __init__(self, value: UiTokenAmount, context: RpcResponseContext) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetTokenAccountBalanceResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetTokenAccountBalanceResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetTokenAccountsByDelegateResp:
    context: RpcResponseContext
    value: List[RpcKeyedAccount]
    def __init__(
        self, value: Sequence[RpcKeyedAccount], context: RpcResponseContext
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetTokenAccountsByDelegateResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetTokenAccountsByDelegateResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetTokenAccountsByDelegateJsonParsedResp:
    context: RpcResponseContext
    value: List[RpcKeyedAccountJsonParsed]
    def __init__(
        self, value: Sequence[RpcKeyedAccountJsonParsed], context: RpcResponseContext
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetTokenAccountsByDelegateJsonParsedResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetTokenAccountsByDelegateJsonParsedResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetTokenAccountsByOwnerResp:
    context: RpcResponseContext
    value: List[RpcKeyedAccount]
    def __init__(
        self, value: Sequence[RpcKeyedAccount], context: RpcResponseContext
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetTokenAccountsByOwnerResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetTokenAccountsByOwnerResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetTokenAccountsByOwnerJsonParsedResp:
    context: RpcResponseContext
    value: List[RpcKeyedAccountJsonParsed]
    def __init__(
        self, value: Sequence[RpcKeyedAccountJsonParsed], context: RpcResponseContext
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetTokenAccountsByOwnerJsonParsedResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetTokenAccountsByOwnerJsonParsedResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcTokenAccountBalance:
    def __init__(self, address: Pubkey, amount: UiTokenAmount) -> None: ...
    @property
    def address(self) -> Pubkey: ...
    @property
    def amount(self) -> UiTokenAmount: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcTokenAccountBalance: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcTokenAccountBalance: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetTokenLargestAccountsResp:
    context: RpcResponseContext
    value: List[RpcTokenAccountBalance]
    def __init__(
        self, value: Sequence[RpcTokenAccountBalance], context: RpcResponseContext
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetTokenLargestAccountsResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetTokenLargestAccountsResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetTokenSupplyResp:
    context: RpcResponseContext
    value: UiTokenAmount
    def __init__(self, value: UiTokenAmount, context: RpcResponseContext) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetTokenSupplyResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetTokenSupplyResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetTransactionResp:
    def __init__(
        self, value: Optional[EncodedConfirmedTransactionWithStatusMeta]
    ) -> None: ...
    @property
    def value(self) -> Optional[EncodedConfirmedTransactionWithStatusMeta]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetTransactionResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetTransactionResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetTransactionCountResp:
    def __init__(self, value: int) -> None: ...
    @property
    def value(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetTransactionCountResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetTransactionCountResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcVersionInfo:
    def __init__(self, solana_core: str, feature_set: Optional[int] = None) -> None: ...
    @property
    def solana_core(self) -> str: ...
    @property
    def feature_set(self) -> Optional[int]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcVersionInfo: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcVersionInfo: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetVersionResp:
    def __init__(self, value: RpcVersionInfo) -> None: ...
    @property
    def value(self) -> RpcVersionInfo: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetVersionResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetVersionResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcVoteAccountInfo:
    def __init__(
        self,
        vote_pubkey: Pubkey,
        node_pubkey: Pubkey,
        activated_stake: int,
        commission: int,
        epoch_vote_account: bool,
        epoch_credits: Sequence[Tuple[int, int, int]],
        last_vote: int,
        root_slot: int,
    ) -> None: ...
    @property
    def vote_pubkey(self) -> Pubkey: ...
    @property
    def node_pubkey(self) -> Pubkey: ...
    @property
    def activated_stake(self) -> int: ...
    @property
    def commission(self) -> int: ...
    @property
    def epoch_vote_account(self) -> bool: ...
    @property
    def epoch_credits(self) -> List[Tuple[int, int, int]]: ...
    @property
    def last_vote(self) -> int: ...
    @property
    def root_slot(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcVersionInfo: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcVersionInfo: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcVoteAccountStatus:
    def __init__(
        self,
        current: Sequence[RpcVoteAccountInfo],
        delinquent: Sequence[RpcVoteAccountInfo],
    ) -> None: ...
    @property
    def current(self) -> List[RpcVoteAccountInfo]: ...
    @property
    def delinquent(self) -> List[RpcVoteAccountInfo]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcVersionInfo: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcVersionInfo: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetVoteAccountsResp:
    def __init__(self, value: RpcVoteAccountStatus) -> None: ...
    @property
    def value(self) -> RpcVoteAccountStatus: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[GetVoteAccountsResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> GetVoteAccountsResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class IsBlockhashValidResp:
    context: RpcResponseContext
    value: bool
    def __init__(self, value: bool) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[IsBlockhashValidResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> IsBlockhashValidResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class MinimumLedgerSlotResp:
    def __init__(self, value: int) -> None: ...
    @property
    def value(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[MinimumLedgerSlotResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> MinimumLedgerSlotResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RequestAirdropResp:
    def __init__(self, value: Signature) -> None: ...
    @property
    def value(self) -> Signature: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[RequestAirdropResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> RequestAirdropResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class ValidatorExitResp:
    def __init__(self, value: bool) -> None: ...
    @property
    def value(self) -> bool: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[ValidatorExitResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> ValidatorExitResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SendTransactionResp:
    def __init__(self, value: Signature) -> None: ...
    @property
    def value(self) -> Signature: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[SendTransactionResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> SendTransactionResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SimulateTransactionResp:
    context: RpcResponseContext
    value: RpcSimulateTransactionResult
    def __init__(self, value: RpcSimulateTransactionResult) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> Resp[SimulateTransactionResp]: ...
    @staticmethod
    def from_bytes(data: bytes) -> SimulateTransactionResp: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcLogsResponse:
    def __init__(
        self,
        signature: Signature,
        err: Optional[TransactionErrorType],
        logs: Sequence[str],
    ) -> None: ...
    @property
    def signature(self) -> Signature: ...
    @property
    def err(self) -> Optional[TransactionErrorType]: ...
    @property
    def logs(self) -> List[str]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcLogsResponse: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcLogsResponse: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcVote:
    def __init__(
        self,
        vote_pubkey: Pubkey,
        slots: Sequence[int],
        hash: Hash,
        timestamp: Optional[int],
        signature: Signature,
    ) -> None: ...
    @property
    def vote_pubkey(self) -> Pubkey: ...
    @property
    def slots(self) -> List[int]: ...
    @property
    def hash(self) -> Hash: ...
    @property
    def timestamp(self) -> Optional[int]: ...
    @property
    def signature(self) -> Signature: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcVote: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcVote: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SlotTransactionStats:
    def __init__(
        self,
        num_transaction_entries: int,
        num_successful_transactions: int,
        num_failed_transactions: int,
        max_transactions_per_entry: int,
    ) -> None: ...
    @property
    def num_transaction_entries(self) -> int: ...
    @property
    def num_successful_transactions(self) -> int: ...
    @property
    def num_failed_transactions(self) -> int: ...
    @property
    def max_transactions_per_entry(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> SlotTransactionStats: ...
    @staticmethod
    def from_bytes(data: bytes) -> SlotTransactionStats: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SlotUpdateFirstShredReceived:
    slot: int
    timestamp: int
    def __init__(
        self,
        slot: int,
        timestamp: int,
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> SlotUpdateFirstShredReceived: ...
    @staticmethod
    def from_bytes(data: bytes) -> SlotUpdateFirstShredReceived: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SlotUpdateCompleted:
    slot: int
    timestamp: int
    def __init__(
        self,
        slot: int,
        timestamp: int,
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> SlotUpdateCompleted: ...
    @staticmethod
    def from_bytes(data: bytes) -> SlotUpdateCompleted: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SlotUpdateCreatedBank:
    slot: int
    timestamp: int
    parent: int
    def __init__(
        self,
        slot: int,
        timestamp: int,
        parent: int,
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> SlotUpdateCreatedBank: ...
    @staticmethod
    def from_bytes(data: bytes) -> SlotUpdateCreatedBank: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SlotUpdateDead:
    slot: int
    timestamp: int
    err: str
    def __init__(
        self,
        slot: int,
        timestamp: int,
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> SlotUpdateDead: ...
    @staticmethod
    def from_bytes(data: bytes) -> SlotUpdateDead: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SlotUpdateOptimisticConfirmation:
    slot: int
    timestamp: int
    def __init__(
        self,
        slot: int,
        timestamp: int,
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> SlotUpdateOptimisticConfirmation: ...
    @staticmethod
    def from_bytes(data: bytes) -> SlotUpdateOptimisticConfirmation: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SlotUpdateRoot:
    slot: int
    timestamp: int
    def __init__(
        self,
        slot: int,
        timestamp: int,
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> SlotUpdateRoot: ...
    @staticmethod
    def from_bytes(data: bytes) -> SlotUpdateRoot: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SlotUpdateFrozen:
    slot: int
    timestamp: int
    stats: SlotTransactionStats
    def __init__(
        self,
        slot: int,
        timestamp: int,
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> SlotUpdateFrozen: ...
    @staticmethod
    def from_bytes(data: bytes) -> SlotUpdateFrozen: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class AccountNotificationResult:
    context: RpcResponseContext
    value: Account
    def __init__(self, value: Account, context: RpcResponseContext) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> AccountNotificationResult: ...
    @staticmethod
    def from_bytes(data: bytes) -> AccountNotificationResult: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class AccountNotification:
    result: AccountNotificationResult
    subscription: int
    def __init__(
        self, result: AccountNotificationResult, subscription: int
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> AccountNotification: ...
    @staticmethod
    def from_bytes(data: bytes) -> AccountNotification: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class AccountNotificationJsonParsedResult:
    context: RpcResponseContext
    value: AccountJSON
    def __init__(self, value: AccountJSON, context: RpcResponseContext) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> AccountNotificationJsonParsedResult: ...
    @staticmethod
    def from_bytes(data: bytes) -> AccountNotificationJsonParsedResult: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class AccountNotificationJsonParsed:
    result: AccountNotificationJsonParsedResult
    subscription: int
    def __init__(
        self, result: AccountNotificationJsonParsedResult, subscription: int
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> AccountNotificationJsonParsed: ...
    @staticmethod
    def from_bytes(data: bytes) -> AccountNotificationJsonParsed: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class BlockNotificationResult:
    context: RpcResponseContext
    value: RpcBlockUpdate
    def __init__(self, value: RpcBlockUpdate, context: RpcResponseContext) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> BlockNotificationResult: ...
    @staticmethod
    def from_bytes(data: bytes) -> BlockNotificationResult: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class BlockNotification:
    result: BlockNotificationResult
    subscription: int
    def __init__(self, result: BlockNotificationResult, subscription: int) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> BlockNotification: ...
    @staticmethod
    def from_bytes(data: bytes) -> BlockNotification: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class LogsNotificationResult:
    context: RpcResponseContext
    value: RpcLogsResponse
    def __init__(self, value: RpcLogsResponse, context: RpcResponseContext) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> LogsNotificationResult: ...
    @staticmethod
    def from_bytes(data: bytes) -> LogsNotificationResult: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class LogsNotification:
    result: LogsNotificationResult
    subscription: int
    def __init__(self, result: LogsNotificationResult, subscription: int) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> LogsNotification: ...
    @staticmethod
    def from_bytes(data: bytes) -> LogsNotification: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class ProgramNotificationResult:
    context: RpcResponseContext
    value: RpcKeyedAccount
    def __init__(self, value: RpcKeyedAccount, context: RpcResponseContext) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> ProgramNotificationResult: ...
    @staticmethod
    def from_bytes(data: bytes) -> ProgramNotificationResult: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class ProgramNotification:
    result: ProgramNotificationResult
    subscription: int
    def __init__(
        self, result: ProgramNotificationResult, subscription: int
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> ProgramNotification: ...
    @staticmethod
    def from_bytes(data: bytes) -> ProgramNotification: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class ProgramNotificationJsonParsedResult:
    context: RpcResponseContext
    value: RpcKeyedAccountJsonParsed
    def __init__(
        self, value: RpcKeyedAccountJsonParsed, context: RpcResponseContext
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> ProgramNotificationJsonParsedResult: ...
    @staticmethod
    def from_bytes(data: bytes) -> ProgramNotificationJsonParsedResult: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class ProgramNotificationJsonParsed:
    result: ProgramNotificationJsonParsedResult
    subscription: int
    def __init__(
        self, result: ProgramNotificationJsonParsedResult, subscription: int
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> ProgramNotificationJsonParsed: ...
    @staticmethod
    def from_bytes(data: bytes) -> ProgramNotificationJsonParsed: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcSignatureResponse:
    err: Optional[TransactionErrorType]
    def __init__(
        self,
        err: Optional[TransactionErrorType] = None,
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcSignatureResponse: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcSignatureResponse: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SignatureNotificationResult:
    context: RpcResponseContext
    value: RpcSignatureResponse
    def __init__(
        self, value: RpcSignatureResponse, context: RpcResponseContext
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> SignatureNotificationResult: ...
    @staticmethod
    def from_bytes(data: bytes) -> SignatureNotificationResult: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SignatureNotification:
    result: SignatureNotificationResult
    subscription: int
    def __init__(
        self, result: SignatureNotificationResult, subscription: int
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> SignatureNotification: ...
    @staticmethod
    def from_bytes(data: bytes) -> SignatureNotification: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SlotInfo:
    def __init__(self, slot: int, parent: int, root: int) -> None: ...
    @property
    def slot(self) -> int: ...
    @property
    def parent(self) -> int: ...
    @property
    def root(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> SlotInfo: ...
    @staticmethod
    def from_bytes(data: bytes) -> SlotInfo: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SlotNotification:
    result: SlotInfo
    subscription: int
    def __init__(self, result: SlotInfo, subscription: int) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> SlotInfo: ...
    @staticmethod
    def from_bytes(data: bytes) -> SlotInfo: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

SlotUpdate = Union[
    SlotUpdateFirstShredReceived,
    SlotUpdateCompleted,
    SlotUpdateCreatedBank,
    SlotUpdateDead,
    SlotUpdateOptimisticConfirmation,
    SlotUpdateRoot,
    SlotUpdateFrozen,
]

class SlotUpdateNotification:
    result: SlotUpdate
    subscription: int
    def __init__(self, result: SlotUpdate, subscription: int) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> SlotUpdateNotification: ...
    @staticmethod
    def from_bytes(data: bytes) -> SlotUpdateNotification: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RootNotification:
    result: int
    subscription: int
    def __init__(self, result: int, subscription: int) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RootNotification: ...
    @staticmethod
    def from_bytes(data: bytes) -> RootNotification: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class VoteNotification:
    result: RpcVote
    subscription: int
    def __init__(self, result: RpcVote, subscription: int) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> VoteNotification: ...
    @staticmethod
    def from_bytes(data: bytes) -> VoteNotification: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SubscriptionResult:
    id: int
    result: int
    def __init__(
        self,
        id: int,
        result: int,
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> SubscriptionResult: ...
    @staticmethod
    def from_bytes(data: bytes) -> SubscriptionResult: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SubscriptionError:
    id: int
    error: RPCError
    def __init__(
        self,
        id: int,
        error: RPCError,
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> SubscriptionError: ...
    @staticmethod
    def from_bytes(data: bytes) -> SubscriptionError: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class BlockStoreError:
    BlockStoreError: "BlockStoreError"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...

RpcBlockUpdateError = Union[UnsupportedTransactionVersion, BlockStoreError]

class RpcBlockUpdate:
    def __init__(
        self,
        slot: int,
        block: Optional[UiConfirmedBlock] = None,
        err: Optional[RpcBlockUpdateError] = None,
    ) -> None: ...
    @property
    def slot(self) -> int: ...
    @property
    def block(self) -> Optional[UiConfirmedBlock]: ...
    @property
    def err(self) -> Optional[RpcBlockUpdateError]: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> RpcVote: ...
    @staticmethod
    def from_bytes(data: bytes) -> RpcVote: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class UnsubscribeResult:
    id: int
    result: bool
    def __init__(
        self,
        id: int,
        result: int,
    ) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> UnsubscribeResult: ...
    @staticmethod
    def from_bytes(data: bytes) -> UnsubscribeResult: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

RPCResult = Union[
    RPCError,
    GetAccountInfoResp,
    GetAccountInfoJsonParsedResp,
    GetAccountInfoMaybeJsonParsedResp,
    GetBalanceResp,
    GetBlockProductionResp,
    GetBlockResp,
    GetBlockCommitmentResp,
    GetBlockHeightResp,
    GetBlocksResp,
    GetBlocksWithLimitResp,
    GetBlockTimeResp,
    GetClusterNodesResp,
    GetEpochInfoResp,
    GetEpochScheduleResp,
    GetFeeForMessageResp,
    GetFirstAvailableBlockResp,
    GetGenesisHashResp,
    GetHealthResp,
    GetHighestSnapshotSlotResp,
    GetIdentityResp,
    GetInflationGovernorResp,
    GetInflationRateResp,
    GetInflationRewardResp,
    GetLargestAccountsResp,
    GetLatestBlockhashResp,
    GetLeaderScheduleResp,
    GetMaxRetransmitSlotResp,
    GetMaxShredInsertSlotResp,
    GetMinimumBalanceForRentExemptionResp,
    GetMultipleAccountsResp,
    GetMultipleAccountsJsonParsedResp,
    GetMultipleAccountsMaybeJsonParsedResp,
    GetProgramAccountsWithContextResp,
    GetProgramAccountsResp,
    GetProgramAccountsWithContextJsonParsedResp,
    GetProgramAccountsJsonParsedResp,
    GetProgramAccountsMaybeJsonParsedResp,
    GetProgramAccountsWithContextMaybeJsonParsedResp,
    GetRecentPerformanceSamplesResp,
    GetSignaturesForAddressResp,
    GetSignatureStatusesResp,
    GetSlotResp,
    GetSlotLeaderResp,
    GetSlotLeadersResp,
    GetSupplyResp,
    GetTokenAccountBalanceResp,
    GetTokenAccountsByDelegateResp,
    GetTokenAccountsByDelegateJsonParsedResp,
    GetTokenAccountsByOwnerResp,
    GetTokenAccountsByOwnerJsonParsedResp,
    GetTokenLargestAccountsResp,
    GetTokenSupplyResp,
    GetTransactionResp,
    GetTransactionCountResp,
    GetVersionResp,
    RpcVersionInfo,
    GetVoteAccountsResp,
    IsBlockhashValidResp,
    MinimumLedgerSlotResp,
    RequestAirdropResp,
    SendTransactionResp,
    SimulateTransactionResp,
]

Notification = Union[
    AccountNotification,
    AccountNotificationJsonParsed,
    BlockNotification,
    LogsNotification,
    ProgramNotification,
    ProgramNotificationJsonParsed,
    SignatureNotification,
    SlotNotification,
    SlotUpdateNotification,
    RootNotification,
    VoteNotification,
]

WebsocketMessage = Union[
    Notification, SubscriptionResult, SubscriptionError, UnsubscribeResult
]

def batch_responses_to_json(resps: Sequence[RPCResult]) -> str: ...
def batch_responses_from_json(raw: str, parsers: Sequence[Any]) -> List[RPCResult]: ...
def parse_notification(raw: str) -> Notification: ...
def parse_websocket_message(raw: str) -> List[WebsocketMessage]: ...

class BlockCleanedUp:
    slot: int
    first_available_block: int
    def __init__(self, slot: int, first_available_block: int) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "BlockCleanedUp": ...
    @staticmethod
    def from_bytes(data: bytes) -> "BlockCleanedUp": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class BlockCleanedUpMessage:
    message: str
    def __init__(self, message: str) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "BlockCleanedUpMessage": ...
    @staticmethod
    def from_bytes(data: bytes) -> "BlockCleanedUpMessage": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SendTransactionPreflightFailure:
    message: str
    result: RpcSimulateTransactionResult
    def __init__(self, message: str, data: RpcSimulateTransactionResult) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "SendTransactionPreflightFailure": ...
    @staticmethod
    def from_bytes(data: bytes) -> "SendTransactionPreflightFailure": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SendTransactionPreflightFailureMessage:
    message: str
    data: RpcSimulateTransactionResult
    def __init__(self, message: str, data: RpcSimulateTransactionResult) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "SendTransactionPreflightFailureMessage": ...
    @staticmethod
    def from_bytes(data: bytes) -> "SendTransactionPreflightFailureMessage": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RpcCustomErrorFieldless:
    TransactionSignatureVerificationFailure: "RpcCustomErrorFieldless"
    NoSnapshot: "RpcCustomErrorFieldless"
    TransactionHistoryNotAvailable: "RpcCustomErrorFieldless"
    TransactionSignatureLenMismatch: "RpcCustomErrorFieldless"
    Base64Zstd: "RpcCustomErrorFieldless"
    def __int__(self) -> int: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...

class BlockNotAvailable:
    slot: int
    def __init__(self, slot: int) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "BlockNotAvailable": ...
    @staticmethod
    def from_bytes(data: bytes) -> "BlockNotAvailable": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class BlockNotAvailableMessage:
    message: str
    def __init__(self, message: str) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "BlockNotAvailableMessage": ...
    @staticmethod
    def from_bytes(data: bytes) -> "BlockNotAvailableMessage": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class NodeUnhealthy:
    num_slots_behind: Optional[int]
    def __init__(self, num_slots_behind: Optional[int] = None) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "NodeUnhealthy": ...
    @staticmethod
    def from_bytes(data: bytes) -> "NodeUnhealthy": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class NodeUnhealthyMessage:
    message: str
    data: NodeUnhealthy
    def __init__(self, message: str, data: NodeUnhealthy) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "NodeUnhealthyMessage": ...
    @staticmethod
    def from_bytes(data: bytes) -> "NodeUnhealthyMessage": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class TransactionPrecompileVerificationFailure:
    def __init__(self, error: TransactionErrorType) -> None: ...
    def error(self) -> TransactionErrorType: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "TransactionPrecompileVerificationFailure": ...
    @staticmethod
    def from_bytes(data: bytes) -> "TransactionPrecompileVerificationFailure": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class TransactionPrecompileVerificationFailureMessage:
    message: str
    def __init__(self, message: str) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "TransactionPrecompileVerificationFailureMessage": ...
    @staticmethod
    def from_bytes(
        data: bytes,
    ) -> "TransactionPrecompileVerificationFailureMessage": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SlotSkipped:
    slot: int
    def __init__(self, slot: int) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "SlotSkipped": ...
    @staticmethod
    def from_bytes(data: bytes) -> "SlotSkipped": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SlotSkippedMessage:
    message: str
    def __init__(self, message: str) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "SlotSkippedMessage": ...
    @staticmethod
    def from_bytes(data: bytes) -> "SlotSkippedMessage": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class LongTermStorageSlotSkipped:
    slot: int
    def __init__(self, slot: int) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "LongTermStorageSlotSkipped": ...
    @staticmethod
    def from_bytes(data: bytes) -> "LongTermStorageSlotSkipped": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class LongTermStorageSlotSkippedMessage:
    message: str
    def __init__(self, message: str) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "LongTermStorageSlotSkippedMessage": ...
    @staticmethod
    def from_bytes(data: bytes) -> "LongTermStorageSlotSkippedMessage": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class KeyExcludedFromSecondaryIndex:
    index_key: str
    def __init__(self, index_key: str) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "KeyExcludedFromSecondaryIndex": ...
    @staticmethod
    def from_bytes(data: bytes) -> "KeyExcludedFromSecondaryIndex": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class KeyExcludedFromSecondaryIndexMessage:
    message: str
    def __init__(self, message: str) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "KeyExcludedFromSecondaryIndexMessage": ...
    @staticmethod
    def from_bytes(data: bytes) -> "KeyExcludedFromSecondaryIndexMessage": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class ScanError:
    message: str
    def __init__(self, message: str) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "ScanError": ...
    @staticmethod
    def from_bytes(data: bytes) -> "ScanError": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class ScanErrorMessage:
    message: str
    def __init__(self, message: str) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "ScanErrorMessage": ...
    @staticmethod
    def from_bytes(data: bytes) -> "ScanErrorMessage": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class BlockStatusNotAvailableYet:
    slot: int
    def __init__(self, slot: int) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "BlockStatusNotAvailableYet": ...
    @staticmethod
    def from_bytes(data: bytes) -> "BlockStatusNotAvailableYet": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class BlockStatusNotAvailableYetMessage:
    message: str
    def __init__(self, message: str) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "BlockStatusNotAvailableYetMessage": ...
    @staticmethod
    def from_bytes(data: bytes) -> "BlockStatusNotAvailableYetMessage": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class MinContextSlotNotReached:
    context_slot: int
    def __init__(self, context_slot: int) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "MinContextSlotNotReached": ...
    @staticmethod
    def from_bytes(data: bytes) -> "MinContextSlotNotReached": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class MinContextSlotNotReachedMessage:
    message: str
    data: MinContextSlotNotReached
    def __init__(self, message: str, data: MinContextSlotNotReached) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "MinContextSlotNotReachedMessage": ...
    @staticmethod
    def from_bytes(data: bytes) -> "MinContextSlotNotReachedMessage": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class UnsupportedTransactionVersion:
    def __init__(self, value: int) -> None: ...
    def value(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "UnsupportedTransactionVersion": ...
    @staticmethod
    def from_bytes(data: bytes) -> "UnsupportedTransactionVersion": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class UnsupportedTransactionVersionMessage:
    message: str
    def __init__(self, message: str) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "UnsupportedTransactionVersionMessage": ...
    @staticmethod
    def from_bytes(data: bytes) -> "UnsupportedTransactionVersionMessage": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class ParseErrorMessage:
    message: str
    def __init__(self, message: str) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "ParseErrorMessage": ...
    @staticmethod
    def from_bytes(data: bytes) -> "ParseErrorMessage": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class InvalidRequestMessage:
    message: str
    def __init__(self, message: str) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "InvalidRequestMessage": ...
    @staticmethod
    def from_bytes(data: bytes) -> "InvalidRequestMessage": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class MethodNotFoundMessage:
    message: str
    def __init__(self, message: str) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "MethodNotFoundMessage": ...
    @staticmethod
    def from_bytes(data: bytes) -> "MethodNotFoundMessage": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class InvalidParamsMessage:
    message: str
    def __init__(self, message: str) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "InvalidParamsMessage": ...
    @staticmethod
    def from_bytes(data: bytes) -> "InvalidParamsMessage": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class InternalErrorMessage:
    message: str
    def __init__(self, message: str) -> None: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_json(raw: str) -> "InternalErrorMessage": ...
    @staticmethod
    def from_bytes(data: bytes) -> "InternalErrorMessage": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetAccountInfo:
    def __init__(
        self,
        pubkey: Pubkey,
        config: Optional[RpcAccountInfoConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def pubkey(self) -> Pubkey: ...
    @property
    def config(self) -> Optional[RpcAccountInfoConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetAccountInfo": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetBalance:
    def __init__(
        self,
        pubkey: Pubkey,
        config: Optional[RpcContextConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def pubkey(self) -> Pubkey: ...
    @property
    def config(self) -> Optional[RpcContextConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetBalance": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetBlock:
    def __init__(
        self,
        slot: int,
        config: Optional[RpcBlockConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def slot(self) -> int: ...
    @property
    def config(self) -> Optional[RpcBlockConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetBlock": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetBlockHeight:
    def __init__(
        self, config: Optional[RpcContextConfig] = None, id: Optional[int] = None
    ): ...
    @property
    def config(self) -> Optional[RpcContextConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetBlockHeight": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetBlockProduction:
    def __init__(
        self,
        config: Optional[RpcBlockProductionConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def config(self) -> Optional[RpcBlockProductionConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetBlockProduction": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetBlockCommitment:
    def __init__(self, slot: int, id: Optional[int] = None): ...
    @property
    def slot(self) -> int: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetBlockCommitment": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetBlocks:
    def __init__(
        self,
        start: int,
        end: Optional[int] = None,
        commitment: Optional[CommitmentLevel] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def start(self) -> int: ...
    @property
    def end(self) -> Optional[int]: ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetBlocks": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetBlocksWithLimit:
    def __init__(
        self,
        start: int,
        limit: Optional[int] = None,
        commitment: Optional[CommitmentLevel] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def start(self) -> int: ...
    @property
    def limit(self) -> Optional[int]: ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetBlocksWithLimit": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetBlockTime:
    def __init__(self, slot: int, id: Optional[int] = None): ...
    @property
    def slot(self) -> int: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetBlockTime": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetEpochInfo:
    def __init__(
        self, config: Optional[RpcContextConfig] = None, id: Optional[int] = None
    ): ...
    @property
    def config(self) -> Optional[RpcContextConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetEpochInfo": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

__T = TypeVar("__T", MessageV0, Message)

class GetFeeForMessage:
    def __init__(
        self,
        message: __T,
        commitment: Optional[CommitmentLevel] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def message(self) -> __T: ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetFeeForMessage": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetIdentity:
    def __init__(self, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetIdentity": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetInflationGovernor:
    def __init__(
        self, commitment: Optional[CommitmentLevel] = None, id: Optional[int] = None
    ): ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetInflationGovernor": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetInflationReward:
    def __init__(
        self,
        addresses: Sequence[Pubkey],
        config: Optional[RpcEpochConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def addresses(self) -> List[Pubkey]: ...
    @property
    def config(self) -> Optional[RpcEpochConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetInflationReward": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetLargestAccounts:
    def __init__(
        self,
        commitment: Optional[CommitmentLevel] = None,
        filter_: Optional[RpcLargestAccountsFilter] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def filter_(self) -> Optional[RpcLargestAccountsFilter]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetLargestAccounts": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetLatestBlockhash:
    def __init__(
        self, config: Optional[RpcContextConfig] = None, id: Optional[int] = None
    ): ...
    @property
    def config(self) -> Optional[RpcContextConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetLatestBlockhash": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetLeaderSchedule:
    def __init__(
        self,
        slot: Optional[int] = None,
        config: Optional[RpcLeaderScheduleConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def slot(self) -> Optional[int]: ...
    @property
    def config(self) -> Optional[RpcLeaderScheduleConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetLeaderSchedule": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetMinimumBalanceForRentExemption:
    def __init__(
        self,
        length: int,
        commitment: Optional[CommitmentLevel] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def length(self) -> int: ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetMinimumBalanceForRentExemption": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetMultipleAccounts:
    def __init__(
        self,
        accounts: Sequence[Pubkey],
        config: Optional[RpcAccountInfoConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def accounts(self) -> List[Pubkey]: ...
    @property
    def config(self) -> Optional[RpcAccountInfoConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetMultipleAccounts": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetProgramAccounts:
    def __init__(
        self,
        program: Pubkey,
        config: Optional[RpcProgramAccountsConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def program(self) -> Pubkey: ...
    @property
    def config(self) -> Optional[RpcProgramAccountsConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetProgramAccounts": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetRecentPerformanceSamples:
    def __init__(self, limit: Optional[int] = None, id: Optional[int] = None): ...
    @property
    def limit(self) -> Optional[int]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetRecentPerformanceSamples": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetSignaturesForAddress:
    def __init__(
        self,
        address: Pubkey,
        config: Optional[RpcSignaturesForAddressConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def address(self) -> Pubkey: ...
    @property
    def config(self) -> Optional[RpcSignaturesForAddressConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetSignaturesForAddress": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetSignatureStatuses:
    def __init__(
        self,
        signatures: Sequence[Signature],
        config: Optional[RpcSignatureStatusConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def signatures(self) -> List[Signature]: ...
    @property
    def config(self) -> Optional[RpcSignatureStatusConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetSignatureStatuses": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetSlot:
    def __init__(
        self, config: Optional[RpcContextConfig] = None, id: Optional[int] = None
    ): ...
    @property
    def config(self) -> Optional[RpcContextConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetSlot": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetSlotLeader:
    def __init__(
        self, config: Optional[RpcContextConfig] = None, id: Optional[int] = None
    ): ...
    @property
    def config(self) -> Optional[RpcContextConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetSlotLeader": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetSlotLeaders:
    def __init__(self, start: int, limit: int, id: Optional[int] = None): ...
    @property
    def start(self) -> int: ...
    @property
    def limit(self) -> int: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetSlotLeaders": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetStakeActivation:
    def __init__(
        self,
        account: Pubkey,
        config: Optional[RpcEpochConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def account(self) -> Pubkey: ...
    @property
    def config(self) -> Optional[RpcEpochConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetStakeActivation": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetSupply:
    def __init__(
        self, config: Optional[RpcSupplyConfig] = None, id: Optional[int] = None
    ): ...
    @property
    def config(self) -> Optional[RpcSupplyConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetSupply": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetTokenAccountBalance:
    def __init__(
        self,
        account: Pubkey,
        commitment: Optional[CommitmentLevel] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def account(self) -> Pubkey: ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetTokenAccountBalance": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetTokenAccountsByDelegate:
    def __init__(
        self,
        account: Pubkey,
        filter_: Union[RpcTokenAccountsFilterMint, RpcTokenAccountsFilterProgramId],
        config: Optional[RpcAccountInfoConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def account(self) -> Pubkey: ...
    @property
    def filter_(
        self,
    ) -> Union[RpcTokenAccountsFilterMint, RpcTokenAccountsFilterProgramId]: ...
    @property
    def config(self) -> Optional[RpcAccountInfoConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetTokenAccountsByDelegate": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetTokenAccountsByOwner:
    def __init__(
        self,
        account: Pubkey,
        filter_: Union[RpcTokenAccountsFilterMint, RpcTokenAccountsFilterProgramId],
        config: Optional[RpcAccountInfoConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def account(self) -> Pubkey: ...
    @property
    def filter_(
        self,
    ) -> Union[RpcTokenAccountsFilterMint, RpcTokenAccountsFilterProgramId]: ...
    @property
    def config(self) -> Optional[RpcAccountInfoConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetTokenAccountsByOwner": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetTokenLargestAccounts:
    def __init__(
        self,
        mint: Pubkey,
        commitment: Optional[CommitmentLevel] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def mint(self) -> Pubkey: ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetTokenLargestAccounts": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetTokenSupply:
    def __init__(
        self,
        mint: Pubkey,
        commitment: Optional[CommitmentLevel] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def mint(self) -> Pubkey: ...
    @property
    def commitment(self) -> Optional[CommitmentLevel]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetTokenSupply": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetTransaction:
    def __init__(
        self,
        signature: Signature,
        config: Optional[RpcTransactionConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def signature(self) -> Signature: ...
    @property
    def config(self) -> Optional[RpcTransactionConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetTransaction": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetTransactionCount:
    def __init__(
        self, config: Optional[RpcContextConfig] = None, id: Optional[int] = None
    ): ...
    @property
    def config(self) -> Optional[RpcContextConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetTransactionCount": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetVoteAccounts:
    def __init__(
        self,
        config: Optional[RpcGetVoteAccountsConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def config(self) -> Optional[RpcGetVoteAccountsConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetVoteAccounts": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class IsBlockhashValid:
    def __init__(
        self,
        blockhash: Hash,
        config: Optional[RpcContextConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def blockhash(self) -> Hash: ...
    @property
    def config(self) -> Optional[RpcContextConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "IsBlockhashValid": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RequestAirdrop:
    def __init__(
        self,
        pubkey: Pubkey,
        lamports: int,
        config: Optional[RpcRequestAirdropConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def pubkey(self) -> Pubkey: ...
    @property
    def lamports(self) -> int: ...
    @property
    def config(self) -> Optional[RpcRequestAirdropConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "RequestAirdrop": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SendLegacyTransaction:
    def __init__(
        self,
        tx: Transaction,
        config: Optional[RpcSendTransactionConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def tx(self) -> Transaction: ...
    @property
    def config(self) -> Optional[RpcSendTransactionConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "SendLegacyTransaction": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SendRawTransaction:
    def __init__(
        self,
        tx: Union[bytes, Sequence[int]],
        config: Optional[RpcSendTransactionConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def tx(self) -> bytes: ...
    @property
    def config(self) -> Optional[RpcSendTransactionConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "SendRawTransaction": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SendVersionedTransaction:
    def __init__(
        self,
        tx: VersionedTransaction,
        config: Optional[RpcSendTransactionConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def tx(self) -> VersionedTransaction: ...
    @property
    def config(self) -> Optional[RpcSendTransactionConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "SendVersionedTransaction": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SimulateLegacyTransaction:
    def __init__(
        self,
        tx: Transaction,
        config: Optional[RpcSimulateTransactionConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def tx(self) -> Transaction: ...
    @property
    def config(self) -> Optional[RpcSimulateTransactionConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "SimulateLegacyTransaction": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SimulateVersionedTransaction:
    def __init__(
        self,
        tx: VersionedTransaction,
        config: Optional[RpcSimulateTransactionConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def tx(self) -> VersionedTransaction: ...
    @property
    def config(self) -> Optional[RpcSimulateTransactionConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "SimulateVersionedTransaction": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class ValidatorExit:
    def __init__(self, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "ValidatorExit": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class AccountSubscribe:
    def __init__(
        self,
        account: Pubkey,
        config: Optional[RpcAccountInfoConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def account(self) -> Pubkey: ...
    @property
    def config(self) -> Optional[RpcAccountInfoConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "AccountSubscribe": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class BlockSubscribe:
    def __init__(
        self,
        filter_: Union[RpcBlockSubscribeFilter, RpcBlockSubscribeFilterMentions],
        config: Optional[RpcBlockSubscribeConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def filter_(
        self,
    ) -> Union[RpcBlockSubscribeFilter, RpcBlockSubscribeFilterMentions]: ...
    @property
    def config(self) -> Optional[RpcBlockSubscribeConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "BlockSubscribe": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class LogsSubscribe:
    def __init__(
        self,
        filter_: Union[RpcTransactionLogsFilter, RpcTransactionLogsFilterMentions],
        config: Optional[RpcTransactionLogsConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def filter_(
        self,
    ) -> Union[RpcTransactionLogsFilter, RpcTransactionLogsFilterMentions]: ...
    @property
    def config(self) -> Optional[RpcTransactionLogsConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "LogsSubscribe": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class ProgramSubscribe:
    def __init__(
        self,
        program: Pubkey,
        config: Optional[RpcProgramAccountsConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def program(self) -> Pubkey: ...
    @property
    def config(self) -> Optional[RpcProgramAccountsConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "ProgramSubscribe": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SignatureSubscribe:
    def __init__(
        self,
        signature: Signature,
        config: Optional[RpcSignatureSubscribeConfig] = None,
        id: Optional[int] = None,
    ): ...
    @property
    def signature(self) -> Signature: ...
    @property
    def config(self) -> Optional[RpcSignatureSubscribeConfig]: ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "SignatureSubscribe": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetClusterNodes:
    def __init__(self, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetClusterNodes": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetEpochSchedule:
    def __init__(self, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetEpochSchedule": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetFirstAvailableBlock:
    def __init__(self, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetFirstAvailableBlock": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetGenesisHash:
    def __init__(self, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetGenesisHash": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetHealth:
    def __init__(self, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetHealth": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetHighestSnapshotSlot:
    def __init__(self, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetHighestSnapshotSlot": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetInflationRate:
    def __init__(self, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetInflationRate": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetMaxRetransmitSlot:
    def __init__(self, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetMaxRetransmitSlot": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetMaxShredInsertSlot:
    def __init__(self, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetMaxShredInsertSlot": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class GetVersion:
    def __init__(self, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "GetVersion": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class MinimumLedgerSlot:
    def __init__(self, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "MinimumLedgerSlot": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SlotSubscribe:
    def __init__(self, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "SlotSubscribe": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SlotsUpdatesSubscribe:
    def __init__(self, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "SlotsUpdatesSubscribe": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RootSubscribe:
    def __init__(self, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "RootSubscribe": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class VoteSubscribe:
    def __init__(self, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "VoteSubscribe": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class AccountUnsubscribe:
    def __init__(self, subscription_id: int, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    @property
    def subscription_id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "AccountUnsubscribe": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class BlockUnsubscribe:
    def __init__(self, subscription_id: int, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    @property
    def subscription_id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "BlockUnsubscribe": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class LogsUnsubscribe:
    def __init__(self, subscription_id: int, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    @property
    def subscription_id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "LogsUnsubscribe": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class ProgramUnsubscribe:
    def __init__(self, subscription_id: int, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    @property
    def subscription_id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "ProgramUnsubscribe": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SignatureUnsubscribe:
    def __init__(self, subscription_id: int, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    @property
    def subscription_id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "SignatureUnsubscribe": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SlotUnsubscribe:
    def __init__(self, subscription_id: int, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    @property
    def subscription_id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "SlotUnsubscribe": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class SlotsUpdatesUnsubscribe:
    def __init__(self, subscription_id: int, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    @property
    def subscription_id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "SlotsUpdatesUnsubscribe": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class RootUnsubscribe:
    def __init__(self, subscription_id: int, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    @property
    def subscription_id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "RootUnsubscribe": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

class VoteUnsubscribe:
    def __init__(self, subscription_id: int, id: Optional[int] = None): ...
    @property
    def id(self) -> int: ...
    @property
    def subscription_id(self) -> int: ...
    def to_json(self) -> str: ...
    @staticmethod
    def from_bytes(data: bytes) -> "VoteUnsubscribe": ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, o: object) -> bool: ...
    def __bytes__(self) -> bytes: ...
    def __hash__(self) -> int: ...

Body = Union[
    GetAccountInfo,
    GetBalance,
    GetBlock,
    GetBlockHeight,
    GetBlockProduction,
    GetBlockCommitment,
    GetBlocks,
    GetBlocksWithLimit,
    GetBlockTime,
    GetClusterNodes,
    GetEpochInfo,
    GetEpochSchedule,
    GetFeeForMessage,
    GetFirstAvailableBlock,
    GetGenesisHash,
    GetHealth,
    GetHighestSnapshotSlot,
    GetIdentity,
    GetInflationGovernor,
    GetInflationRate,
    GetInflationReward,
    GetLargestAccounts,
    GetLatestBlockhash,
    GetLeaderSchedule,
    GetMaxRetransmitSlot,
    GetMaxShredInsertSlot,
    GetMinimumBalanceForRentExemption,
    GetMultipleAccounts,
    GetProgramAccounts,
    GetRecentPerformanceSamples,
    GetSignaturesForAddress,
    GetSignatureStatuses,
    GetSlot,
    GetSlotLeader,
    GetSlotLeaders,
    GetStakeActivation,
    GetSupply,
    GetTokenAccountBalance,
    GetTokenAccountsByDelegate,
    GetTokenAccountsByOwner,
    GetTokenLargestAccounts,
    GetTokenSupply,
    GetTransaction,
    GetTransactionCount,
    GetVersion,
    GetVoteAccounts,
    IsBlockhashValid,
    MinimumLedgerSlot,
    RequestAirdrop,
    SendLegacyTransaction,
    SendRawTransaction,
    SendVersionedTransaction,
    ValidatorExit,
    AccountSubscribe,
    BlockSubscribe,
    LogsSubscribe,
    ProgramSubscribe,
    SignatureSubscribe,
    SlotSubscribe,
    SlotsUpdatesSubscribe,
    RootSubscribe,
    VoteSubscribe,
    AccountUnsubscribe,
    BlockUnsubscribe,
    LogsUnsubscribe,
    ProgramUnsubscribe,
    SignatureUnsubscribe,
    SimulateLegacyTransaction,
    SimulateVersionedTransaction,
    SlotUnsubscribe,
    SlotsUpdatesUnsubscribe,
    RootUnsubscribe,
    VoteUnsubscribe,
]

def batch_requests_to_json(reqs: Sequence[Body]) -> str: ...
def batch_requests_from_json(raw: str) -> List[Body]: ...

ACCOUNT_STORAGE_OVERHEAD: Final[int]
DEFAULT_BURN_PERCENT: Final[int]
DEFAULT_EXEMPTION_THRESHOLD: Final[float]
DEFAULT_LAMPORTS_PER_BYTE_YEAR: Final[int]

class Rent:
    @property
    def burn_percent(self) -> int: ...
    @property
    def exemption_threshold(self) -> float: ...
    @property
    def lamports_per_byte_year(self) -> int: ...
    def __init__(
        self, burn_percent: int, exemption_threshold: float, lamports_per_byte_year: int
    ) -> None: ...
    def calculate_burn(self, rent_collected: int) -> Tuple[int, int]: ...
    @staticmethod
    def default() -> "Rent": ...
    def due(
        self, balance: int, data_len: int, years_elapsed: float
    ) -> Optional[int]: ...
    def due_amount(self, data_len: int, years_elapsed: float) -> int: ...
    @staticmethod
    def free() -> "Rent": ...
    @staticmethod
    def from_bytes(raw_bytes: bytes) -> "Rent": ...
    @staticmethod
    def from_json(raw: str) -> "Rent": ...
    def is_exempt(self, balance: int, data_len: int) -> bool: ...
    def minimum_balance(self, data_len: int) -> int: ...
    def to_json(self) -> str: ...
    def with_slots_per_epoch(self, slots_per_epoch: int) -> "Rent": ...
    def __bytes__(self) -> bytes: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "Rent", op: int) -> bool: ...

class FeatureSet:
    def __init__(self, active: Dict[Pubkey, int], inactive: Set[Pubkey]) -> None: ...
    @property
    def active(self) -> Dict[Pubkey, int]: ...
    @active.setter
    def active(self, val: Dict[Pubkey, int]) -> None: ...
    @property
    def inactive(self) -> Set[Pubkey]: ...
    @inactive.setter
    def inactive(self, val: Set[Pubkey]) -> None: ...
    @staticmethod
    def default() -> "FeatureSet": ...
    @staticmethod
    def all_enabled() -> "FeatureSet": ...
    def is_active(self, feature_id: Pubkey) -> bool: ...
    def activated_slot(self, feature_id: Pubkey) -> Optional[int]: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __richcmp__(self, other: "FeatureSet", op: int) -> bool: ...

class LiteSVM:
    def __init__(self) -> None: ...
    @staticmethod
    def default() -> "LiteSVM": ...
    def set_compute_budget(self, budget: ComputeBudget) -> None: ...
    def set_sigverify(self, sigverify: bool) -> None: ...
    def set_blockhash_check(self, check: bool) -> None: ...
    def set_sysvars(self) -> None: ...
    def set_builtins(self, feature_set: Optional[FeatureSet]) -> None: ...
    def set_lamports(self, lamports: int) -> None: ...
    def set_spl_programs(self) -> None: ...
    def set_transaction_history(self, capacity: int) -> None: ...
    def set_log_bytes_limit(self, limit: Optional[int]) -> None: ...
    def set_precompiles(self, feature_set: Optional[FeatureSet]) -> None: ...
    def minimum_balance_for_rent_exemption(self, data_len: int) -> int: ...
    def get_account(self, pubkey: Pubkey) -> Optional[Account]: ...
    def set_account(self, pubkey: Pubkey, data: Account) -> None: ...
    def get_balance(self, pubkey: Pubkey) -> Optional[int]: ...
    def latest_blockhash(self) -> Hash: ...
    def get_transaction(self, signature: Signature) -> Optional[TransactionResult]: ...
    def airdrop(self, pubkey: Pubkey, lamports: int) -> TransactionResult: ...
    def add_program_from_file(self, program_id: Pubkey, path: Path) -> None: ...
    def add_program(
        self, program_id: Pubkey, program_bytes: Union[bytes, Sequence[int]]
    ) -> None: ...
    def send_transaction(
        self, tx: Union[Transaction, VersionedTransaction]
    ) -> TransactionResult: ...
    def simulate_transaction(
        self, tx: Union[Transaction, VersionedTransaction]
    ) -> SimulateResult: ...
    def expire_blockhash(self) -> None: ...
    def warp_to_slot(self, slot: int) -> None: ...
    def get_compute_budget(self) -> Optional[ComputeBudget]: ...
    def get_clock(self) -> Clock: ...
    def set_clock(self, clock: Clock) -> None: ...
    def get_rent(self) -> Rent: ...
    def set_rent(self, rent: Rent) -> None: ...
    def get_epoch_rewards(self) -> EpochRewards: ...
    def set_epoch_rewards(self, rewards: EpochRewards) -> None: ...
    def get_epoch_schedule(self) -> EpochSchedule: ...
    def set_epoch_schedule(self, schedule: EpochSchedule) -> None: ...
    def get_last_restart_slot(self) -> int: ...
    def set_last_restart_slot(self, slot: int) -> None: ...
    def get_slot_hashes(self) -> List[Tuple[int, Hash]]: ...
    def set_slot_hashes(self, hashes: Sequence[Tuple[int, Hash]]) -> None: ...
    def get_slot_history(self) -> SlotHistory: ...
    def set_slot_history(self, history: SlotHistory) -> None: ...
    def get_stake_history(self) -> StakeHistory: ...
    def set_stake_history(self, history: StakeHistory) -> None: ...
