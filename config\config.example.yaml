# AI Crypto Trading Agent 配置文件

# 基础配置
app:
  name: "AI Crypto Trading Agent"
  version: "1.0.0"
  debug: false
  host: "0.0.0.0"
  port: 8000

# 数据库配置
database:
  url: "sqlite:///./data/trading_agent.db"
  # url: "postgresql://user:password@localhost/trading_agent"

# Redis配置
redis:
  url: "redis://localhost:6379"

# 区块链RPC配置
blockchain:
  ethereum:
    mainnet_rpc: "https://mainnet.infura.io/v3/YOUR_PROJECT_ID"
    testnet_rpc: "https://goerli.infura.io/v3/YOUR_PROJECT_ID"
    chain_id: 1
    explorer: "https://etherscan.io"
    
  bsc:
    mainnet_rpc: "https://bsc-dataseed.binance.org/"
    testnet_rpc: "https://data-seed-prebsc-1-s1.binance.org:8545/"
    chain_id: 56
    explorer: "https://bscscan.com"
    
  solana:
    mainnet_rpc: "https://api.mainnet-beta.solana.com"
    testnet_rpc: "https://api.testnet.solana.com"
    explorer: "https://solscan.io"

# 钱包配置
wallet:
  # 警告：请妥善保管私钥，建议使用环境变量
  private_key: ""  # 从环境变量 WALLET_PRIVATE_KEY 读取
  mnemonic: ""     # 从环境变量 WALLET_MNEMONIC 读取
  
  # 测试模式（使用测试网络）
  test_mode: true

# 交易配置
trading:
  # 滑点设置
  max_slippage: 0.05  # 5%
  
  # Gas设置
  max_gas_price: 100  # Gwei
  default_gas_limit: 200000
  
  # 交易限制
  min_trade_amount: 0.001  # ETH
  max_trade_amount: 1.0    # ETH

# 风险管理
risk_management:
  # 仓位管理
  max_position_size: 0.1      # 单个代币最大仓位 10%
  max_total_exposure: 0.8     # 总仓位限制 80%
  
  # 损失控制
  max_daily_loss: 0.05        # 日最大损失 5%
  max_drawdown: 0.2           # 最大回撤 20%
  
  # 止损止盈
  stop_loss_percentage: 0.1   # 止损 10%
  take_profit_percentage: 0.2 # 止盈 20%
  
  # 风险评分阈值
  min_risk_score: 0.3         # 最低风险评分
  max_risk_score: 0.8         # 最高风险评分

# 监控配置
monitoring:
  # 监控间隔
  block_monitor_interval: 10    # 秒
  price_check_interval: 5       # 秒
  portfolio_update_interval: 60 # 秒
  
  # 数据保留
  transaction_retention_days: 90
  signal_retention_days: 30
  log_retention_days: 7

# 策略配置
strategies:
  # 动量策略
  momentum:
    enabled: true
    config:
      rsi_period: 14
      rsi_oversold: 30
      rsi_overbought: 70
      volume_threshold: 1.5
      min_confidence: 0.6
      
  # 均值回归策略
  mean_reversion:
    enabled: true
    config:
      bollinger_period: 20
      bollinger_std: 2
      min_deviation: 0.02
      min_confidence: 0.5
      
  # 机器学习策略
  ml_strategy:
    enabled: false
    config:
      model_type: "random_forest"
      n_estimators: 100
      confidence_threshold: 0.6
      retrain_interval: 24  # 小时
      
  # 网格交易策略
  grid_trading:
    enabled: false
    config:
      grid_count: 10
      price_range: 0.2  # 20%
      min_profit: 0.01  # 1%

# 选币配置
token_selection:
  # 基础筛选条件
  min_market_cap: 1000000      # 最小市值 $1M
  min_liquidity: 100000        # 最小流动性 $100K
  min_volume_24h: 50000        # 最小24h交易量 $50K
  min_holders: 100             # 最小持有者数量
  
  # 技术指标筛选
  max_volatility: 0.5          # 最大波动率 50%
  min_price_change: 0.05       # 最小价格变化 5%
  max_price_change: 0.5        # 最大价格变化 50%
  
  # 安全筛选
  require_verified: false      # 是否要求验证代币
  blacklist_enabled: true      # 启用黑名单
  honeypot_check: true         # 蜜罐检测

# API密钥配置
api_keys:
  coingecko: ""               # CoinGecko API密钥
  dexscreener: ""             # DexScreener API密钥
  moralis: ""                 # Moralis API密钥
  alchemy: ""                 # Alchemy API密钥

# 通知配置
notifications:
  # Telegram通知
  telegram:
    enabled: false
    bot_token: ""             # 从环境变量读取
    chat_id: ""               # 从环境变量读取
    
  # 邮件通知
  email:
    enabled: false
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: ""
    password: ""              # 从环境变量读取
    to_email: ""
    
  # Webhook通知
  webhook:
    enabled: false
    url: ""
    secret: ""

# Web自动化配置
web_automation:
  # 浏览器设置
  browser: "chrome"           # chrome, firefox
  headless: true
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  
  # 超时设置
  page_load_timeout: 30       # 秒
  element_wait_timeout: 10    # 秒
  
  # 代理设置
  proxy_enabled: false
  proxy_url: ""

# 日志配置
logging:
  level: "INFO"               # DEBUG, INFO, WARNING, ERROR
  file: "logs/trading_agent.log"
  max_size: "10MB"
  backup_count: 5
  
  # 日志格式
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  
  # 特定模块日志级别
  modules:
    web3: "WARNING"
    urllib3: "WARNING"
    selenium: "WARNING"

# 性能配置
performance:
  # 并发设置
  max_concurrent_requests: 10
  request_timeout: 30         # 秒
  
  # 缓存设置
  cache_enabled: true
  cache_ttl: 300             # 秒
  
  # 数据库连接池
  db_pool_size: 5
  db_max_overflow: 10

# 安全配置
security:
  # API安全
  secret_key: "your-secret-key-change-in-production"
  allowed_hosts: ["*"]
  cors_origins: ["http://localhost:3000"]
  
  # 速率限制
  rate_limit_enabled: true
  requests_per_minute: 60
  
  # 加密设置
  encryption_enabled: true
  encryption_key: ""          # 从环境变量读取
