#!/usr/bin/env python3
"""
AI Crypto Trading Agent 开发环境启动脚本
"""

import os
import sys
import subprocess
import time
import signal
from pathlib import Path


def run_backend():
    """启动后端服务"""
    print("启动后端服务...")
    backend_dir = Path("backend")
    
    if not backend_dir.exists():
        print("错误: backend目录不存在")
        return None
    
    # 检查虚拟环境
    venv_dir = backend_dir / "venv"
    if not venv_dir.exists():
        print("虚拟环境不存在，正在创建...")
        subprocess.run([sys.executable, "-m", "venv", "venv"], cwd=backend_dir)
        
        # 安装依赖
        if os.name == 'nt':  # Windows
            pip_path = venv_dir / "Scripts" / "pip"
        else:  # Unix/Linux/macOS
            pip_path = venv_dir / "bin" / "pip"
        
        subprocess.run([str(pip_path), "install", "-r", "requirements.txt"], cwd=backend_dir)
    
    # 启动后端
    if os.name == 'nt':  # Windows
        python_path = venv_dir / "Scripts" / "python"
    else:  # Unix/Linux/macOS
        python_path = venv_dir / "bin" / "python"
    
    return subprocess.Popen([str(python_path), "main.py"], cwd=backend_dir)


def run_frontend():
    """启动前端服务"""
    print("启动前端服务...")
    frontend_dir = Path("frontend")
    
    if not frontend_dir.exists():
        print("错误: frontend目录不存在")
        return None
    
    # 检查node_modules
    node_modules = frontend_dir / "node_modules"
    if not node_modules.exists():
        print("依赖未安装，正在安装...")
        subprocess.run(["npm", "install"], cwd=frontend_dir)
    
    # 启动前端
    return subprocess.Popen(["npm", "start"], cwd=frontend_dir)


def main():
    """主函数"""
    print("AI Crypto Trading Agent 开发环境启动")
    print("=" * 50)
    
    # 创建必要的目录
    for directory in ["data", "logs", "config"]:
        Path(directory).mkdir(exist_ok=True)
    
    # 检查配置文件
    config_file = Path("config/config.yaml")
    config_example = Path("config/config.example.yaml")
    
    if not config_file.exists() and config_example.exists():
        print("配置文件不存在，正在创建...")
        import shutil
        shutil.copy(config_example, config_file)
        print("已创建配置文件，请根据需要修改 config/config.yaml")
    
    # 检查环境变量文件
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("环境变量文件不存在，正在创建...")
        import shutil
        shutil.copy(env_example, env_file)
        print("已创建环境变量文件，请根据需要修改 .env")
    
    processes = []
    
    try:
        # 启动后端
        backend_process = run_backend()
        if backend_process:
            processes.append(backend_process)
            print("后端服务启动成功 (PID: {})".format(backend_process.pid))
        
        # 等待后端启动
        print("等待后端服务启动...")
        time.sleep(5)
        
        # 启动前端
        frontend_process = run_frontend()
        if frontend_process:
            processes.append(frontend_process)
            print("前端服务启动成功 (PID: {})".format(frontend_process.pid))
        
        print("\n" + "=" * 50)
        print("服务启动完成！")
        print("后端地址: http://localhost:8000")
        print("前端地址: http://localhost:3000")
        print("API文档: http://localhost:8000/docs")
        print("\n按 Ctrl+C 停止服务")
        print("=" * 50)
        
        # 等待用户中断
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n正在停止服务...")
        
        # 停止所有进程
        for process in processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            except Exception as e:
                print(f"停止进程时出错: {e}")
        
        print("所有服务已停止")


if __name__ == "__main__":
    main()
