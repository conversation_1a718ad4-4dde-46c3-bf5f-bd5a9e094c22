#!/usr/bin/env python3
"""
AI Crypto Trading Agent 系统测试脚本
"""

import asyncio
import sys
from pathlib import Path

# 添加backend路径到sys.path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

try:
    from app.core.config import settings
    from app.models.base import create_tables
    from app.services.blockchain_monitor import BlockchainMonitor
    from app.services.strategy_manager import StrategyManager
    from app.services.trading_engine import TradingEngine
    print("✅ 所有模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    sys.exit(1)


async def test_configuration():
    """测试配置"""
    print("\n🔧 测试配置...")
    
    try:
        print(f"应用名称: {settings.APP_NAME}")
        print(f"版本: {settings.VERSION}")
        print(f"调试模式: {settings.DEBUG}")
        print(f"主机: {settings.HOST}")
        print(f"端口: {settings.PORT}")
        print("✅ 配置测试通过")
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


async def test_database():
    """测试数据库"""
    print("\n💾 测试数据库...")
    
    try:
        # 创建数据库表
        create_tables()
        print("✅ 数据库表创建成功")
        return True
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False


async def test_services():
    """测试服务"""
    print("\n🔄 测试服务...")
    
    try:
        # 测试策略管理器
        strategy_manager = StrategyManager()
        print(f"策略数量: {len(strategy_manager.strategies)}")
        
        # 测试区块链监控器
        blockchain_monitor = BlockchainMonitor()
        print("区块链监控器初始化成功")
        
        # 测试交易引擎
        trading_engine = TradingEngine(strategy_manager)
        print("交易引擎初始化成功")
        
        print("✅ 服务测试通过")
        return True
    except Exception as e:
        print(f"❌ 服务测试失败: {e}")
        return False


async def test_api_endpoints():
    """测试API端点"""
    print("\n🌐 测试API端点...")
    
    try:
        import httpx
        
        # 测试健康检查端点
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get("http://localhost:8000/health", timeout=5.0)
                if response.status_code == 200:
                    print("✅ API健康检查通过")
                    return True
                else:
                    print(f"❌ API健康检查失败: {response.status_code}")
                    return False
            except httpx.ConnectError:
                print("⚠️  API服务未运行，跳过API测试")
                return True
    except ImportError:
        print("⚠️  httpx未安装，跳过API测试")
        return True
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False


def test_frontend():
    """测试前端"""
    print("\n🎨 测试前端...")
    
    try:
        frontend_dir = Path("frontend")
        
        # 检查前端目录
        if not frontend_dir.exists():
            print("❌ 前端目录不存在")
            return False
        
        # 检查package.json
        package_json = frontend_dir / "package.json"
        if not package_json.exists():
            print("❌ package.json不存在")
            return False
        
        # 检查node_modules
        node_modules = frontend_dir / "node_modules"
        if not node_modules.exists():
            print("⚠️  node_modules不存在，需要运行 npm install")
        else:
            print("✅ 前端依赖已安装")
        
        print("✅ 前端测试通过")
        return True
    except Exception as e:
        print(f"❌ 前端测试失败: {e}")
        return False


def test_directories():
    """测试目录结构"""
    print("\n📁 测试目录结构...")
    
    required_dirs = [
        "backend",
        "frontend", 
        "config",
        "data",
        "logs"
    ]
    
    missing_dirs = []
    for directory in required_dirs:
        if not Path(directory).exists():
            missing_dirs.append(directory)
    
    if missing_dirs:
        print(f"❌ 缺少目录: {', '.join(missing_dirs)}")
        return False
    else:
        print("✅ 目录结构完整")
        return True


def test_config_files():
    """测试配置文件"""
    print("\n⚙️  测试配置文件...")
    
    config_files = [
        ("config/config.example.yaml", "配置示例文件"),
        (".env.example", "环境变量示例文件"),
        ("backend/requirements.txt", "Python依赖文件"),
        ("frontend/package.json", "Node.js依赖文件")
    ]
    
    missing_files = []
    for file_path, description in config_files:
        if not Path(file_path).exists():
            missing_files.append(f"{description} ({file_path})")
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    else:
        print("✅ 配置文件完整")
        return True


async def main():
    """主测试函数"""
    print("AI Crypto Trading Agent 系统测试")
    print("=" * 50)
    
    tests = [
        ("目录结构", test_directories),
        ("配置文件", test_config_files),
        ("系统配置", test_configuration),
        ("数据库", test_database),
        ("核心服务", test_services),
        ("前端", test_frontend),
        ("API端点", test_api_endpoints),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
        print("\n下一步:")
        print("1. 编辑配置文件: config/config.yaml")
        print("2. 设置环境变量: .env")
        print("3. 启动系统: python start_dev.py")
    else:
        print("⚠️  部分测试失败，请检查上述错误信息。")
        
        if not Path("config/config.yaml").exists():
            print("\n💡 提示: 运行 python start_dev.py 可以自动创建配置文件")


if __name__ == "__main__":
    asyncio.run(main())
