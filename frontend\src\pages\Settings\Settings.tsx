import React from 'react';
import { Box, Typography, Card, CardContent } from '@mui/material';
import { Helmet } from 'react-helmet-async';

const Settings: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>系统设置 - AI Crypto Trading Agent</title>
      </Helmet>
      
      <Box>
        <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
          系统设置
        </Typography>
        
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              系统设置功能开发中...
            </Typography>
            <Typography color="textSecondary">
              此页面将提供系统配置、风险管理、通知设置等功能。
            </Typography>
          </CardContent>
        </Card>
      </Box>
    </>
  );
};

export default Settings;
