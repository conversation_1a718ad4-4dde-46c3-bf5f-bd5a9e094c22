"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.30.
"""

# source: web3/_utils/contract_sources/PayableTester.sol:PayableTesterContract
PAYABLE_TESTER_CONTRACT_BYTECODE = "0x6080604052348015600e575f5ffd5b5060e78061001b5f395ff3fe6080604052348015600e575f5ffd5b50600436106030575f3560e01c8063c6803622146034578063e4cb8f5c14604e575b5f5ffd5b603a6056565b60405160459190609a565b60405180910390f35b60546067565b005b5f5f9054906101000a900460ff1681565b60015f5f6101000a81548160ff021916908315150217905550565b5f8115159050919050565b6094816082565b82525050565b5f60208201905060ab5f830184608d565b9291505056fea2646970667358221220a9b00e8591f184642b3b3dfbbe6465e7a6ad284ba74537a9e2445f737666c8a364736f6c634300081e0033"  # noqa: E501
PAYABLE_TESTER_CONTRACT_RUNTIME = "0x6080604052348015600e575f5ffd5b50600436106030575f3560e01c8063c6803622146034578063e4cb8f5c14604e575b5f5ffd5b603a6056565b60405160459190609a565b60405180910390f35b60546067565b005b5f5f9054906101000a900460ff1681565b60015f5f6101000a81548160ff021916908315150217905550565b5f8115159050919050565b6094816082565b82525050565b5f60208201905060ab5f830184608d565b9291505056fea2646970667358221220a9b00e8591f184642b3b3dfbbe6465e7a6ad284ba74537a9e2445f737666c8a364736f6c634300081e0033"  # noqa: E501
PAYABLE_TESTER_CONTRACT_ABI = [
    {
        "inputs": [],
        "name": "doNoValueCall",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "wasCalled",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "view",
        "type": "function",
    },
]
PAYABLE_TESTER_CONTRACT_DATA = {
    "bytecode": PAYABLE_TESTER_CONTRACT_BYTECODE,
    "bytecode_runtime": PAYABLE_TESTER_CONTRACT_RUNTIME,
    "abi": PAYABLE_TESTER_CONTRACT_ABI,
}
