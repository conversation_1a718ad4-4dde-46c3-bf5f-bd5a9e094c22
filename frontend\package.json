{"name": "ai-crypto-trading-frontend", "version": "1.0.0", "description": "AI Crypto Trading Agent Frontend", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^27.5.2", "@types/node": "^16.18.68", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "recharts": "^2.8.0", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/x-data-grid": "^6.18.2", "@mui/x-date-pickers": "^6.18.2", "dayjs": "^1.11.10", "react-query": "^3.39.3", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.16", "react-helmet-async": "^2.0.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/recharts": "^1.8.29", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0"}, "proxy": "http://localhost:8000"}