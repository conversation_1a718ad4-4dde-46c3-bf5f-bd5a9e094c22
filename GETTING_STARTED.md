# AI Crypto Trading Agent - 快速入门指南

欢迎使用AI加密货币交易机器人！本指南将帮助您快速上手。

## 🚀 快速开始

### 1. 系统测试
首先运行系统测试，确保所有组件正常：
```bash
python test_system.py
```

### 2. 一键启动
使用开发环境启动脚本：
```bash
python start_dev.py
```

### 3. 访问界面
- 前端界面: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

## 📋 首次配置清单

### ✅ 必须配置项

1. **复制配置文件**
   ```bash
   cp config/config.example.yaml config/config.yaml
   cp .env.example .env
   ```

2. **设置钱包信息** (在 `.env` 文件中)
   ```bash
   WALLET_PRIVATE_KEY=your_private_key_here
   # 或者
   WALLET_MNEMONIC=your_mnemonic_phrase_here
   ```

3. **配置RPC节点** (在 `.env` 文件中)
   ```bash
   ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/YOUR_PROJECT_ID
   BSC_RPC_URL=https://bsc-dataseed.binance.org/
   SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
   ```

### 🔧 可选配置项

1. **API密钥** (用于获取价格数据)
   ```bash
   COINGECKO_API_KEY=your_api_key
   DEXSCREENER_API_KEY=your_api_key
   ```

2. **通知设置**
   ```bash
   TELEGRAM_BOT_TOKEN=your_bot_token
   TELEGRAM_CHAT_ID=your_chat_id
   ```

3. **风险管理参数** (在 `config/config.yaml` 中)
   ```yaml
   risk_management:
     max_position_size: 0.1      # 单个代币最大仓位 10%
     max_daily_loss: 0.05        # 日最大损失 5%
     stop_loss_percentage: 0.1   # 止损 10%
   ```

## 🎯 使用流程

### 第一步：添加监控代币
1. 访问前端界面
2. 进入"代币监控"页面
3. 点击"添加代币"
4. 输入代币合约地址和基本信息

### 第二步：配置交易策略
1. 进入"策略配置"页面
2. 选择要启用的策略
3. 调整策略参数
4. 保存配置

### 第三步：启动自动交易
1. 确认风险管理设置
2. 系统会自动监控代币价格
3. 根据策略生成交易信号
4. 自动执行买卖操作

## 🛡️ 安全建议

### 🔐 钱包安全
- **测试环境优先**: 先在测试网络熟悉系统
- **小额测试**: 初期使用小额资金测试
- **硬件钱包**: 生产环境建议使用硬件钱包
- **私钥保护**: 绝不在代码中硬编码私钥

### ⚠️ 风险控制
- **仓位限制**: 单个代币仓位不超过总资产的10%
- **止损设置**: 设置合理的止损点
- **监控系统**: 定期检查系统运行状态
- **备份数据**: 定期备份配置和交易数据

## 📊 监控和维护

### 日常检查
- 查看仪表板了解系统状态
- 检查交易记录和收益情况
- 监控日志文件是否有错误
- 确认网络连接和API正常

### 日志文件位置
- 主日志: `logs/trading_agent.log`
- 错误日志: `logs/trading_agent_error.log`

### 常用命令
```bash
# 查看实时日志
tail -f logs/trading_agent.log

# 重启系统
python start_dev.py

# 系统测试
python test_system.py
```

## 🔧 故障排除

### 常见问题

1. **无法连接到区块链**
   - 检查RPC URL是否正确
   - 确认网络连接正常
   - 验证API密钥有效性

2. **交易失败**
   - 检查钱包余额是否充足
   - 确认Gas费设置合理
   - 验证代币合约地址正确

3. **策略不生成信号**
   - 检查策略配置参数
   - 确认代币有足够的历史数据
   - 验证市场条件是否满足策略要求

4. **前端无法访问**
   - 确认后端服务正在运行
   - 检查端口是否被占用
   - 查看浏览器控制台错误信息

### 获取帮助
1. 查看日志文件定位问题
2. 检查配置文件是否正确
3. 运行系统测试脚本
4. 查看项目文档和FAQ

## 📈 进阶使用

### 自定义策略
1. 在 `backend/app/services/strategy_manager.py` 中创建新策略类
2. 继承 `BaseStrategy` 基类
3. 实现 `analyze` 方法
4. 在策略管理器中注册新策略

### 添加新区块链
1. 在 `app/core/config.py` 中添加链配置
2. 在相关服务中实现链接口
3. 更新前端界面支持新链

### 性能优化
1. 调整监控间隔参数
2. 优化数据库查询
3. 使用缓存减少API调用
4. 配置负载均衡

## 📚 学习资源

- [项目文档](README.md)
- [API文档](http://localhost:8000/docs)
- [策略配置说明](config/strategies.yaml)
- [环境变量说明](.env.example)

## 🎉 开始交易

配置完成后，您就可以开始使用AI交易机器人了！

记住：
- 🧪 先在测试环境熟悉系统
- 💰 使用小额资金开始
- 📊 定期监控和调整策略
- 🛡️ 始终注意风险控制

祝您交易愉快！ 🚀
