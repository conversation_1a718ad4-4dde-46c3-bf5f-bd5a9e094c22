import io
import typing as t

class RestreamedBytesIO(object):
    substream: t.Optional[io.BytesIO]
    encoder: t.Callable[[bytes], bytes]
    encoderunit: int
    decoder: t.Callable[[bytes], bytes]
    decoderunit: int
    rbuffer: bytes
    wbuffer: bytes
    sincereadwritten: int
    def __init__(
        self,
        substream: t.Optional[io.BytesIO],
        decoder: t.Callable[[bytes], bytes],
        decoderunit: int,
        encoder: t.Callable[[bytes], bytes],
        encoderunit: int,
    ) -> None: ...
    def read(self, count: t.Optional[int] = ...) -> bytes: ...
    def write(self, data: t.Union[bytes, bytearray, memoryview]) -> int: ...
    def close(self) -> None: ...
    def seek(self, at: int, whence: int = ...) -> int: ...
    def seekable(self) -> bool: ...
    def tell(self) -> int: ...
    def tellable(self) -> bool: ...

class RebufferedBytesIO(object):
    substream: t.Optional[io.BytesIO]
    offset: int
    rwbuffer: bytes
    moved: int
    tailcutoff: t.Optional[int]
    def __init__(
        self, substream: t.Optional[io.BytesIO], tailcutoff: t.Optional[int] = ...
    ) -> None: ...
    def read(self, count: t.Optional[int] = ...) -> bytes: ...
    def write(self, data: t.Union[bytes, bytearray, memoryview]) -> int: ...
    def seek(self, at: int, whence: int = ...) -> int: ...
    def seekable(self) -> bool: ...
    def tell(self) -> int: ...
    def tellable(self) -> bool: ...
    def cachedfrom(self) -> int: ...
    def cachedto(self) -> int: ...
