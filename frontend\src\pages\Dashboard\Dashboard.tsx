import React from 'react';
import {
  <PERSON>,
  <PERSON>rid,
  Card,
  CardContent,
  Typo<PERSON>,
  Chip,
  LinearProgress,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AccountBalanceWallet as WalletIcon,
  Psychology as StrategyIcon,
  Speed as SpeedIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import { Helmet } from 'react-helmet-async';

// Mock data
const portfolioData = [
  { time: '00:00', value: 10000 },
  { time: '04:00', value: 10200 },
  { time: '08:00', value: 9800 },
  { time: '12:00', value: 10500 },
  { time: '16:00', value: 10800 },
  { time: '20:00', value: 10650 },
  { time: '24:00', value: 10900 },
];

const allocationData = [
  { name: 'ETH', value: 40, color: '#627eea' },
  { name: 'BTC', value: 30, color: '#f7931a' },
  { name: 'BNB', value: 20, color: '#f3ba2f' },
  { name: 'SOL', value: 10, color: '#9945ff' },
];

const recentTrades = [
  { id: 1, symbol: 'ETH/USDT', type: 'BUY', amount: '0.5', price: '2,100', pnl: '****%', status: 'completed' },
  { id: 2, symbol: 'BTC/USDT', type: 'SELL', amount: '0.1', price: '42,000', pnl: '-1.2%', status: 'completed' },
  { id: 3, symbol: 'BNB/USDT', type: 'BUY', amount: '10', price: '300', pnl: '****%', status: 'pending' },
];

const StatCard: React.FC<{
  title: string;
  value: string;
  change: string;
  icon: React.ReactNode;
  positive?: boolean;
}> = ({ title, value, change, icon, positive = true }) => (
  <Card sx={{ height: '100%' }}>
    <CardContent>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <Box>
          <Typography color="textSecondary" gutterBottom variant="body2">
            {title}
          </Typography>
          <Typography variant="h4" component="div" sx={{ fontWeight: 600 }}>
            {value}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            {positive ? (
              <TrendingUpIcon sx={{ color: 'success.main', fontSize: 16, mr: 0.5 }} />
            ) : (
              <TrendingDownIcon sx={{ color: 'error.main', fontSize: 16, mr: 0.5 }} />
            )}
            <Typography
              variant="body2"
              sx={{ color: positive ? 'success.main' : 'error.main' }}
            >
              {change}
            </Typography>
          </Box>
        </Box>
        <Box
          sx={{
            backgroundColor: 'rgba(0, 212, 255, 0.1)',
            borderRadius: '50%',
            p: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          {icon}
        </Box>
      </Box>
    </CardContent>
  </Card>
);

const Dashboard: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>仪表板 - AI Crypto Trading Agent</title>
      </Helmet>
      
      <Box sx={{ flexGrow: 1 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" sx={{ fontWeight: 600 }}>
            交易仪表板
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Chip
              label="系统运行中"
              color="success"
              variant="outlined"
              icon={<SpeedIcon />}
            />
            <IconButton>
              <RefreshIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="总资产价值"
              value="$10,900"
              change="+8.5%"
              icon={<WalletIcon sx={{ color: '#00d4ff' }} />}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="今日收益"
              value="$245"
              change="+2.3%"
              icon={<TrendingUpIcon sx={{ color: '#4caf50' }} />}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="活跃策略"
              value="3"
              change="运行中"
              icon={<StrategyIcon sx={{ color: '#ff9800' }} />}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="成功率"
              value="78.5%"
              change="+1.2%"
              icon={<SpeedIcon sx={{ color: '#9c27b0' }} />}
            />
          </Grid>
        </Grid>

        {/* Charts */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  投资组合价值趋势
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={portfolioData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#2a2f3e" />
                    <XAxis dataKey="time" stroke="#b0bec5" />
                    <YAxis stroke="#b0bec5" />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: '#1a1f2e',
                        border: '1px solid #2a2f3e',
                        borderRadius: '8px',
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="value"
                      stroke="#00d4ff"
                      strokeWidth={2}
                      dot={{ fill: '#00d4ff', strokeWidth: 2, r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  资产分配
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={allocationData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {allocationData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip
                      contentStyle={{
                        backgroundColor: '#1a1f2e',
                        border: '1px solid #2a2f3e',
                        borderRadius: '8px',
                      }}
                    />
                  </PieChart>
                </ResponsiveContainer>
                <Box sx={{ mt: 2 }}>
                  {allocationData.map((item) => (
                    <Box
                      key={item.name}
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        mb: 1,
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box
                          sx={{
                            width: 12,
                            height: 12,
                            backgroundColor: item.color,
                            borderRadius: '50%',
                            mr: 1,
                          }}
                        />
                        <Typography variant="body2">{item.name}</Typography>
                      </Box>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {item.value}%
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Recent Trades */}
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>
              最近交易
            </Typography>
            <TableContainer component={Paper} sx={{ backgroundColor: 'transparent' }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>交易对</TableCell>
                    <TableCell>类型</TableCell>
                    <TableCell>数量</TableCell>
                    <TableCell>价格</TableCell>
                    <TableCell>盈亏</TableCell>
                    <TableCell>状态</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {recentTrades.map((trade) => (
                    <TableRow key={trade.id}>
                      <TableCell>{trade.symbol}</TableCell>
                      <TableCell>
                        <Chip
                          label={trade.type}
                          color={trade.type === 'BUY' ? 'success' : 'error'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{trade.amount}</TableCell>
                      <TableCell>${trade.price}</TableCell>
                      <TableCell>
                        <Typography
                          sx={{
                            color: trade.pnl.startsWith('+') ? 'success.main' : 'error.main',
                          }}
                        >
                          {trade.pnl}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={trade.status}
                          color={trade.status === 'completed' ? 'success' : 'warning'}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Box>
    </>
  );
};

export default Dashboard;
