#!/usr/bin/env python3
"""
AI Crypto Trading Agent 安装脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(command, cwd=None):
    """运行命令"""
    print(f"执行命令: {command}")
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            cwd=cwd,
            capture_output=True,
            text=True
        )
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False


def check_requirements():
    """检查系统要求"""
    print("检查系统要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 9):
        print("错误: 需要Python 3.9或更高版本")
        return False
    
    # 检查Node.js
    if not shutil.which("node"):
        print("错误: 未找到Node.js，请先安装Node.js 16+")
        return False
    
    # 检查npm
    if not shutil.which("npm"):
        print("错误: 未找到npm")
        return False
    
    print("系统要求检查通过")
    return True


def setup_backend():
    """设置后端"""
    print("\n设置后端环境...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("错误: backend目录不存在")
        return False
    
    # 创建虚拟环境
    if not run_command("python -m venv venv", cwd=backend_dir):
        return False
    
    # 激活虚拟环境并安装依赖
    if os.name == 'nt':  # Windows
        activate_cmd = "venv\\Scripts\\activate && pip install -r requirements.txt"
    else:  # Unix/Linux/macOS
        activate_cmd = "source venv/bin/activate && pip install -r requirements.txt"
    
    if not run_command(activate_cmd, cwd=backend_dir):
        return False
    
    print("后端环境设置完成")
    return True


def setup_frontend():
    """设置前端"""
    print("\n设置前端环境...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("错误: frontend目录不存在")
        return False
    
    # 安装依赖
    if not run_command("npm install", cwd=frontend_dir):
        return False
    
    print("前端环境设置完成")
    return True


def setup_directories():
    """创建必要的目录"""
    print("\n创建必要的目录...")
    
    directories = [
        "data",
        "logs",
        "config",
        "static"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"创建目录: {directory}")
    
    print("目录创建完成")


def setup_config():
    """设置配置文件"""
    print("\n设置配置文件...")
    
    config_example = Path("config/config.example.yaml")
    config_file = Path("config/config.yaml")
    
    if config_example.exists() and not config_file.exists():
        shutil.copy(config_example, config_file)
        print("已创建配置文件: config/config.yaml")
        print("请编辑配置文件以设置您的参数")
    
    # 创建环境变量文件
    env_file = Path(".env")
    if not env_file.exists():
        with open(env_file, 'w') as f:
            f.write("""# AI Crypto Trading Agent 环境变量
# 请根据需要修改以下配置

# 钱包配置（请妥善保管）
WALLET_PRIVATE_KEY=
WALLET_MNEMONIC=

# API密钥
COINGECKO_API_KEY=
DEXSCREENER_API_KEY=
MORALIS_API_KEY=
ALCHEMY_API_KEY=

# 通知配置
TELEGRAM_BOT_TOKEN=
TELEGRAM_CHAT_ID=
EMAIL_PASSWORD=

# 安全配置
SECRET_KEY=your-secret-key-change-in-production
ENCRYPTION_KEY=

# 数据库配置
DATABASE_URL=sqlite:///./data/trading_agent.db
REDIS_URL=redis://localhost:6379
""")
        print("已创建环境变量文件: .env")
        print("请编辑 .env 文件以设置您的密钥和配置")


def create_startup_scripts():
    """创建启动脚本"""
    print("\n创建启动脚本...")
    
    # Windows启动脚本
    with open("start.bat", 'w') as f:
        f.write("""@echo off
echo 启动 AI Crypto Trading Agent...

echo 启动后端服务...
cd backend
call venv\\Scripts\\activate
start "Backend" python main.py

echo 等待后端启动...
timeout /t 5

echo 启动前端服务...
cd ..\\frontend
start "Frontend" npm start

echo 服务启动完成！
echo 后端地址: http://localhost:8000
echo 前端地址: http://localhost:3000
pause
""")
    
    # Unix/Linux/macOS启动脚本
    with open("start.sh", 'w') as f:
        f.write("""#!/bin/bash
echo "启动 AI Crypto Trading Agent..."

echo "启动后端服务..."
cd backend
source venv/bin/activate
python main.py &
BACKEND_PID=$!

echo "等待后端启动..."
sleep 5

echo "启动前端服务..."
cd ../frontend
npm start &
FRONTEND_PID=$!

echo "服务启动完成！"
echo "后端地址: http://localhost:8000"
echo "前端地址: http://localhost:3000"
echo "后端PID: $BACKEND_PID"
echo "前端PID: $FRONTEND_PID"

# 等待用户输入以停止服务
read -p "按Enter键停止服务..."
kill $BACKEND_PID $FRONTEND_PID
""")
    
    # 设置执行权限
    if os.name != 'nt':
        os.chmod("start.sh", 0o755)
    
    print("启动脚本创建完成")


def main():
    """主函数"""
    print("AI Crypto Trading Agent 安装程序")
    print("=" * 50)
    
    # 检查系统要求
    if not check_requirements():
        sys.exit(1)
    
    # 设置目录
    setup_directories()
    
    # 设置配置
    setup_config()
    
    # 设置后端
    if not setup_backend():
        print("后端设置失败")
        sys.exit(1)
    
    # 设置前端
    if not setup_frontend():
        print("前端设置失败")
        sys.exit(1)
    
    # 创建启动脚本
    create_startup_scripts()
    
    print("\n" + "=" * 50)
    print("安装完成！")
    print("\n下一步:")
    print("1. 编辑 config/config.yaml 配置文件")
    print("2. 编辑 .env 环境变量文件")
    print("3. 运行启动脚本:")
    if os.name == 'nt':
        print("   Windows: start.bat")
    else:
        print("   Unix/Linux/macOS: ./start.sh")
    print("\n注意: 请妥善保管您的私钥和API密钥！")


if __name__ == "__main__":
    main()
