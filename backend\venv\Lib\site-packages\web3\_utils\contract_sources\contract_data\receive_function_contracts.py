"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.30.
"""

# source: web3/_utils/contract_sources/ReceiveFunctionContracts.sol:ReceiveFunctionContract  # noqa: E501
RECEIVE_FUNCTION_CONTRACT_BYTECODE = "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"  # noqa: E501
RECEIVE_FUNCTION_CONTRACT_RUNTIME = "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"  # noqa: E501
RECEIVE_FUNCTION_CONTRACT_ABI = [
    {"stateMutability": "payable", "type": "fallback"},
    {
        "inputs": [],
        "name": "getText",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "string", "name": "new_text", "type": "string"}],
        "name": "setText",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {"stateMutability": "payable", "type": "receive"},
]
RECEIVE_FUNCTION_CONTRACT_DATA = {
    "bytecode": RECEIVE_FUNCTION_CONTRACT_BYTECODE,
    "bytecode_runtime": RECEIVE_FUNCTION_CONTRACT_RUNTIME,
    "abi": RECEIVE_FUNCTION_CONTRACT_ABI,
}


# source: web3/_utils/contract_sources/ReceiveFunctionContracts.sol:NoReceiveFunctionContract  # noqa: E501
NO_RECEIVE_FUNCTION_CONTRACT_BYTECODE = "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"  # noqa: E501
NO_RECEIVE_FUNCTION_CONTRACT_RUNTIME = "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"  # noqa: E501
NO_RECEIVE_FUNCTION_CONTRACT_ABI = [
    {"stateMutability": "nonpayable", "type": "fallback"},
    {
        "inputs": [],
        "name": "getText",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "string", "name": "new_text", "type": "string"}],
        "name": "setText",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
NO_RECEIVE_FUNCTION_CONTRACT_DATA = {
    "bytecode": NO_RECEIVE_FUNCTION_CONTRACT_BYTECODE,
    "bytecode_runtime": NO_RECEIVE_FUNCTION_CONTRACT_RUNTIME,
    "abi": NO_RECEIVE_FUNCTION_CONTRACT_ABI,
}
