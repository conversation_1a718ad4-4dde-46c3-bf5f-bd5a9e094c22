"""
配置管理模块
"""

from typing import List, Optional
from pydantic import BaseSettings, field_validator


class Settings(BaseSettings):
    """应用配置"""

    # 基础配置
    APP_NAME: str = "AI Crypto Trading Agent"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8000

    # 安全配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALLOWED_HOSTS: List[str] = ["*"]

    # 数据库配置
    DATABASE_URL: str = "sqlite:///./data/trading_agent.db"

    # Redis配置
    REDIS_URL: str = "redis://localhost:6379"

    # 区块链RPC配置
    ETHEREUM_RPC_URL: str = "https://mainnet.infura.io/v3/YOUR_PROJECT_ID"
    BSC_RPC_URL: str = "https://bsc-dataseed.binance.org/"
    SOLANA_RPC_URL: str = "https://api.mainnet-beta.solana.com"

    # 测试网络RPC
    ETHEREUM_TESTNET_RPC: str = "https://goerli.infura.io/v3/YOUR_PROJECT_ID"
    BSC_TESTNET_RPC: str = "https://data-seed-prebsc-1-s1.binance.org:8545/"
    SOLANA_TESTNET_RPC: str = "https://api.testnet.solana.com"

    # 钱包配置
    WALLET_PRIVATE_KEY: Optional[str] = None
    WALLET_MNEMONIC: Optional[str] = None

    # 交易配置
    MAX_SLIPPAGE: float = 0.05  # 5%
    MAX_GAS_PRICE: int = 100  # Gwei
    DEFAULT_GAS_LIMIT: int = 200000

    # 风险管理
    MAX_POSITION_SIZE: float = 0.1  # 10%
    MAX_DAILY_LOSS: float = 0.05  # 5%
    STOP_LOSS_PERCENTAGE: float = 0.1  # 10%
    TAKE_PROFIT_PERCENTAGE: float = 0.2  # 20%

    # 监控配置
    MONITOR_INTERVAL: int = 10  # 秒
    PRICE_CHECK_INTERVAL: int = 5  # 秒

    # API密钥
    COINGECKO_API_KEY: Optional[str] = None
    DEXSCREENER_API_KEY: Optional[str] = None
    TELEGRAM_BOT_TOKEN: Optional[str] = None
    TELEGRAM_CHAT_ID: Optional[str] = None

    # Web自动化配置
    CHROME_DRIVER_PATH: Optional[str] = None
    HEADLESS_BROWSER: bool = True

    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/trading_agent.log"

    # 静态文件
    SERVE_STATIC: bool = True

    # 策略配置
    DEFAULT_STRATEGY: str = "momentum"
    STRATEGY_CONFIG_PATH: str = "config/strategies.yaml"

    @field_validator("ALLOWED_HOSTS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v):
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()


class ChainConfig:
    """区块链配置"""

    ETHEREUM = {
        "name": "Ethereum",
        "chain_id": 1,
        "rpc_url": settings.ETHEREUM_RPC_URL,
        "testnet_rpc": settings.ETHEREUM_TESTNET_RPC,
        "native_token": "ETH",
        "explorer": "https://etherscan.io",
        "dex_routers": {
            "uniswap_v2": "******************************************",
            "uniswap_v3": "******************************************",
            "sushiswap": "******************************************"
        }
    }

    BSC = {
        "name": "Binance Smart Chain",
        "chain_id": 56,
        "rpc_url": settings.BSC_RPC_URL,
        "testnet_rpc": settings.BSC_TESTNET_RPC,
        "native_token": "BNB",
        "explorer": "https://bscscan.com",
        "dex_routers": {
            "pancakeswap_v2": "******************************************",
            "pancakeswap_v3": "******************************************"
        }
    }

    SOLANA = {
        "name": "Solana",
        "rpc_url": settings.SOLANA_RPC_URL,
        "testnet_rpc": settings.SOLANA_TESTNET_RPC,
        "native_token": "SOL",
        "explorer": "https://solscan.io",
        "dex_programs": {
            "raydium": "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8",
            "orca": "9W959DqEETiGZocYWCQPaJ6sBmUzgfxXfqGeTEdp3aQP",
            "jupiter": "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4"
        }
    }


# 导出配置
__all__ = ["settings", "ChainConfig"]
