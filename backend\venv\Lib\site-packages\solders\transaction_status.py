from typing import Union

from .solders import (
    EncodedConfirmedTransactionWithStatusMeta,
    EncodedTransactionWithStatusMeta,
    InstructionErrorBorshIO,
    InstructionErrorCustom,
    InstructionErrorFieldless,
    ParsedAccountSource,
    ParsedInstruction,
    Reward,
    RewardType,
    TransactionBinaryEncoding,
    TransactionConfirmationStatus,
    TransactionDetails,
    TransactionErrorDuplicateInstruction,
    TransactionErrorFieldless,
    TransactionErrorInstructionError,
    TransactionErrorInsufficientFundsForRent,
    TransactionErrorProgramExecutionTemporarilyRestricted,
    TransactionReturnData,
    TransactionStatus,
    UiAccountsList,
    UiAddressTableLookup,
    UiCompiledInstruction,
    UiConfirmedBlock,
    UiInnerInstructions,
    UiLoadedAddresses,
    UiParsedMessage,
    UiPartiallyDecodedInstruction,
    UiRawMessage,
    UiTransaction,
    UiTransactionEncoding,
    UiTransactionStatusMeta,
    UiTransactionTokenBalance,
    VersionedTransaction,
)
from .solders import (
    ParsedAccountTxStatus as ParsedAccount,
)

UiParsedInstruction = Union[ParsedInstruction, UiPartiallyDecodedInstruction]
UiInstruction = Union[UiParsedInstruction, UiCompiledInstruction]
UiMessage = Union[UiParsedMessage, UiRawMessage]
EncodedVersionedTransaction = Union[VersionedTransaction, UiTransaction, UiAccountsList]
InstructionErrorType = Union[
    InstructionErrorFieldless,
    InstructionErrorCustom,
    InstructionErrorBorshIO,
]
TransactionErrorType = Union[
    TransactionErrorFieldless,
    TransactionErrorInstructionError,
    TransactionErrorDuplicateInstruction,
    TransactionErrorInsufficientFundsForRent,
    TransactionErrorProgramExecutionTemporarilyRestricted,
]

__all__ = [
    "UiTransactionEncoding",
    "TransactionDetails",
    "TransactionBinaryEncoding",
    "ParsedInstruction",
    "UiPartiallyDecodedInstruction",
    "UiCompiledInstruction",
    "UiAddressTableLookup",
    "UiParsedMessage",
    "UiRawMessage",
    "ParsedAccountSource",
    "ParsedAccount",
    "UiTransaction",
    "UiAccountsList",
    "UiInnerInstructions",
    "UiLoadedAddresses",
    "UiTransactionTokenBalance",
    "Reward",
    "RewardType",
    "TransactionReturnData",
    "UiTransactionStatusMeta",
    "EncodedTransactionWithStatusMeta",
    "TransactionConfirmationStatus",
    "TransactionStatus",
    "EncodedConfirmedTransactionWithStatusMeta",
    "UiConfirmedBlock",
    "InstructionErrorFieldless",
    "InstructionErrorCustom",
    "InstructionErrorBorshIO",
    "TransactionErrorFieldless",
    "TransactionErrorInstructionError",
    "TransactionErrorDuplicateInstruction",
    "TransactionErrorInsufficientFundsForRent",
    "TransactionErrorProgramExecutionTemporarilyRestricted",
    "UiParsedInstruction",
    "UiInstruction",
    "UiMessage",
    "EncodedVersionedTransaction",
    "InstructionErrorType",
    "TransactionErrorType",
]
