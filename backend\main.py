"""
AI Crypto Trading Agent - 主启动文件
"""

import asyncio
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.logging import setup_logging
from app.api.routes import api_router
from app.services.blockchain_monitor import BlockchainMonitor
from app.services.trading_engine import TradingEngine
from app.services.strategy_manager import StrategyManager

# 设置日志
logger = setup_logging()

# 全局服务实例
blockchain_monitor = None
trading_engine = None
strategy_manager = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global blockchain_monitor, trading_engine, strategy_manager

    logger.info("启动AI加密货币交易Agent...")

    try:
        # 创建数据库表
        from app.models.base import create_tables
        create_tables()
        logger.info("数据库表创建成功")

        # 初始化核心服务
        strategy_manager = StrategyManager()
        blockchain_monitor = BlockchainMonitor()
        trading_engine = TradingEngine(strategy_manager)

        # 启动监控服务
        await blockchain_monitor.start()
        await trading_engine.start()

        logger.info("所有服务启动成功")

        yield

    except Exception as e:
        logger.error(f"启动服务时发生错误: {e}")
        raise
    finally:
        # 清理资源
        logger.info("正在关闭服务...")
        if blockchain_monitor:
            await blockchain_monitor.stop()
        if trading_engine:
            await trading_engine.stop()
        logger.info("所有服务已关闭")


# 创建FastAPI应用
app = FastAPI(
    title="AI Crypto Trading Agent",
    description="智能加密货币链上交易监控和自动化交易系统",
    version="1.0.0",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(api_router, prefix="/api/v1")

# 静态文件服务
if settings.SERVE_STATIC:
    import os
    static_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static")
    if os.path.exists(static_dir):
        app.mount("/static", StaticFiles(directory=static_dir), name="static")
    else:
        logger.warning(f"静态文件目录不存在: {static_dir}")


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "AI Crypto Trading Agent API",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "services": {
            "blockchain_monitor": blockchain_monitor.is_running if blockchain_monitor else False,
            "trading_engine": trading_engine.is_running if trading_engine else False,
            "strategy_manager": strategy_manager.is_active if strategy_manager else False
        }
    }


if __name__ == "__main__":
    # 开发环境直接运行
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info" if settings.DEBUG else "warning"
    )
