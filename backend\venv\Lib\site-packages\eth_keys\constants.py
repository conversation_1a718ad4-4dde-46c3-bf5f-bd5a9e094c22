from typing import (
    <PERSON><PERSON>,
)

#
# SECPK1N
#
SECPK1_P: int = 2**256 - 2**32 - 977
SECPK1_N: int = (
    115792089237316195423570985008687907852837564279074904382605163141518161494337
)
SECPK1_A: int = 0
SECPK1_B: int = 7
SECPK1_Gx: int = (
    55066263022277343669578718895168534326250603453777594175500187360389116729240
)
SECPK1_Gy: int = (
    32670510020758816978083085130507043184471273380659243275938904335757337482424
)
SECPK1_G: Tuple[int, int] = (SECPK1_Gx, SECPK1_Gy)


#
# Internal representations of Jacobian identity points
#
IDENTITY_POINTS = ((0, 0, 0), (0, 0, 1))
