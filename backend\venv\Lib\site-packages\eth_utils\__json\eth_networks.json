[{"name": "Ethereum Mainnet", "chainId": 1, "shortName": "eth", "networkId": 1, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://mainnet.infura.io/v3/${INFURA_API_KEY}", "wss://mainnet.infura.io/ws/v3/${INFURA_API_KEY}", "https://api.mycryptoapi.com/eth", "https://cloudflare-eth.com", "https://ethereum-rpc.publicnode.com", "wss://ethereum-rpc.publicnode.com", "https://mainnet.gateway.tenderly.co", "wss://mainnet.gateway.tenderly.co", "https://rpc.blocknative.com/boost", "https://rpc.flashbots.net", "https://rpc.flashbots.net/fast", "https://rpc.mevblocker.io", "https://rpc.mevblocker.io/fast", "https://rpc.mevblocker.io/noreverts", "https://rpc.mevblocker.io/fullprivacy", "https://eth.drpc.org", "wss://eth.drpc.org"], "faucets": [], "infoURL": "https://ethereum.org"}, {"name": "Expanse Network", "chainId": 2, "shortName": "exp", "networkId": 1, "nativeCurrency": {"name": "Expanse Network Ether", "symbol": "EXP", "decimals": 18}, "rpc": ["https://node.expanse.tech"], "faucets": [], "infoURL": "https://expanse.tech"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 3, "shortName": "rop", "networkId": 3, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://ropsten.infura.io/v3/${INFURA_API_KEY}", "wss://ropsten.infura.io/ws/v3/${INFURA_API_KEY}"], "faucets": ["http://fauceth.komputing.org?chain=3&address=${ADDRESS}", "https://faucet.ropsten.be?${ADDRESS}"], "infoURL": "https://github.com/ethereum/ropsten"}, {"name": "Rinkeb<PERSON>", "chainId": 4, "shortName": "rin", "networkId": 4, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rinkeby.infura.io/v3/${INFURA_API_KEY}", "wss://rinkeby.infura.io/ws/v3/${INFURA_API_KEY}"], "faucets": ["http://fauceth.komputing.org?chain=4&address=${ADDRESS}", "https://faucet.rinkeby.io"], "infoURL": "https://www.rinkeby.io"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 5, "shortName": "gor", "networkId": 5, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://goerli.infura.io/v3/${INFURA_API_KEY}", "wss://goerli.infura.io/v3/${INFURA_API_KEY}", "https://rpc.goerli.mudit.blog/", "https://ethereum-goerli-rpc.publicnode.com", "wss://ethereum-goerli-rpc.publicnode.com", "https://goerli.gateway.tenderly.co", "wss://goerli.gateway.tenderly.co"], "faucets": ["http://fauceth.komputing.org?chain=5&address=${ADDRESS}", "https://goerli-faucet.slock.it?address=${ADDRESS}", "https://faucet.goerli.mudit.blog"], "infoURL": "https://goerli.net/#about"}, {"name": "<PERSON><PERSON>", "chainId": 6, "shortName": "kot", "networkId": 6, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "KOT", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://ethereumclassic.org/development/testnets"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 7, "shortName": "tch", "networkId": 7, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "TCH", "decimals": 18}, "rpc": ["https://rpc.dome.cloud", "https://rpc.thaichain.org"], "faucets": [], "infoURL": "https://thaichain.io"}, {"name": "Ubiq", "chainId": 8, "shortName": "ubq", "networkId": 8, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "UBQ", "decimals": 18}, "rpc": ["https://rpc.octano.dev", "https://pyrus2.ubiqscan.io"], "faucets": [], "infoURL": "https://ubiqsmart.com"}, {"name": "Ubiq Network Testnet", "chainId": 9, "shortName": "tubq", "networkId": 2, "nativeCurrency": {"name": "Ubiq Testnet Ether", "symbol": "TUBQ", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://ethersocial.org"}, {"name": "OP Mainnet", "chainId": 10, "shortName": "oeth", "networkId": 10, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://mainnet.optimism.io", "https://optimism-rpc.publicnode.com", "wss://optimism-rpc.publicnode.com", "https://optimism.gateway.tenderly.co", "wss://optimism.gateway.tenderly.co", "https://optimism.drpc.org", "wss://optimism.drpc.org"], "faucets": [], "infoURL": "https://optimism.io"}, {"name": "Metadium Mainnet", "chainId": 11, "shortName": "meta", "networkId": 11, "nativeCurrency": {"name": "Metadium Mainnet Ether", "symbol": "META", "decimals": 18}, "rpc": ["https://api.metadium.com/prod"], "faucets": [], "infoURL": "https://metadium.com"}, {"name": "Metadium Testnet", "chainId": 12, "shortName": "kal", "networkId": 12, "nativeCurrency": {"name": "Metadium Testnet Ether", "symbol": "KAL", "decimals": 18}, "rpc": ["https://api.metadium.com/dev"], "faucets": [], "infoURL": "https://metadium.com"}, {"name": "Diode Testnet Staging", "chainId": 13, "shortName": "dstg", "networkId": 13, "nativeCurrency": {"name": "Staging Diodes", "symbol": "sDIODE", "decimals": 18}, "rpc": ["https://staging.diode.io:8443/", "wss://staging.diode.io:8443/ws"], "faucets": [], "infoURL": "https://diode.io/staging"}, {"name": "<PERSON><PERSON>e <PERSON>", "chainId": 14, "shortName": "flr", "networkId": 14, "nativeCurrency": {"name": "Flare", "symbol": "FLR", "decimals": 18}, "rpc": ["https://flare-api.flare.network/ext/C/rpc", "https://flare.rpc.thirdweb.com", "https://flare-bundler.etherspot.io", "https://rpc.ankr.com/flare", "https://01-gravelines-003-01.rpc.tatum.io/ext/bc/C/rpc", "https://01-vinthill-003-02.rpc.tatum.io/ext/bc/C/rpc", "https://rpc.ftso.au/flare", "https://flare.enosys.global/ext/C/rpc", "https://flare.solidifi.app/ext/C/rpc"], "faucets": [], "infoURL": "https://flare.network"}, {"name": "Diode Prenet", "chainId": 15, "shortName": "diode", "networkId": 15, "nativeCurrency": {"name": "Diodes", "symbol": "DIODE", "decimals": 18}, "rpc": ["https://prenet.diode.io:8443/", "wss://prenet.diode.io:8443/ws"], "faucets": [], "infoURL": "https://diode.io/prenet"}, {"name": "Songbird Testnet Coston", "chainId": 16, "shortName": "cflr", "networkId": 16, "nativeCurrency": {"name": "Coston Flare", "symbol": "CFLR", "decimals": 18}, "rpc": ["https://coston-api.flare.network/ext/C/rpc", "https://songbird-testnet-coston.rpc.thirdweb.com", "https://01-gravelines-004-01.rpc.tatum.io/ext/bc/C/rpc", "https://02-chicago-004-02.rpc.tatum.io/ext/bc/C/rpc", "https://02-tokyo-004-03.rpc.tatum.io/ext/bc/C/rpc", "https://coston.enosys.global/ext/C/rpc"], "faucets": ["https://faucet.flare.network"], "infoURL": "https://flare.network"}, {"name": "ThaiChain 2.0 ThaiFi", "chainId": 17, "shortName": "tfi", "networkId": 17, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "TFI", "decimals": 18}, "rpc": ["https://rpc.thaifi.com"], "faucets": [], "infoURL": "https://exp.thaifi.com"}, {"name": "ThunderCore Testnet", "chainId": 18, "shortName": "TST", "networkId": 18, "nativeCurrency": {"name": "ThunderCore Testnet Token", "symbol": "TST", "decimals": 18}, "rpc": ["https://testnet-rpc.thundercore.com", "https://thundercore-testnet.drpc.org", "wss://thundercore-testnet.drpc.org"], "faucets": ["https://faucet-testnet.thundercore.com"], "infoURL": "https://thundercore.com"}, {"name": "Songbird Canary-Network", "chainId": 19, "shortName": "sgb", "networkId": 19, "nativeCurrency": {"name": "Songbird", "symbol": "SGB", "decimals": 18}, "rpc": ["https://songbird-api.flare.network/ext/C/rpc", "https://01-gravelines-006-01.rpc.tatum.io/ext/bc/C/rpc", "https://01-vinthill-006-02.rpc.tatum.io/ext/bc/C/rpc", "https://02-tokyo-006-03.rpc.tatum.io/ext/bc/C/rpc", "https://rpc.ftso.au/songbird", "https://songbird.enosys.global/ext/C/rpc", "https://songbird.solidifi.app/ext/C/rpc"], "faucets": [], "infoURL": "https://flare.network"}, {"name": "Elastos Smart Chain", "chainId": 20, "shortName": "esc", "networkId": 20, "nativeCurrency": {"name": "Elastos", "symbol": "ELA", "decimals": 18}, "rpc": ["https://api.elastos.io/eth"], "faucets": [], "infoURL": "https://www.elastos.org/"}, {"name": "Elastos Smart Chain Testnet", "chainId": 21, "shortName": "esct", "networkId": 21, "nativeCurrency": {"name": "Elastos", "symbol": "tELA", "decimals": 18}, "rpc": ["https://api-testnet.elastos.io/eth"], "faucets": ["https://esc-faucet.elastos.io/"], "infoURL": "https://www.elastos.org/"}, {"name": "ELA-DID-Sidechain Mainnet", "chainId": 22, "shortName": "eladid", "networkId": 22, "nativeCurrency": {"name": "Elastos", "symbol": "ELA", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://www.elastos.org/"}, {"name": "ELA-DID-Sidechain Testnet", "chainId": 23, "shortName": "eladi<PERSON>", "networkId": 23, "nativeCurrency": {"name": "Elastos", "symbol": "tELA", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://elaeth.io/"}, {"name": "Kardia<PERSON><PERSON><PERSON>net", "chainId": 24, "shortName": "kardi<PERSON><PERSON>", "networkId": 0, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "KAI", "decimals": 18}, "rpc": ["https://rpc.kardiachain.io"], "faucets": [], "infoURL": "https://kardiachain.io"}, {"name": "Cronos Mainnet", "chainId": 25, "shortName": "cro", "networkId": 25, "nativeCurrency": {"name": "Cronos", "symbol": "CRO", "decimals": 18}, "rpc": ["https://evm.cronos.org", "https://cronos-evm-rpc.publicnode.com", "wss://cronos-evm-rpc.publicnode.com", "https://cronos.drpc.org", "wss://cronos.drpc.org"], "faucets": [], "infoURL": "https://cronos.org/"}, {"name": "Genesis L1 testnet", "chainId": 26, "shortName": "L1test", "networkId": 26, "nativeCurrency": {"name": "L1 testcoin", "symbol": "L1test", "decimals": 18}, "rpc": ["https://testrpc.genesisl1.org"], "faucets": [], "infoURL": "https://www.genesisl1.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 27, "shortName": "shib", "networkId": 27, "nativeCurrency": {"name": "SHIBA INU COIN", "symbol": "SHIB", "decimals": 18}, "rpc": ["https://rpc.shibchain.org"], "faucets": [], "infoURL": "https://shibchain.org"}, {"name": "Boba Network Rinkeby Testnet", "chainId": 28, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "networkId": 28, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rinkeby.boba.network/"], "faucets": [], "infoURL": "https://boba.network"}, {"name": "Genesis L1", "chainId": 29, "shortName": "L1", "networkId": 29, "nativeCurrency": {"name": "L1 coin", "symbol": "L1", "decimals": 18}, "rpc": ["https://rpc.genesisl1.org"], "faucets": [], "infoURL": "https://www.genesisl1.com"}, {"name": "Rootstock Mainnet", "chainId": 30, "shortName": "rsk", "networkId": 30, "nativeCurrency": {"name": "Smart Bitcoin", "symbol": "RBTC", "decimals": 18}, "rpc": ["https://public-node.rsk.co", "https://mycrypto.rsk.co"], "faucets": [], "infoURL": "https://rootstock.io"}, {"name": "Rootstock Testnet", "chainId": 31, "shortName": "trsk", "networkId": 31, "nativeCurrency": {"name": "Testnet Smart Bitcoin", "symbol": "tRBTC", "decimals": 18}, "rpc": ["https://public-node.testnet.rsk.co", "https://mycrypto.testnet.rsk.co"], "faucets": ["https://faucet.rsk.co/"], "infoURL": "https://rootstock.io"}, {"name": "GoodData Testnet", "chainId": 32, "shortName": "GooDT", "networkId": 32, "nativeCurrency": {"name": "GoodData Testnet Ether", "symbol": "GooD", "decimals": 18}, "rpc": ["https://test2.goodata.io"], "faucets": [], "infoURL": "https://www.goodata.org"}, {"name": "GoodData Mainnet", "chainId": 33, "shortName": "GooD", "networkId": 33, "nativeCurrency": {"name": "GoodData Mainnet Ether", "symbol": "GooD", "decimals": 18}, "rpc": ["https://rpc.goodata.io"], "faucets": [], "infoURL": "https://www.goodata.org"}, {"name": "SecureChain Mainnet", "chainId": 34, "shortName": "scai", "networkId": 34, "nativeCurrency": {"name": "Secure<PERSON>hain", "symbol": "SCAI", "decimals": 18}, "rpc": ["https://mainnet-rpc.scai.network"], "faucets": [], "infoURL": "https://securechain.ai"}, {"name": "TBWG Chain", "chainId": 35, "shortName": "tbwg", "networkId": 35, "nativeCurrency": {"name": "TBWG Ether", "symbol": "TBG", "decimals": 18}, "rpc": ["https://rpc.tbwg.io"], "faucets": [], "infoURL": "https://tbwg.io"}, {"name": "Dxchain Mainnet", "chainId": 36, "shortName": "dx", "networkId": 36, "nativeCurrency": {"name": "Dxchain", "symbol": "DX", "decimals": 18}, "rpc": ["https://mainnet.dxchain.com"], "faucets": [], "infoURL": "https://www.dxchain.com/"}, {"name": "Xpla Mainnet", "chainId": 37, "shortName": "xpla", "networkId": 37, "nativeCurrency": {"name": "XPLA", "symbol": "XPLA", "decimals": 18}, "rpc": ["https://dimension-evm-rpc.xpla.dev"], "faucets": [], "infoURL": "https://xpla.io"}, {"name": "Valorbit", "chainId": 38, "shortName": "val", "networkId": 38, "nativeCurrency": {"name": "Valorbit", "symbol": "VAL", "decimals": 18}, "rpc": ["https://rpc.valorbit.com/v2"], "faucets": [], "infoURL": "https://valorbit.com"}, {"name": "U2U Solaris Mainnet", "chainId": 39, "shortName": "u2u", "networkId": 39, "nativeCurrency": {"name": "Unicorn Ultra", "symbol": "U2U", "decimals": 18}, "rpc": ["https://rpc-mainnet.uniultra.xyz"], "faucets": [], "infoURL": "https://uniultra.xyz"}, {"name": "Telos EVM Mainnet", "chainId": 40, "shortName": "TelosEVM", "networkId": 40, "nativeCurrency": {"name": "Telos", "symbol": "TLOS", "decimals": 18}, "rpc": ["https://mainnet.telos.net/evm", "https://telos.drpc.org", "wss://telos.drpc.org"], "faucets": [], "infoURL": "https://telos.net"}, {"name": "Telos EVM Testnet", "chainId": 41, "shortName": "TelosEVMTestnet", "networkId": 41, "nativeCurrency": {"name": "Telos", "symbol": "TLOS", "decimals": 18}, "rpc": ["https://testnet.telos.net/evm", "https://telos-testnet.drpc.org", "wss://telos-testnet.drpc.org"], "faucets": ["https://app.telos.net/testnet/developers"], "infoURL": "https://telos.net"}, {"name": "LUKSO Mainnet", "chainId": 42, "shortName": "lukso", "networkId": 42, "nativeCurrency": {"name": "LUKSO", "symbol": "LYX", "decimals": 18}, "rpc": ["https://rpc.mainnet.lukso.network", "wss://ws-rpc.mainnet.lukso.network"], "faucets": [], "infoURL": "https://lukso.network"}, {"name": "Darwinia Pangolin Testnet", "chainId": 43, "shortName": "pangolin", "networkId": 43, "nativeCurrency": {"name": "Pangolin Network Native Token", "symbol": "PRING", "decimals": 18}, "rpc": ["https://pangolin-rpc.darwinia.network"], "faucets": ["https://docs.darwinia.network/pangolin-testnet-1e9ac8b09e874e8abd6a7f18c096ca6a"], "infoURL": "https://darwinia.network/"}, {"name": "Crab Network", "chainId": 44, "shortName": "crab", "networkId": 44, "nativeCurrency": {"name": "Crab Network Native Token", "symbol": "CRAB", "decimals": 18}, "rpc": ["https://crab-rpc.darwinia.network", "https://crab-rpc.darwiniacommunitydao.xyz"], "faucets": [], "infoURL": "https://crab.network/"}, {"name": "Darwinia Pangoro Testnet", "chainId": 45, "shortName": "pangoro", "networkId": 45, "nativeCurrency": {"name": "Pangoro Network Native Token", "symbol": "ORING", "decimals": 18}, "rpc": ["https://pangoro-rpc.darwinia.network"], "faucets": ["https://docs.darwinia.network/pangoro-testnet-70cfec5dc9ca42759959ba3803edaec2"], "infoURL": "https://darwinia.network/"}, {"name": "Darwinia Network", "chainId": 46, "shortName": "dar<PERSON>ia", "networkId": 46, "nativeCurrency": {"name": "Darwinia Network Native Token", "symbol": "RING", "decimals": 18}, "rpc": ["https://rpc.darwinia.network", "https://darwinia-rpc.darwiniacommunitydao.xyz", "https://darwinia-rpc.dwellir.com"], "faucets": [], "infoURL": "https://darwinia.network/"}, {"name": "Acria <PERSON>liChain", "chainId": 47, "shortName": "aic", "networkId": 47, "nativeCurrency": {"name": "ACRIA", "symbol": "ACRIA", "decimals": 18}, "rpc": ["https://aic.acria.ai"], "faucets": [], "infoURL": "https://acria.ai"}, {"name": "Ennothem Mainnet Proterozoic", "chainId": 48, "shortName": "etmp", "networkId": 48, "nativeCurrency": {"name": "Ennothem", "symbol": "ETMP", "decimals": 18}, "rpc": ["https://rpc.etm.network"], "faucets": [], "infoURL": "https://etm.network"}, {"name": "Ennothem Testnet Pioneer", "chainId": 49, "shortName": "etmpTest", "networkId": 49, "nativeCurrency": {"name": "Ennothem", "symbol": "ETMP", "decimals": 18}, "rpc": ["https://rpc.pioneer.etm.network"], "faucets": [], "infoURL": "https://etm.network"}, {"name": "XDC Network", "chainId": 50, "shortName": "xdc", "networkId": 50, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "XDC", "decimals": 18}, "rpc": ["https://erpc.xinfin.network", "https://rpc.xinfin.network", "https://rpc1.xinfin.network", "https://rpc-xdc.icecreamswap.com"], "faucets": [], "infoURL": "https://xinfin.org"}, {"name": "XDC Apothem Network", "chainId": 51, "shortName": "txdc", "networkId": 51, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "TXDC", "decimals": 18}, "rpc": ["https://rpc.apothem.network", "https://erpc.apothem.network"], "faucets": ["https://faucet.apothem.network"], "infoURL": "https://xinfin.org"}, {"name": "CoinEx Smart Chain Mainnet", "chainId": 52, "shortName": "cet", "networkId": 52, "nativeCurrency": {"name": "CoinEx Chain Native Token", "symbol": "cet", "decimals": 18}, "rpc": ["https://rpc.coinex.net"], "faucets": [], "infoURL": "https://www.coinex.org/"}, {"name": "CoinEx Smart Chain Testnet", "chainId": 53, "shortName": "tcet", "networkId": 53, "nativeCurrency": {"name": "CoinEx Chain Test Native Token", "symbol": "cett", "decimals": 18}, "rpc": ["https://testnet-rpc.coinex.net/"], "faucets": [], "infoURL": "https://www.coinex.org/"}, {"name": "Openpiece Mainnet", "chainId": 54, "shortName": "OP", "networkId": 54, "nativeCurrency": {"name": "<PERSON>y", "symbol": "BELLY", "decimals": 18}, "rpc": ["https://mainnet.openpiece.io"], "faucets": [], "infoURL": "https://cryptopiece.online"}, {"name": "Zyx Mainnet", "chainId": 55, "shortName": "ZYX", "networkId": 55, "nativeCurrency": {"name": "Zyx", "symbol": "ZYX", "decimals": 18}, "rpc": ["https://rpc-1.zyx.network/", "https://rpc-2.zyx.network/", "https://rpc-3.zyx.network/", "https://rpc-4.zyx.network/", "https://rpc-5.zyx.network/", "https://rpc-6.zyx.network/"], "faucets": [], "infoURL": "https://zyx.network/"}, {"name": "BNB Smart Chain Mainnet", "chainId": 56, "shortName": "bnb", "networkId": 56, "nativeCurrency": {"name": "BNB Chain Native Token", "symbol": "BNB", "decimals": 18}, "rpc": ["https://bsc-dataseed1.bnbchain.org", "https://bsc-dataseed2.bnbchain.org", "https://bsc-dataseed3.bnbchain.org", "https://bsc-dataseed4.bnbchain.org", "https://bsc-dataseed1.defibit.io", "https://bsc-dataseed2.defibit.io", "https://bsc-dataseed3.defibit.io", "https://bsc-dataseed4.defibit.io", "https://bsc-dataseed1.ninicoin.io", "https://bsc-dataseed2.ninicoin.io", "https://bsc-dataseed3.ninicoin.io", "https://bsc-dataseed4.ninicoin.io", "https://bsc-rpc.publicnode.com", "wss://bsc-rpc.publicnode.com", "wss://bsc-ws-node.nariox.org"], "faucets": [], "infoURL": "https://www.bnbchain.org/en"}, {"name": "Syscoin Mainnet", "chainId": 57, "shortName": "sys", "networkId": 57, "nativeCurrency": {"name": "Syscoin", "symbol": "SYS", "decimals": 18}, "rpc": ["https://rpc.syscoin.org", "https://rpc.ankr.com/syscoin/${ANKR_API_KEY}", "https://syscoin.public-rpc.com", "wss://rpc.syscoin.org/wss", "https://syscoin-evm.publicnode.com", "wss://syscoin-evm.publicnode.com"], "faucets": ["https://faucet.syscoin.org"], "infoURL": "https://www.syscoin.org"}, {"name": "Ontology Mainnet", "chainId": 58, "shortName": "OntologyMainnet", "networkId": 58, "nativeCurrency": {"name": "ONG", "symbol": "ONG", "decimals": 18}, "rpc": ["http://dappnode1.ont.io:20339", "http://dappnode2.ont.io:20339", "http://dappnode3.ont.io:20339", "http://dappnode4.ont.io:20339", "https://dappnode1.ont.io:10339", "https://dappnode2.ont.io:10339", "https://dappnode3.ont.io:10339", "https://dappnode4.ont.io:10339"], "faucets": [], "infoURL": "https://ont.io/"}, {"name": "EOS EVM Legacy", "chainId": 59, "shortName": "eos-legacy", "networkId": 59, "nativeCurrency": {"name": "EOS", "symbol": "EOS", "decimals": 18}, "rpc": ["https://api.eosargentina.io"], "faucets": [], "infoURL": "https://eosargentina.io"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 60, "shortName": "go", "networkId": 60, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "GO", "decimals": 18}, "rpc": ["https://rpc.gochain.io"], "faucets": [], "infoURL": "https://gochain.io"}, {"name": "Ethereum Classic", "chainId": 61, "shortName": "etc", "networkId": 1, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETC", "decimals": 18}, "rpc": ["https://etc.rivet.link", "https://besu-at.etc-network.info", "https://besu-de.etc-network.info", "https://geth-at.etc-network.info", "https://geth-de.etc-network.info", "https://etc.etcdesktop.com", "https://rpc.etcinscribe.com", "https://etc.mytokenpocket.vip"], "faucets": [], "infoURL": "https://ethereumclassic.org"}, {"name": "Morden Testnet", "chainId": 62, "shortName": "tetc", "networkId": 2, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "TETC", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://ethereumclassic.org/development/testnets"}, {"name": "Mordor Testnet", "chainId": 63, "shortName": "metc", "networkId": 7, "nativeCurrency": {"name": "Mo<PERSON><PERSON>", "symbol": "METC", "decimals": 18}, "rpc": ["https://rpc.mordor.etccooperative.org", "https://geth-mordor.etc-network.info"], "faucets": ["https://easy.hebeswap.com/#/faucet", "https://faucet.mordortest.net"], "infoURL": "https://ethereumclassic.org/development/testnets"}, {"name": "Ellaism", "chainId": 64, "shortName": "ellaism", "networkId": 64, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ELLA", "decimals": 18}, "rpc": ["https://jsonrpc.ellaism.org"], "faucets": [], "infoURL": "https://ellaism.org"}, {"name": "OKExChain Testnet", "chainId": 65, "shortName": "tokt", "networkId": 65, "nativeCurrency": {"name": "OKExChain Global Utility Token in testnet", "symbol": "OKT", "decimals": 18}, "rpc": ["https://exchaintestrpc.okex.org"], "faucets": ["https://www.okex.com/drawdex"], "infoURL": "https://www.okex.com/okexchain"}, {"name": "OKXChain Mainnet", "chainId": 66, "shortName": "okt", "networkId": 66, "nativeCurrency": {"name": "OKXChain Global Utility Token", "symbol": "OKT", "decimals": 18}, "rpc": ["https://exchainrpc.okex.org", "https://okc-mainnet.gateway.pokt.network/v1/lb/6275309bea1b320039c893ff"], "faucets": [], "infoURL": "https://www.okex.com/okc"}, {"name": "DBChain <PERSON>net", "chainId": 67, "shortName": "dbm", "networkId": 67, "nativeCurrency": {"name": "DBChain <PERSON>net", "symbol": "DBM", "decimals": 18}, "rpc": ["http://test-rpc.dbmbp.com"], "faucets": [], "infoURL": "http://test.dbmbp.com"}, {"name": "SoterOne Mainnet", "chainId": 68, "shortName": "SO1", "networkId": 68, "nativeCurrency": {"name": "SoterOne Mainnet Ether", "symbol": "SOTER", "decimals": 18}, "rpc": ["https://rpc.soter.one"], "faucets": [], "infoURL": "https://www.soterone.com"}, {"name": "Optimism Ko<PERSON>", "chainId": 69, "shortName": "<PERSON>ov", "networkId": 69, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://kovan.optimism.io/"], "faucets": ["http://fauceth.komputing.org?chain=69&address=${ADDRESS}"], "infoURL": "https://optimism.io"}, {"name": "Hoo Smart Chain", "chainId": 70, "shortName": "hsc", "networkId": 70, "nativeCurrency": {"name": "Hoo Smart Chain Native Token", "symbol": "HOO", "decimals": 18}, "rpc": ["https://http-mainnet.hoosmartchain.com", "https://http-mainnet2.hoosmartchain.com", "wss://ws-mainnet.hoosmartchain.com", "wss://ws-mainnet2.hoosmartchain.com"], "faucets": [], "infoURL": "https://www.hoosmartchain.com"}, {"name": "Conflux eSpace (Testnet)", "chainId": 71, "shortName": "cfxtest", "networkId": 71, "nativeCurrency": {"name": "CFX", "symbol": "CFX", "decimals": 18}, "rpc": ["https://evmtestnet.confluxrpc.com"], "faucets": ["https://faucet.confluxnetwork.org"], "infoURL": "https://confluxnetwork.org"}, {"name": "DxChain Testnet", "chainId": 72, "shortName": "dxc", "networkId": 72, "nativeCurrency": {"name": "DxChain Testnet", "symbol": "DX", "decimals": 18}, "rpc": ["https://testnet-http.dxchain.com"], "faucets": ["https://faucet.dxscan.io"], "infoURL": "https://testnet.dxscan.io/"}, {"name": "FNCY", "chainId": 73, "shortName": "FNCY", "networkId": 73, "nativeCurrency": {"name": "FNCY", "symbol": "FNCY", "decimals": 18}, "rpc": ["https://fncy-seed1.fncy.world"], "faucets": ["https://faucet-testnet.fncy.world"], "infoURL": "https://fncyscan.fncy.world"}, {"name": "IDChain Mainnet", "chainId": 74, "shortName": "idchain", "networkId": 74, "nativeCurrency": {"name": "EIDI", "symbol": "EIDI", "decimals": 18}, "rpc": ["https://idchain.one/rpc/", "wss://idchain.one/ws/"], "faucets": [], "infoURL": "https://idchain.one/begin/"}, {"name": "Decimal Smart Chain Mainnet", "chainId": 75, "shortName": "DSC", "networkId": 75, "nativeCurrency": {"name": "Decimal", "symbol": "DEL", "decimals": 18}, "rpc": ["https://node.decimalchain.com/web3/", "https://node1-mainnet.decimalchain.com/web3/", "https://node2-mainnet.decimalchain.com/web3/", "https://node3-mainnet.decimalchain.com/web3/", "https://node4-mainnet.decimalchain.com/web3/"], "faucets": [], "infoURL": "https://decimalchain.com"}, {"name": "Mix", "chainId": 76, "shortName": "mix", "networkId": 76, "nativeCurrency": {"name": "<PERSON> Ether", "symbol": "MIX", "decimals": 18}, "rpc": ["https://rpc2.mix-blockchain.org:8647"], "faucets": [], "infoURL": "https://mix-blockchain.org"}, {"name": "POA Network Sokol", "chainId": 77, "shortName": "spoa", "networkId": 77, "nativeCurrency": {"name": "POA Sokol Ether", "symbol": "SPOA", "decimals": 18}, "rpc": ["https://sokol.poa.network", "wss://sokol.poa.network/wss", "ws://sokol.poa.network:8546"], "faucets": [], "infoURL": "https://poa.network"}, {"name": "PrimusChain mainnet", "chainId": 78, "shortName": "primuschain", "networkId": 78, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "PETH", "decimals": 18}, "rpc": ["https://ethnode.primusmoney.com/mainnet"], "faucets": [], "infoURL": "https://primusmoney.com"}, {"name": "Zenith Mainnet", "chainId": 79, "shortName": "zenith", "networkId": 79, "nativeCurrency": {"name": "ZENITH", "symbol": "ZENITH", "decimals": 18}, "rpc": ["https://dataserver-us-1.zenithchain.co/", "https://dataserver-asia-3.zenithchain.co/", "https://dataserver-asia-4.zenithchain.co/", "https://dataserver-asia-2.zenithchain.co/", "https://dataserver-asia-5.zenithchain.co/", "https://dataserver-asia-6.zenithchain.co/", "https://dataserver-asia-7.zenithchain.co/"], "faucets": [], "infoURL": "https://www.zenithchain.co/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 80, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "networkId": 80, "nativeCurrency": {"name": "RNA", "symbol": "RNA", "decimals": 18}, "rpc": ["https://rpc.genechain.io"], "faucets": [], "infoURL": "https://scan.genechain.io/"}, {"name": "Japan Open Chain Mainnet", "chainId": 81, "shortName": "joc", "networkId": 81, "nativeCurrency": {"name": "Japan Open Chain Token", "symbol": "JOC", "decimals": 18}, "rpc": ["https://rpc-1.japanopenchain.org:8545", "https://rpc-2.japanopenchain.org:8545"], "faucets": [], "infoURL": "https://www.japanopenchain.org/"}, {"name": "Meter Mainnet", "chainId": 82, "shortName": "<PERSON>er", "networkId": 82, "nativeCurrency": {"name": "<PERSON>er", "symbol": "MTR", "decimals": 18}, "rpc": ["https://rpc.meter.io"], "faucets": ["https://faucet.meter.io"], "infoURL": "https://www.meter.io"}, {"name": "Meter Testnet", "chainId": 83, "shortName": "MeterTest", "networkId": 83, "nativeCurrency": {"name": "<PERSON>er", "symbol": "MTR", "decimals": 18}, "rpc": ["https://rpctest.meter.io"], "faucets": ["https://faucet-warringstakes.meter.io"], "infoURL": "https://www.meter.io"}, {"name": "Linqto Devnet", "chainId": 84, "shortName": "linqto-devnet", "networkId": 84, "nativeCurrency": {"name": "XRP", "symbol": "XRP", "decimals": 18}, "rpc": ["https://linqto-dev.com"], "faucets": [], "infoURL": "https://linqto.com"}, {"name": "GateChain <PERSON>net", "chainId": 85, "shortName": "gttest", "networkId": 85, "nativeCurrency": {"name": "GateToken", "symbol": "GT", "decimals": 18}, "rpc": ["https://testnet.gatenode.cc"], "faucets": ["https://www.gatescan.org/testnet/faucet"], "infoURL": "https://www.gatechain.io"}, {"name": "GateChain Mainnet", "chainId": 86, "shortName": "gt", "networkId": 86, "nativeCurrency": {"name": "GateToken", "symbol": "GT", "decimals": 18}, "rpc": ["https://evm.gatenode.cc"], "faucets": ["https://www.gatescan.org/faucet"], "infoURL": "https://www.gatechain.io"}, {"name": "Nova Network", "chainId": 87, "shortName": "nnw", "networkId": 87, "nativeCurrency": {"name": "Supernova", "symbol": "SNT", "decimals": 18}, "rpc": ["https://connect.novanetwork.io", "https://0x57.redjackstudio.com", "https://rpc.novanetwork.io:9070"], "faucets": [], "infoURL": "https://novanetwork.io"}, {"name": "Viction", "chainId": 88, "shortName": "vic", "networkId": 88, "nativeCurrency": {"name": "Viction", "symbol": "VIC", "decimals": 18}, "rpc": ["https://rpc.viction.xyz"], "faucets": [], "infoURL": "https://viction.xyz"}, {"name": "Viction Testnet", "chainId": 89, "shortName": "vict", "networkId": 89, "nativeCurrency": {"name": "Viction", "symbol": "VIC", "decimals": 18}, "rpc": ["https://rpc-testnet.viction.xyz"], "faucets": [], "infoURL": "https://viction.xyz"}, {"name": "Garizon Stage0", "chainId": 90, "shortName": "gar-s0", "networkId": 90, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "GAR", "decimals": 18}, "rpc": ["https://s0.garizon.net/rpc"], "faucets": [], "infoURL": "https://garizon.com"}, {"name": "Garizon Stage1", "chainId": 91, "shortName": "gar-s1", "networkId": 91, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "GAR", "decimals": 18}, "rpc": ["https://s1.garizon.net/rpc"], "faucets": [], "infoURL": "https://garizon.com"}, {"name": "Garizon Stage2", "chainId": 92, "shortName": "gar-s2", "networkId": 92, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "GAR", "decimals": 18}, "rpc": ["https://s2.garizon.net/rpc"], "faucets": [], "infoURL": "https://garizon.com"}, {"name": "Garizon Stage3", "chainId": 93, "shortName": "gar-s3", "networkId": 93, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "GAR", "decimals": 18}, "rpc": ["https://s3.garizon.net/rpc"], "faucets": [], "infoURL": "https://garizon.com"}, {"name": "SwissDLT", "chainId": 94, "shortName": "sdlt", "networkId": 94, "nativeCurrency": {"name": "BCTS", "symbol": "BCTS", "decimals": 18}, "rpc": ["https://rpc.swissdlt.ch"], "faucets": [], "infoURL": "https://bcts.ch"}, {"name": "CamDL Mainnet", "chainId": 95, "shortName": "camdl", "networkId": 95, "nativeCurrency": {"name": "CADL", "symbol": "CADL", "decimals": 18}, "rpc": ["https://rpc1.camdl.gov.kh/"], "faucets": ["https://faucet.camdl.gov.kh/"], "infoURL": "https://camdl.gov.kh/"}, {"name": "Bitkub Chain", "chainId": 96, "shortName": "bkc", "networkId": 96, "nativeCurrency": {"name": "Bitkub Coin", "symbol": "KUB", "decimals": 18}, "rpc": ["https://rpc.bitkubchain.io", "wss://wss.bitkubchain.io"], "faucets": [], "infoURL": "https://www.bitkubchain.com/"}, {"name": "BNB Smart Chain Testnet", "chainId": 97, "shortName": "bnbt", "networkId": 97, "nativeCurrency": {"name": "BNB Chain Native Token", "symbol": "tBNB", "decimals": 18}, "rpc": ["https://data-seed-prebsc-1-s1.bnbchain.org:8545", "https://data-seed-prebsc-2-s1.bnbchain.org:8545", "https://data-seed-prebsc-1-s2.bnbchain.org:8545", "https://data-seed-prebsc-2-s2.bnbchain.org:8545", "https://data-seed-prebsc-1-s3.bnbchain.org:8545", "https://data-seed-prebsc-2-s3.bnbchain.org:8545", "https://bsc-testnet-rpc.publicnode.com", "wss://bsc-testnet-rpc.publicnode.com"], "faucets": ["https://testnet.bnbchain.org/faucet-smart"], "infoURL": "https://www.bnbchain.org/en"}, {"name": "Six Protocol", "chainId": 98, "shortName": "six", "networkId": 98, "nativeCurrency": {"name": "SIX evm token", "symbol": "SIX", "decimals": 18}, "rpc": ["https://sixnet-rpc-evm.sixprotocol.net"], "faucets": [], "infoURL": "https://six.network/"}, {"name": "POA Network Core", "chainId": 99, "shortName": "poa", "networkId": 99, "nativeCurrency": {"name": "POA Network Core Ether", "symbol": "POA", "decimals": 18}, "rpc": ["https://core.poa.network"], "faucets": [], "infoURL": "https://poa.network"}, {"name": "Gnosis", "chainId": 100, "shortName": "gno", "networkId": 100, "nativeCurrency": {"name": "xDAI", "symbol": "XDAI", "decimals": 18}, "rpc": ["https://rpc.gnosischain.com", "https://rpc.gnosis.gateway.fm", "https://rpc.ankr.com/gnosis", "https://gnosischain-rpc.gateway.pokt.network", "https://gnosis-mainnet.public.blastapi.io", "https://gnosis.api.onfinality.io/public", "https://gnosis.blockpi.network/v1/rpc/public", "https://web3endpoints.com/gnosischain-mainnet", "https://gnosis.oat.farm", "wss://rpc.gnosischain.com/wss", "https://gnosis-rpc.publicnode.com", "wss://gnosis-rpc.publicnode.com"], "faucets": ["https://gnosisfaucet.com", "https://stakely.io/faucet/gnosis-chain-xdai", "https://faucet.prussia.dev/xdai"], "infoURL": "https://docs.gnosischain.com"}, {"name": "EtherInc", "chainId": 101, "shortName": "eti", "networkId": 1, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "ETI", "decimals": 18}, "rpc": ["https://api.einc.io/jsonrpc/mainnet"], "faucets": [], "infoURL": "https://einc.io"}, {"name": "Web3Games Testnet", "chainId": 102, "shortName": "tw3g", "networkId": 102, "nativeCurrency": {"name": "Web3Games", "symbol": "W3G", "decimals": 18}, "rpc": ["https://testnet-rpc-0.web3games.org/evm", "https://testnet-rpc-1.web3games.org/evm", "https://testnet-rpc-2.web3games.org/evm"], "faucets": [], "infoURL": "https://web3games.org/"}, {"name": "WorldLand Mainnet", "chainId": 103, "shortName": "WLC", "networkId": 103, "nativeCurrency": {"name": "Worldland", "symbol": "WLC", "decimals": 18}, "rpc": ["https://seoul.worldland.foundation", "https://seoul2.worldland.foundation"], "faucets": [], "infoURL": "https://worldland.foundation"}, {"name": "Kaiba Lightning Chain Testnet", "chainId": 104, "shortName": "tklc", "networkId": 104, "nativeCurrency": {"name": "Kaiba Testnet Token", "symbol": "tKAIBA", "decimals": 18}, "rpc": ["https://klc.live/"], "faucets": [], "infoURL": "https://kaibadefi.com"}, {"name": "Web3Games Devnet", "chainId": 105, "shortName": "dw3g", "networkId": 105, "nativeCurrency": {"name": "Web3Games", "symbol": "W3G", "decimals": 18}, "rpc": ["https://devnet.web3games.org/evm"], "faucets": [], "infoURL": "https://web3games.org/"}, {"name": "Velas EVM Mainnet", "chainId": 106, "shortName": "vlx", "networkId": 106, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "VLX", "decimals": 18}, "rpc": ["https://evmexplorer.velas.com/rpc", "https://explorer.velas.com/rpc"], "faucets": [], "infoURL": "https://velas.com"}, {"name": "Nebula Testnet", "chainId": 107, "shortName": "ntn", "networkId": 107, "nativeCurrency": {"name": "Nebula X", "symbol": "NBX", "decimals": 18}, "rpc": ["https://testnet.rpc.novanetwork.io"], "faucets": ["https://faucet.novanetwork.io"], "infoURL": "https://novanetwork.io"}, {"name": "ThunderCore Mainnet", "chainId": 108, "shortName": "TT", "networkId": 108, "nativeCurrency": {"name": "ThunderCore Token", "symbol": "TT", "decimals": 18}, "rpc": ["https://mainnet-rpc.thundercore.com", "https://mainnet-rpc.thundertoken.net", "https://mainnet-rpc.thundercore.io"], "faucets": [], "infoURL": "https://thundercore.com"}, {"name": "Shibarium", "chainId": 109, "shortName": "shibariumecosystem", "networkId": 109, "nativeCurrency": {"name": "BONE Shibarium", "symbol": "BONE", "decimals": 18}, "rpc": ["https://www.shibrpc.com"], "faucets": [], "infoURL": "https://shibariumecosystem.com"}, {"name": "Proton Testnet", "chainId": 110, "shortName": "xpr", "networkId": 110, "nativeCurrency": {"name": "Proton", "symbol": "XPR", "decimals": 4}, "rpc": ["https://protontestnet.greymass.com/"], "faucets": [], "infoURL": "https://protonchain.com"}, {"name": "EtherLite Chain", "chainId": 111, "shortName": "ETL", "networkId": 111, "nativeCurrency": {"name": "EtherLite", "symbol": "ETL", "decimals": 18}, "rpc": ["https://rpc.etherlite.org"], "faucets": ["https://etherlite.org/faucets"], "infoURL": "https://etherlite.org"}, {"name": "Coinbit Mainnet", "chainId": 112, "shortName": "coinbit", "networkId": 112, "nativeCurrency": {"name": "Gas IDR", "symbol": "GIDR", "decimals": 18}, "rpc": ["https://coinbit-rpc-mainnet.chain.sbcrypto.app"], "faucets": [], "infoURL": "https://crypto.stockbit.com/"}, {"name": "<PERSON>h<PERSON>", "chainId": 113, "shortName": "deh", "networkId": 113, "nativeCurrency": {"name": "<PERSON>h<PERSON>", "symbol": "<PERSON><PERSON>", "decimals": 18}, "rpc": ["https://connect.dehvo.com", "https://rpc.dehvo.com", "https://rpc1.dehvo.com", "https://rpc2.dehvo.com"], "faucets": ["https://buy.dehvo.com"], "infoURL": "https://dehvo.com"}, {"name": "Flare Testnet Coston2", "chainId": 114, "shortName": "c2flr", "networkId": 114, "nativeCurrency": {"name": "Coston2 Flare", "symbol": "C2FLR", "decimals": 18}, "rpc": ["https://coston2-api.flare.network/ext/C/rpc", "https://flare-testnet-coston2.rpc.thirdweb.com", "https://flaretestnet-bundler.etherspot.io", "https://01-gravelines-005-01.rpc.tatum.io/ext/bc/C/rpc", "https://02-chicago-005-02.rpc.tatum.io/ext/bc/C/rpc", "https://02-tokyo-005-03.rpc.tatum.io/ext/bc/C/rpc", "https://coston2.enosys.global/ext/C/rpc"], "faucets": ["https://faucet.flare.network"], "infoURL": "https://flare.network"}, {"name": "DeBank Testnet(Deprecated)", "chainId": 115, "shortName": "debank-testnet", "networkId": 115, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://debank.com"}, {"name": "DeBank Mainnet", "chainId": 116, "shortName": "debank-mainnet", "networkId": 116, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://debank.com"}, {"name": "Uptick Mainnet", "chainId": 117, "shortName": "auptick", "networkId": 117, "nativeCurrency": {"name": "Uptick", "symbol": "UPTICK", "decimals": 18}, "rpc": ["https://json-rpc.uptick.network"], "faucets": [], "infoURL": "https://www.uptick.network"}, {"name": "Arcology Testnet", "chainId": 118, "shortName": "arcology", "networkId": 118, "nativeCurrency": {"name": "Arcology Coin", "symbol": "Acol", "decimals": 18}, "rpc": ["https://testnet.arcology.network/rpc"], "faucets": [], "infoURL": "https://arcology.network/"}, {"name": "ENULS Mainnet", "chainId": 119, "shortName": "enuls", "networkId": 119, "nativeCurrency": {"name": "NULS", "symbol": "NULS", "decimals": 18}, "rpc": ["https://evmapi.nuls.io", "https://evmapi2.nuls.io"], "faucets": [], "infoURL": "https://nuls.io"}, {"name": "ENULS Testnet", "chainId": 120, "shortName": "enulst", "networkId": 120, "nativeCurrency": {"name": "NULS", "symbol": "NULS", "decimals": 18}, "rpc": ["https://beta.evmapi.nuls.io", "https://beta.evmapi2.nuls.io"], "faucets": ["http://faucet.nuls.io"], "infoURL": "https://nuls.io"}, {"name": "Realchain Mainnet", "chainId": 121, "shortName": "REAL", "networkId": 121, "nativeCurrency": {"name": "Realchain", "symbol": "REAL", "decimals": 18}, "rpc": ["https://rcl-dataseed1.rclsidechain.com", "https://rcl-dataseed2.rclsidechain.com", "https://rcl-dataseed3.rclsidechain.com", "https://rcl-dataseed4.rclsidechain.com", "wss://rcl-dataseed1.rclsidechain.com/v1/", "wss://rcl-dataseed2.rclsidechain.com/v1/", "wss://rcl-dataseed3.rclsidechain.com/v1/", "wss://rcl-dataseed4.rclsidechain.com/v1/"], "faucets": [], "infoURL": "https://www.rclsidechain.com/"}, {"name": "<PERSON><PERSON>", "chainId": 122, "shortName": "fuse", "networkId": 122, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "FUSE", "decimals": 18}, "rpc": ["https://rpc.fuse.io", "https://fuse.drpc.org", "wss://fuse.drpc.org"], "faucets": [], "infoURL": "https://fuse.io/"}, {"name": "<PERSON><PERSON>", "chainId": 123, "shortName": "spark", "networkId": 123, "nativeCurrency": {"name": "Spark", "symbol": "SPARK", "decimals": 18}, "rpc": ["https://rpc.fusespark.io"], "faucets": ["https://get.fusespark.io"], "infoURL": "https://docs.fuse.io/general/fuse-network-blockchain/fuse-testnet"}, {"name": "Decentralized Web Mainnet", "chainId": 124, "shortName": "dwu", "networkId": 124, "nativeCurrency": {"name": "Decentralized Web Utility", "symbol": "DWU", "decimals": 18}, "rpc": ["https://decentralized-web.tech/dw_rpc.php"], "faucets": [], "infoURL": "https://decentralized-web.tech/dw_chain.php"}, {"name": "OYchain Testnet", "chainId": 125, "shortName": "OYchainTestnet", "networkId": 125, "nativeCurrency": {"name": "OYchain <PERSON>", "symbol": "OY", "decimals": 18}, "rpc": ["https://rpc.testnet.oychain.io"], "faucets": ["https://faucet.oychain.io"], "infoURL": "https://www.oychain.io"}, {"name": "OYchain Mainnet", "chainId": 126, "shortName": "OYchainMainnet", "networkId": 126, "nativeCurrency": {"name": "OYchain <PERSON>", "symbol": "OY", "decimals": 18}, "rpc": ["https://rpc.mainnet.oychain.io"], "faucets": [], "infoURL": "https://www.oychain.io"}, {"name": "Factory 127 Mainnet", "chainId": 127, "shortName": "feth", "networkId": 127, "nativeCurrency": {"name": "Factory 127 Token", "symbol": "FETH", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://www.factory127.com"}, {"name": "Huobi ECO Chain Mainnet", "chainId": 128, "shortName": "heco", "networkId": 128, "nativeCurrency": {"name": "Huobi ECO Chain Native Token", "symbol": "HT", "decimals": 18}, "rpc": ["https://http-mainnet.hecochain.com", "wss://ws-mainnet.hecochain.com"], "faucets": [], "infoURL": "https://www.hecochain.com"}, {"name": "Innovator Chain", "chainId": 129, "shortName": "Innovator", "networkId": 129, "nativeCurrency": {"name": "INOV8", "symbol": "INOV8", "decimals": 18}, "rpc": ["https://rpc.innovatorchain.com"], "faucets": [], "infoURL": "https://innovatorchain.com"}, {"name": "Engram Testnet", "chainId": 131, "shortName": "tgram", "networkId": 131, "nativeCurrency": {"name": "Engram Tokio Testnet", "symbol": "tGRAM", "decimals": 18}, "rpc": ["https://tokioswift.engram.tech", "https://tokio-archive.engram.tech"], "faucets": [], "infoURL": "https://engramnet.io"}, {"name": "HashKey Chain Testnet", "chainId": 133, "shortName": "HSKT", "networkId": 133, "nativeCurrency": {"name": "HashKey EcoPoints", "symbol": "HSK", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://hashkey.cloud"}, {"name": "iExec Sidechain", "chainId": 134, "shortName": "rlc", "networkId": 134, "nativeCurrency": {"name": "xRLC", "symbol": "xRLC", "decimals": 18}, "rpc": ["https://bellecour.iex.ec"], "faucets": [], "infoURL": "https://iex.ec"}, {"name": "Alyx Chain Testnet", "chainId": 135, "shortName": "AlyxTestnet", "networkId": 135, "nativeCurrency": {"name": "Alyx Testnet Native Token", "symbol": "ALYX", "decimals": 18}, "rpc": ["https://testnet-rpc.alyxchain.com"], "faucets": ["https://faucet.alyxchain.com"], "infoURL": "https://www.alyxchain.com"}, {"name": "Deamchain Mainnet", "chainId": 136, "shortName": "deam", "networkId": 136, "nativeCurrency": {"name": "Deamchain Native Token", "symbol": "DEAM", "decimals": 18}, "rpc": ["https://mainnet.deamchain.com"], "faucets": [], "infoURL": "https://deamchain.com"}, {"name": "Polygon Mainnet", "chainId": 137, "shortName": "matic", "networkId": 137, "nativeCurrency": {"name": "MATIC", "symbol": "MATIC", "decimals": 18}, "rpc": ["https://polygon-rpc.com/", "https://rpc-mainnet.matic.network", "https://matic-mainnet.chainstacklabs.com", "https://rpc-mainnet.maticvigil.com", "https://rpc-mainnet.matic.quiknode.pro", "https://matic-mainnet-full-rpc.bwarelabs.com", "https://polygon-bor-rpc.publicnode.com", "wss://polygon-bor-rpc.publicnode.com", "https://polygon.gateway.tenderly.co", "wss://polygon.gateway.tenderly.co", "https://polygon.drpc.org", "wss://polygon.drpc.org"], "faucets": [], "infoURL": "https://polygon.technology/"}, {"name": "Defi Oracle Meta Mainnet", "chainId": 138, "shortName": "dfio-meta-main", "networkId": 1, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.defi-oracle.io", "wss://wss.defi-oracle.io"], "faucets": [], "infoURL": "https://info.defi-oracle.io/"}, {"name": "WoopChain Mainnet", "chainId": 139, "shortName": "woop", "networkId": 139, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "WOOC", "decimals": 18}, "rpc": ["https://rpc.woop.ai/rpc"], "faucets": [], "infoURL": "https://wikiwoop.com"}, {"name": "Openpiece Testnet", "chainId": 141, "shortName": "OPtest", "networkId": 141, "nativeCurrency": {"name": "<PERSON>y", "symbol": "BELLY", "decimals": 18}, "rpc": ["https://testnet.openpiece.io"], "faucets": [], "infoURL": "https://cryptopiece.online"}, {"name": "DAX CHAIN", "chainId": 142, "shortName": "dax", "networkId": 142, "nativeCurrency": {"name": "Prodax", "symbol": "DAX", "decimals": 18}, "rpc": ["https://rpc.prodax.io"], "faucets": [], "infoURL": "https://prodax.io/"}, {"name": "PHI Network v2", "chainId": 144, "shortName": "PHI", "networkId": 144, "nativeCurrency": {"name": "PHI", "symbol": "Φ", "decimals": 18}, "rpc": ["https://connect.phi.network"], "faucets": [], "infoURL": "https://phi.network"}, {"name": "SoraAI Testnet", "chainId": 145, "shortName": "SETH", "networkId": 145, "nativeCurrency": {"name": "SoraETH", "symbol": "SETH", "decimals": 18}, "rpc": ["https://rpc-testnet.soraai.bot"], "faucets": [], "infoURL": "https://soraai.bot"}, {"name": "ShimmerEVM", "chainId": 148, "shortName": "shimmerevm", "networkId": 148, "nativeCurrency": {"name": "SMR", "symbol": "SMR", "decimals": 18}, "rpc": ["https://json-rpc.evm.shimmer.network"], "faucets": [], "infoURL": "https://shimmer.network"}, {"name": "Six Protocol Testnet", "chainId": 150, "shortName": "sixt", "networkId": 150, "nativeCurrency": {"name": "SIX testnet evm token", "symbol": "tSIX", "decimals": 18}, "rpc": ["https://rpc-evm.fivenet.sixprotocol.net"], "faucets": ["https://faucet.sixprotocol.net"], "infoURL": "https://six.network/"}, {"name": "Redbelly Network Mainnet", "chainId": 151, "shortName": "rbn", "networkId": 151, "nativeCurrency": {"name": "Redbelly Network Coin", "symbol": "RBNT", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://redbelly.network"}, {"name": "Redbelly Network Devnet", "chainId": 152, "shortName": "rbn-devnet", "networkId": 152, "nativeCurrency": {"name": "Redbelly Network Coin", "symbol": "RBNT", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://redbelly.network"}, {"name": "Redbelly Network Testnet", "chainId": 153, "shortName": "rbn-testnet", "networkId": 153, "nativeCurrency": {"name": "Redbelly Network Coin", "symbol": "RBNT", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://redbelly.network"}, {"name": "Redbelly Network TGE", "chainId": 154, "shortName": "rbn-tge", "networkId": 154, "nativeCurrency": {"name": "Redbelly Network Coin", "symbol": "RBNT", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://redbelly.network"}, {"name": "Tenet Testnet", "chainId": 155, "shortName": "tenet-testnet", "networkId": 155, "nativeCurrency": {"name": "TENET", "symbol": "TENET", "decimals": 18}, "rpc": ["https://rpc.testnet.tenet.org"], "faucets": ["https://faucet.testnet.tenet.org"], "infoURL": "https://tenet.org/"}, {"name": "OEBlock Testnet", "chainId": 156, "shortName": "obe", "networkId": 156, "nativeCurrency": {"name": "OEBlock", "symbol": "OEB", "decimals": 18}, "rpc": ["https://testnet-rpc.oeblock.com"], "faucets": [], "infoURL": "https://www.oeblock.com/"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Shibarium", "chainId": 157, "shortName": "puppynet", "networkId": 157, "nativeCurrency": {"name": "BONE", "symbol": "BONE", "decimals": 18}, "rpc": ["https://puppynet.shibrpc.com"], "faucets": ["https://beta.shibariumtech.com/faucet"], "infoURL": "https://shibariumecosystem.com"}, {"name": "Roburna Mainnet", "chainId": 158, "shortName": "rba", "networkId": 158, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "RBA", "decimals": 18}, "rpc": ["https://dataseed.roburna.com"], "faucets": [], "infoURL": "https://www.roburna.com/"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 159, "shortName": "rbat", "networkId": 159, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "RBAT", "decimals": 18}, "rpc": ["https://preseed-testnet-1.roburna.com"], "faucets": [], "infoURL": "https://www.roburna.com/"}, {"name": "Armonia Eva Chain Mainnet", "chainId": 160, "shortName": "eva", "networkId": 160, "nativeCurrency": {"name": "Armonia Multichain Native Token", "symbol": "AMAX", "decimals": 18}, "rpc": ["https://evascan.io/api/eth-rpc/"], "faucets": [], "infoURL": "https://amax.network"}, {"name": "Armonia Eva Chain Testnet", "chainId": 161, "shortName": "wall-e", "networkId": 161, "nativeCurrency": {"name": "Armonia Multichain Native Token", "symbol": "AMAX", "decimals": 18}, "rpc": ["https://testnet.evascan.io/api/eth-rpc/"], "faucets": [], "infoURL": "https://amax.network"}, {"name": "Lightstreams Testnet", "chainId": 162, "shortName": "tpht", "networkId": 162, "nativeCurrency": {"name": "Lightstreams PHT", "symbol": "PHT", "decimals": 18}, "rpc": ["https://node.sirius.lightstreams.io"], "faucets": ["https://discuss.lightstreams.network/t/request-test-tokens"], "infoURL": "https://explorer.sirius.lightstreams.io"}, {"name": "Lightstreams Mainnet", "chainId": 163, "shortName": "pht", "networkId": 163, "nativeCurrency": {"name": "Lightstreams PHT", "symbol": "PHT", "decimals": 18}, "rpc": ["https://node.mainnet.lightstreams.io"], "faucets": [], "infoURL": "https://explorer.lightstreams.io"}, {"name": "Omni Testnet (incubating)", "chainId": 164, "shortName": "omni_testnet_164", "networkId": 164, "nativeCurrency": {"name": "Omni", "symbol": "OMNI", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://docs.omni.network"}, {"name": "Omni Testnet", "chainId": 165, "shortName": "omni_testnet", "networkId": 165, "nativeCurrency": {"name": "Omni", "symbol": "OMNI", "decimals": 18}, "rpc": ["https://testnet.omni.network"], "faucets": [], "infoURL": "https://docs.omni.network/"}, {"name": "Omni", "chainId": 166, "shortName": "omni", "networkId": 166, "nativeCurrency": {"name": "Omni", "symbol": "OMNI", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://docs.omni.network/"}, {"name": "<PERSON><PERSON>", "chainId": 167, "shortName": "atoshi", "networkId": 167, "nativeCurrency": {"name": "ATOSHI", "symbol": "ATOS", "decimals": 18}, "rpc": ["https://node.atoshi.io/"], "faucets": [], "infoURL": "https://atoshi.org"}, {"name": "AIOZ Network", "chainId": 168, "shortName": "aioz", "networkId": 168, "nativeCurrency": {"name": "AIOZ", "symbol": "AIOZ", "decimals": 18}, "rpc": ["https://eth-dataseed.aioz.network"], "faucets": [], "infoURL": "https://aioz.network"}, {"name": "Manta Pacific Mainnet", "chainId": 169, "shortName": "manta", "networkId": 169, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://pacific-rpc.manta.network/http", "https://manta-pacific.drpc.org", "wss://manta-pacific.drpc.org"], "faucets": [], "infoURL": "https://pacific-info.manta.network"}, {"name": "HOO Smart Chain Testnet", "chainId": 170, "shortName": "hoosmartchain", "networkId": 170, "nativeCurrency": {"name": "HOO", "symbol": "HOO", "decimals": 18}, "rpc": ["https://http-testnet.hoosmartchain.com"], "faucets": ["https://faucet-testnet.hscscan.com/"], "infoURL": "https://www.hoosmartchain.com"}, {"name": "Latam-Blockchain Resil Testnet", "chainId": 172, "shortName": "resil", "networkId": 172, "nativeCurrency": {"name": "Latam-Blockchain Resil Test Native Token", "symbol": "usd", "decimals": 18}, "rpc": ["https://rpc.latam-blockchain.com", "wss://ws.latam-blockchain.com"], "faucets": ["https://faucet.latam-blockchain.com"], "infoURL": "https://latam-blockchain.com"}, {"name": "AME Chain Mainnet", "chainId": 180, "shortName": "ame", "networkId": 180, "nativeCurrency": {"name": "AME", "symbol": "AME", "decimals": 18}, "rpc": ["https://node1.amechain.io/"], "faucets": [], "infoURL": "https://amechain.io/"}, {"name": "Seele Mainnet", "chainId": 186, "shortName": "<PERSON><PERSON>", "networkId": 186, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "<PERSON><PERSON>", "decimals": 18}, "rpc": ["https://rpc.seelen.pro/"], "faucets": [], "infoURL": "https://seelen.pro/"}, {"name": "BMC Mainnet", "chainId": 188, "shortName": "BMC", "networkId": 188, "nativeCurrency": {"name": "BTM", "symbol": "BTM", "decimals": 18}, "rpc": ["https://mainnet.bmcchain.com/"], "faucets": [], "infoURL": "https://bmc.bytom.io/"}, {"name": "BMC Testnet", "chainId": 189, "shortName": "BMCT", "networkId": 189, "nativeCurrency": {"name": "BTM", "symbol": "BTM", "decimals": 18}, "rpc": ["https://testnet.bmcchain.com"], "faucets": [], "infoURL": "https://bmc.bytom.io/"}, {"name": "FileFileGo", "chainId": 191, "shortName": "ffg", "networkId": 191, "nativeCurrency": {"name": "FFG", "symbol": "FFG", "decimals": 18}, "rpc": ["https://rpc.filefilego.com/rpc"], "faucets": [], "infoURL": "https://filefilego.com"}, {"name": "Crypto Emergency", "chainId": 193, "shortName": "cem", "networkId": 193, "nativeCurrency": {"name": "Crypto Emergency", "symbol": "CEM", "decimals": 18}, "rpc": ["https://cemchain.com"], "faucets": [], "infoURL": "https://cemblockchain.com/"}, {"name": "X Layer Testnet", "chainId": 195, "shortName": "tokb", "networkId": 195, "nativeCurrency": {"name": "X Layer Global Utility Token in testnet", "symbol": "OKB", "decimals": 18}, "rpc": ["https://testrpc.xlayer.tech", "https://xlayertestrpc.okx.com"], "faucets": ["https://www.okx.com/xlayer/faucet"], "infoURL": "https://www.okx.com/xlayer"}, {"name": "X Layer Mainnet", "chainId": 196, "shortName": "okb", "networkId": 196, "nativeCurrency": {"name": "X Layer Global Utility Token", "symbol": "OKB", "decimals": 18}, "rpc": ["https://rpc.xlayer.tech", "https://xlayerrpc.okx.com"], "faucets": [], "infoURL": "https://www.okx.com/xlayer"}, {"name": "Neutrinos TestNet", "chainId": 197, "shortName": "NEUTR", "networkId": 197, "nativeCurrency": {"name": "Neutrinos", "symbol": "NEUTR", "decimals": 18}, "rpc": ["https://testnet-rpc.neutrinoschain.com"], "faucets": ["https://neutrinoschain.com/faucet"], "infoURL": "https://docs.neutrinoschain.com"}, {"name": "Bitchain Mainnet", "chainId": 198, "shortName": "bit", "networkId": 198, "nativeCurrency": {"name": "Bitcoin", "symbol": "BTC", "decimals": 18}, "rpc": ["https://rpc.bitchain.biz/"], "faucets": [], "infoURL": "https://www.bitchain.biz/"}, {"name": "BitTorrent Chain Mainnet", "chainId": 199, "shortName": "BTT", "networkId": 199, "nativeCurrency": {"name": "BitTorrent", "symbol": "BTT", "decimals": 18}, "rpc": ["https://rpc.bt.io", "https://bittorrent.drpc.org", "wss://bittorrent.drpc.org"], "faucets": [], "infoURL": "https://bt.io"}, {"name": "Arbitrum on xDai", "chainId": 200, "shortName": "aox", "networkId": 200, "nativeCurrency": {"name": "xDAI", "symbol": "xDAI", "decimals": 18}, "rpc": ["https://arbitrum.xdaichain.com/"], "faucets": [], "infoURL": "https://xdaichain.com"}, {"name": "MOAC testnet", "chainId": 201, "shortName": "moactest", "networkId": 201, "nativeCurrency": {"name": "MOAC", "symbol": "mc", "decimals": 18}, "rpc": ["https://gateway.moac.io/testnet"], "faucets": [], "infoURL": "https://moac.io"}, {"name": "Edgeless Testnet", "chainId": 202, "shortName": "edgeless-testnet", "networkId": 202, "nativeCurrency": {"name": "<PERSON><PERSON> Wrapped Eth", "symbol": "EwEth", "decimals": 18}, "rpc": ["https://testnet.rpc.edgeless.network/http"], "faucets": [], "infoURL": "https://edgeless.network"}, {"name": "opBNB Mainnet", "chainId": 204, "shortName": "obnb", "networkId": 204, "nativeCurrency": {"name": "BNB Chain Native Token", "symbol": "BNB", "decimals": 18}, "rpc": ["https://opbnb-mainnet-rpc.bnbchain.org", "https://opbnb-mainnet.nodereal.io/v1/64a9df0874fb4a93b9d0a3849de012d3", "wss://opbnb-mainnet.nodereal.io/ws/v1/64a9df0874fb4a93b9d0a3849de012d3", "https://opbnb-mainnet.nodereal.io/v1/e9a36765eb8a40b9bd12e680a1fd2bc5", "wss://opbnb-mainnet.nodereal.io/ws/v1/e9a36765eb8a40b9bd12e680a1fd2bc5", "https://opbnb-rpc.publicnode.com", "wss://opbnb-rpc.publicnode.com", "https://opbnb.drpc.org", "wss://opbnb.drpc.org"], "faucets": [], "infoURL": "https://opbnb.bnbchain.org/en"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 206, "shortName": "VCTEST", "networkId": 206, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "VC", "decimals": 18}, "rpc": ["https://vinufoundation-rpc.com"], "faucets": [], "infoURL": "https://vitainu.org"}, {"name": "VinuChain Network", "chainId": 207, "shortName": "VC", "networkId": 207, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "VC", "decimals": 18}, "rpc": ["https://vinuchain-rpc.com"], "faucets": [], "infoURL": "https://vitainu.org"}, {"name": "Structx Mainnet", "chainId": 208, "shortName": "utx", "networkId": 208, "nativeCurrency": {"name": "Notes", "symbol": "utx", "decimals": 18}, "rpc": ["https://mainnet.structx.io"], "faucets": [], "infoURL": "https://structx.io"}, {"name": "Bitnet", "chainId": 210, "shortName": "BTN", "networkId": 210, "nativeCurrency": {"name": "Bitnet", "symbol": "BTN", "decimals": 18}, "rpc": ["https://rpc.bitnet.money", "https://rpc.btnscan.com"], "faucets": [], "infoURL": "https://bitnet.money"}, {"name": "Freight Trust Network", "chainId": 211, "shortName": "EDI", "networkId": 0, "nativeCurrency": {"name": "Freight Trust Native", "symbol": "0xF", "decimals": 18}, "rpc": ["http://13.57.207.168:3435", "https://app.freighttrust.net/ftn/${API_KEY}"], "faucets": ["http://faucet.freight.sh"], "infoURL": "https://freighttrust.com"}, {"name": "MAPO Makalu", "chainId": 212, "shortName": "makalu", "networkId": 212, "nativeCurrency": {"name": "Makalu MAPO", "symbol": "MAPO", "decimals": 18}, "rpc": ["https://testnet-rpc.maplabs.io"], "faucets": ["https://faucet.mapprotocol.io"], "infoURL": "https://mapprotocol.io/"}, {"name": "B2 Hub Mainnet", "chainId": 213, "shortName": "B2Hub-mainnet", "networkId": 213, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "B2", "decimals": 18}, "rpc": ["https://hub-rpc.bsquared.network"], "faucets": [], "infoURL": "https://www.bsquared.network"}, {"name": "Shinarium Mainnet", "chainId": 214, "shortName": "shinarium", "networkId": 214, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "SHI", "decimals": 18}, "rpc": ["https://mainnet.shinarium.org"], "faucets": [], "infoURL": "https://shinarium.org"}, {"name": "SiriusNet V2", "chainId": 217, "shortName": "SIN2", "networkId": 217, "nativeCurrency": {"name": "MCD", "symbol": "MCD", "decimals": 18}, "rpc": ["https://rpc2.siriusnet.io"], "faucets": [], "infoURL": "https://siriusnet.io"}, {"name": "SoterOne Mainnet old", "chainId": 218, "shortName": "SO1-old", "networkId": 218, "nativeCurrency": {"name": "SoterOne Mainnet Ether", "symbol": "SOTER", "decimals": 18}, "rpc": ["https://rpc.soter.one"], "faucets": [], "infoURL": "https://www.soterone.com"}, {"name": "Scalind Testnet", "chainId": 220, "shortName": "sepscal", "networkId": 220, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-sepolia.scalind.com"], "faucets": ["https://faucet.scalind.com"], "infoURL": "https://scalind.com"}, {"name": "Permission", "chainId": 222, "shortName": "ASK", "networkId": 2221, "nativeCurrency": {"name": "ASK", "symbol": "ASK", "decimals": 18}, "rpc": ["https://blockchain-api-mainnet.permission.io/rpc"], "faucets": [], "infoURL": "https://permission.io/"}, {"name": "B2 Mainnet", "chainId": 223, "shortName": "B2-mainnet", "networkId": 223, "nativeCurrency": {"name": "Bitcoin", "symbol": "BTC", "decimals": 18}, "rpc": ["https://mainnet.b2-rpc.com", "https://rpc.bsquared.network", "https://b2-mainnet.alt.technology", "https://b2-mainnet-public.s.chainbase.com"], "faucets": [], "infoURL": "https://www.bsquared.network"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 224, "shortName": "VRD-Testnet", "networkId": 224, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "VRD", "decimals": 18}, "rpc": ["https://testnet-rpc.vrd.network"], "faucets": ["https://faucet.vrd.network"], "infoURL": "https://viridis.network"}, {"name": "LACHAIN Mainnet", "chainId": 225, "shortName": "LA", "networkId": 225, "nativeCurrency": {"name": "LA", "symbol": "LA", "decimals": 18}, "rpc": ["https://rpc-mainnet.lachain.io"], "faucets": [], "infoURL": "https://lachain.io"}, {"name": "LACHAIN Testnet", "chainId": 226, "shortName": "TLA", "networkId": 226, "nativeCurrency": {"name": "TLA", "symbol": "TLA", "decimals": 18}, "rpc": ["https://rpc-testnet.lachain.io"], "faucets": [], "infoURL": "https://lachain.io"}, {"name": "Mind Network Mainnet", "chainId": 228, "shortName": "fhe", "networkId": 228, "nativeCurrency": {"name": "FHE", "symbol": "FHE", "decimals": 18}, "rpc": ["https://rpc_mainnet.mindnetwork.xyz", "wss://rpc_mainnet.mindnetwork.xyz"], "faucets": [], "infoURL": "https://mindnetwork.xyz"}, {"name": "SwapDEX", "chainId": 230, "shortName": "SDX", "networkId": 230, "nativeCurrency": {"name": "SwapDEX", "symbol": "SDX", "decimals": 18}, "rpc": ["https://rpc.swapdex.network", "wss://ss.swapdex.network"], "faucets": [], "infoURL": "https://swapdex.network/"}, {"name": "ProtoJumbo Testnet", "chainId": 234, "shortName": "ProtoJumbo", "networkId": 234, "nativeCurrency": {"name": "JNFTC", "symbol": "JNFTC", "decimals": 18}, "rpc": ["https://testnode.jumbochain.org"], "faucets": ["https://protojumbo.jumbochain.org/faucet-smart"], "infoURL": "https://jumbochain.org"}, {"name": "Deam<PERSON>in Testnet", "chainId": 236, "shortName": "deamtest", "networkId": 236, "nativeCurrency": {"name": "Deamchain Native Token", "symbol": "DEAM", "decimals": 18}, "rpc": ["https://testnet.deamchain.com"], "faucets": ["https://faucet.deamchain.com"], "infoURL": "https://deamchain.com"}, {"name": "Blast Mainnet", "chainId": 238, "shortName": "blast", "networkId": 238, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.blastblockchain.com"], "faucets": [], "infoURL": "https://docs.blastblockchain.com"}, {"name": "Plinga Mainnet", "chainId": 242, "shortName": "plgchain", "networkId": 242, "nativeCurrency": {"name": "Plinga", "symbol": "PLINGA", "decimals": 18}, "rpc": ["https://rpcurl.mainnet.plgchain.com", "https://rpcurl.plgchain.blockchain.evmnode.online", "https://rpcurl.mainnet.plgchain.plinga.technology"], "faucets": [], "infoURL": "https://www.plinga.technology/"}, {"name": "Energy Web Chain", "chainId": 246, "shortName": "ewt", "networkId": 246, "nativeCurrency": {"name": "Energy Web Token", "symbol": "EWT", "decimals": 18}, "rpc": ["https://rpc.energyweb.org", "wss://rpc.energyweb.org/ws"], "faucets": [], "infoURL": "https://energyweb.org"}, {"name": "Oasys Mainnet", "chainId": 248, "shortName": "OAS", "networkId": 248, "nativeCurrency": {"name": "OAS", "symbol": "OAS", "decimals": 18}, "rpc": ["https://rpc.mainnet.oasys.games"], "faucets": [], "infoURL": "https://oasys.games"}, {"name": "Fantom Opera", "chainId": 250, "shortName": "ftm", "networkId": 250, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "FTM", "decimals": 18}, "rpc": ["https://rpc.ftm.tools", "https://fantom-rpc.publicnode.com", "wss://fantom-rpc.publicnode.com", "https://fantom.drpc.org", "wss://fantom.drpc.org"], "faucets": [], "infoURL": "https://fantom.foundation"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 252, "shortName": "fraxtal", "networkId": 252, "nativeCurrency": {"name": "Fr<PERSON>", "symbol": "frxETH", "decimals": 18}, "rpc": ["https://rpc.frax.com"], "faucets": [], "infoURL": "https://mainnet.frax.com"}, {"name": "Kroma", "chainId": 255, "shortName": "kroma", "networkId": 255, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://api.kroma.network"], "faucets": [], "infoURL": "https://kroma.network"}, {"name": "Huobi ECO Chain Testnet", "chainId": 256, "shortName": "he<PERSON>", "networkId": 256, "nativeCurrency": {"name": "Huobi ECO Chain Test Native Token", "symbol": "htt", "decimals": 18}, "rpc": ["https://http-testnet.hecochain.com", "wss://ws-testnet.hecochain.com"], "faucets": ["https://scan-testnet.hecochain.com/faucet"], "infoURL": "https://testnet.hecoinfo.com"}, {"name": "Setheum", "chainId": 258, "shortName": "setm", "networkId": 258, "nativeCurrency": {"name": "Setheum", "symbol": "SETM", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://setheum.xyz"}, {"name": "Neonlink Mainnet", "chainId": 259, "shortName": "neon", "networkId": 259, "nativeCurrency": {"name": "Neonlink Native Token", "symbol": "NEON", "decimals": 18}, "rpc": ["https://mainnet.neonlink.io"], "faucets": [], "infoURL": "https://neonlink.io"}, {"name": "SUR Blockchain Network", "chainId": 262, "shortName": "SUR", "networkId": 1, "nativeCurrency": {"name": "<PERSON>n", "symbol": "SRN", "decimals": 18}, "rpc": ["https://sur.nilin.org"], "faucets": [], "infoURL": "https://surnet.org"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 266, "shortName": "neura", "networkId": 266, "nativeCurrency": {"name": "Ankr", "symbol": "ANKR", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://www.neuraprotocol.io/"}, {"name": "<PERSON><PERSON>a <PERSON>net", "chainId": 267, "shortName": "tneura", "networkId": 267, "nativeCurrency": {"name": "Testnet Ankr", "symbol": "ANKR", "decimals": 18}, "rpc": ["https://rpc.ankr.com/neura_testnet"], "faucets": ["https://testnet.neuraprotocol.io/faucet"], "infoURL": "https://www.neuraprotocol.io/"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 268, "shortName": "dneura", "networkId": 268, "nativeCurrency": {"name": "Devnet Ankr", "symbol": "ANKR", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://www.neuraprotocol.io/"}, {"name": "High Performance Blockchain", "chainId": 269, "shortName": "hpb", "networkId": 269, "nativeCurrency": {"name": "High Performance Blockchain Ether", "symbol": "HPB", "decimals": 18}, "rpc": ["https://hpbnode.com", "wss://ws.hpbnode.com"], "faucets": ["https://myhpbwallet.com/"], "infoURL": "https://hpb.io"}, {"name": "EgonCoin Mainnet", "chainId": 271, "shortName": "EGONm", "networkId": 271, "nativeCurrency": {"name": "EgonCoin", "symbol": "EGON", "decimals": 18}, "rpc": ["https://rpc.egonscan.com"], "faucets": [], "infoURL": "https://egonscan.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 274, "shortName": "lachain", "networkId": 274, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "LAC", "decimals": 18}, "rpc": ["https://rpc1.mainnet.lachain.network", "https://rpc2.mainnet.lachain.network", "https://lachain.rpc-nodes.cedalio.dev"], "faucets": [], "infoURL": ""}, {"name": "xFair.AI Mainnet", "chainId": 278, "shortName": "fai", "networkId": 278, "nativeCurrency": {"name": "FAI", "symbol": "FAI", "decimals": 18}, "rpc": ["https://rpc_mainnet.xfair.ai", "wss://rpc_mainnet.xfair.ai"], "faucets": [], "infoURL": "https://xfair.ai"}, {"name": "BPX Blockchain", "chainId": 279, "shortName": "bpx", "networkId": 279, "nativeCurrency": {"name": "BPX", "symbol": "BPX", "decimals": 18}, "rpc": ["https://rpc.mainnet.bpxchain.cc", "https://bpx-dataseed.infinex.cc"], "faucets": [], "infoURL": "https://bpxchain.cc"}, {"name": "zkSync Era Goerli Testnet (deprecated)", "chainId": 280, "shortName": "zksync-goerli", "networkId": 280, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://testnet.era.zksync.dev"], "faucets": [], "infoURL": "https://zksync.io/"}, {"name": "Cronos zkEVM Testnet", "chainId": 282, "shortName": "zktcro", "networkId": 282, "nativeCurrency": {"name": "Cronos zkEVM Test Coin", "symbol": "TCRO", "decimals": 18}, "rpc": ["https://rpc-zkevm-t0.cronos.org"], "faucets": ["https://zkevm-t0.cronos.org/faucet"], "infoURL": "https://docs.cronos.org/cronos-zkevm-chain/introduction-to-cronos-zkevm-chain-testnet"}, {"name": "Boba Network", "chainId": 288, "shortName": "<PERSON><PERSON>", "networkId": 288, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://mainnet.boba.network", "https://replica.boba.network", "https://boba-ethereum.gateway.tenderly.co", "https://gateway.tenderly.co/public/boba-ethereum", "wss://boba-ethereum.gateway.tenderly.co/", "wss://gateway.tenderly.co/public/boba-ethereum", "https://boba-eth.drpc.org", "wss://boba-eth.drpc.org"], "faucets": [], "infoURL": "https://boba.network"}, {"name": "Orderly Mainnet", "chainId": 291, "shortName": "orderly", "networkId": 291, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.orderly.network", "https://l2-orderly-mainnet-0.t.conduit.xyz"], "faucets": [], "infoURL": "www.orderly.network"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 295, "shortName": "hedera-mainnet", "networkId": 295, "nativeCurrency": {"name": "hbar", "symbol": "HBAR", "decimals": 18}, "rpc": ["https://mainnet.hashio.io/api"], "faucets": [], "infoURL": "https://hedera.com"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 296, "shortName": "hedera-testnet", "networkId": 296, "nativeCurrency": {"name": "hbar", "symbol": "HBAR", "decimals": 18}, "rpc": ["https://testnet.hashio.io/api"], "faucets": ["https://portal.hedera.com"], "infoURL": "https://hedera.com"}, {"name": "Hedera Previewnet", "chainId": 297, "shortName": "hedera-previewnet", "networkId": 297, "nativeCurrency": {"name": "hbar", "symbol": "HBAR", "decimals": 18}, "rpc": ["https://previewnet.hashio.io/api"], "faucets": ["https://portal.hedera.com"], "infoURL": "https://hedera.com"}, {"name": "Hedera Localnet", "chainId": 298, "shortName": "hedera-localnet", "networkId": 298, "nativeCurrency": {"name": "hbar", "symbol": "HBAR", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://hedera.com"}, {"name": "zkSync Sepolia Testnet", "chainId": 300, "shortName": "zksync-sepolia", "networkId": 300, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://sepolia.era.zksync.dev", "https://zksync-sepolia.drpc.org", "wss://zksync-sepolia.drpc.org"], "faucets": [], "infoURL": "https://zksync.io/"}, {"name": "Bobaopera", "chainId": 301, "shortName": "Bobaopera", "networkId": 301, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "BOBA", "decimals": 18}, "rpc": ["https://bobaopera.boba.network", "wss://wss.bobaopera.boba.network", "https://replica.bobaopera.boba.network", "wss://replica-wss.bobaopera.boba.network"], "faucets": [], "infoURL": "https://boba.network"}, {"name": "zkCandy Sepolia Testnet", "chainId": 302, "shortName": "zkcandy-sepolia", "networkId": 302, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://sepolia.rpc.zkcandy.io"], "faucets": [], "infoURL": "https://zkcandy.io/"}, {"name": "Neurochain <PERSON>net", "chainId": 303, "shortName": "ncnt", "networkId": 303, "nativeCurrency": {"name": "Neur<PERSON><PERSON>", "symbol": "tNCN", "decimals": 18}, "rpc": ["https://nc-rpc-test1.neurochain.io"], "faucets": [], "infoURL": "https://www.neurochain.ai"}, {"name": "ZKSats Mainnet", "chainId": 305, "shortName": "ZKSats-Mainnet", "networkId": 305, "nativeCurrency": {"name": "BTC", "symbol": "BTC", "decimals": 18}, "rpc": ["https://mainnet.zksats.io"], "faucets": [], "infoURL": "https://zksats.io"}, {"name": "Lovely Network Testnet", "chainId": 307, "shortName": "LOVELY-Testnet", "networkId": 307, "nativeCurrency": {"name": "Lovely", "symbol": "LOVELY", "decimals": 18}, "rpc": ["https://trpc.lovely.network"], "faucets": ["https://faucet.lovely.network"], "infoURL": "https://lovely.network"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 308, "shortName": "furtheon", "networkId": 308, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "FTH", "decimals": 18}, "rpc": ["https://rpc.furtheon.org"], "faucets": [], "infoURL": "https://furtheon.org/"}, {"name": "Wyzth Testnet", "chainId": 309, "shortName": "wyz", "networkId": 309, "nativeCurrency": {"name": "Wyzth", "symbol": "WYZ", "decimals": 18}, "rpc": ["https://rpc-testnet3.wyzthchain.org/"], "faucets": [], "infoURL": "https://wyzth.org/"}, {"name": "Omax Mainnet", "chainId": 311, "shortName": "omax", "networkId": 311, "nativeCurrency": {"name": "OMAX COIN", "symbol": "OMAX", "decimals": 18}, "rpc": ["https://mainapi.omaxray.com"], "faucets": ["https://faucet.omaxray.com/"], "infoURL": "https://www.omaxcoin.com/"}, {"name": "Neurochain Mainnet", "chainId": 313, "shortName": "ncn", "networkId": 313, "nativeCurrency": {"name": "Neur<PERSON><PERSON>", "symbol": "NCN", "decimals": 18}, "rpc": ["https://nc-rpc-prd1.neurochain.io", "https://nc-rpc-prd2.neurochain.io"], "faucets": [], "infoURL": "https://www.neurochain.ai"}, {"name": "Filecoin - Mainnet", "chainId": 314, "shortName": "filecoin", "networkId": 314, "nativeCurrency": {"name": "filecoin", "symbol": "FIL", "decimals": 18}, "rpc": ["https://api.node.glif.io/", "https://rpc.ankr.com/filecoin", "https://filecoin-mainnet.chainstacklabs.com/rpc/v1", "https://filfox.info/rpc/v1", "https://filecoin.drpc.org", "wss://filecoin.drpc.org"], "faucets": [], "infoURL": "https://filecoin.io"}, {"name": "KCC Mainnet", "chainId": 321, "shortName": "kcs", "networkId": 321, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "KCS", "decimals": 18}, "rpc": ["https://rpc-mainnet.kcc.network", "https://kcc.mytokenpocket.vip", "https://public-rpc.blockpi.io/http/kcc"], "faucets": [], "infoURL": "https://kcc.io"}, {"name": "KCC Testnet", "chainId": 322, "shortName": "kcst", "networkId": 322, "nativeCurrency": {"name": "KuCoin Testnet Token", "symbol": "tKCS", "decimals": 18}, "rpc": ["https://rpc-testnet.kcc.network"], "faucets": ["https://faucet-testnet.kcc.network"], "infoURL": "https://scan-testnet.kcc.network"}, {"name": "Cosvm Mainnet", "chainId": 323, "shortName": "cvm", "networkId": 323, "nativeCurrency": {"name": "Cosvm", "symbol": "CVM", "decimals": 18}, "rpc": ["https://rpc.cosvm.net"], "faucets": [], "infoURL": "https://cosvm.network"}, {"name": "zkSync Mainnet", "chainId": 324, "shortName": "zksync", "networkId": 324, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://mainnet.era.zksync.io", "https://zksync.drpc.org", "wss://zksync.drpc.org"], "faucets": [], "infoURL": "https://zksync.io/"}, {"name": "Web3Q Mainnet", "chainId": 333, "shortName": "w3q", "networkId": 333, "nativeCurrency": {"name": "Web3Q", "symbol": "W3Q", "decimals": 18}, "rpc": ["https://mainnet.web3q.io:8545"], "faucets": [], "infoURL": "https://web3q.io/home.w3q/"}, {"name": "DFK Chain Test", "chainId": 335, "shortName": "DFKTEST", "networkId": 335, "nativeCurrency": {"name": "Jewel", "symbol": "JEWEL", "decimals": 18}, "rpc": ["https://subnets.avax.network/defi-kingdoms/dfk-chain-testnet/rpc"], "faucets": [], "infoURL": "https://defikingdoms.com"}, {"name": "<PERSON>den", "chainId": 336, "shortName": "sdn", "networkId": 336, "nativeCurrency": {"name": "<PERSON>den", "symbol": "SDN", "decimals": 18}, "rpc": ["https://shiden.api.onfinality.io/public", "https://shiden-rpc.dwellir.com", "https://shiden.public.blastapi.io", "wss://shiden.api.onfinality.io/public-ws", "wss://shiden.public.blastapi.io", "wss://shiden-rpc.dwellir.com"], "faucets": [], "infoURL": "https://shiden.astar.network/"}, {"name": "Cronos Testnet", "chainId": 338, "shortName": "tcro", "networkId": 338, "nativeCurrency": {"name": "Cronos Test Coin", "symbol": "TCRO", "decimals": 18}, "rpc": ["https://evm-t3.cronos.org", "https://cronos-testnet.drpc.org", "wss://cronos-testnet.drpc.org"], "faucets": ["https://cronos.org/faucet"], "infoURL": "https://cronos.org"}, {"name": "Theta Mainnet", "chainId": 361, "shortName": "theta-mainnet", "networkId": 361, "nativeCurrency": {"name": "Theta Fuel", "symbol": "TFUEL", "decimals": 18}, "rpc": ["https://eth-rpc-api.thetatoken.org/rpc"], "faucets": [], "infoURL": "https://www.thetatoken.org/"}, {"name": "Theta Sapphire Testnet", "chainId": 363, "shortName": "theta-sapphire", "networkId": 363, "nativeCurrency": {"name": "Theta Fuel", "symbol": "TFUEL", "decimals": 18}, "rpc": ["https://eth-rpc-api-sapphire.thetatoken.org/rpc"], "faucets": [], "infoURL": "https://www.thetatoken.org/"}, {"name": "<PERSON><PERSON>", "chainId": 364, "shortName": "theta-amber", "networkId": 364, "nativeCurrency": {"name": "Theta Fuel", "symbol": "TFUEL", "decimals": 18}, "rpc": ["https://eth-rpc-api-amber.thetatoken.org/rpc"], "faucets": [], "infoURL": "https://www.thetatoken.org/"}, {"name": "Theta Testnet", "chainId": 365, "shortName": "theta-testnet", "networkId": 365, "nativeCurrency": {"name": "Theta Fuel", "symbol": "TFUEL", "decimals": 18}, "rpc": ["https://eth-rpc-api-testnet.thetatoken.org/rpc"], "faucets": [], "infoURL": "https://www.thetatoken.org/"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 369, "shortName": "pls", "networkId": 369, "nativeCurrency": {"name": "Pulse", "symbol": "PLS", "decimals": 18}, "rpc": ["https://rpc.pulsechain.com", "wss://rpc.pulsechain.com", "https://pulsechain-rpc.publicnode.com", "wss://pulsechain-rpc.publicnode.com", "https://rpc-pulsechain.g4mm4.io", "wss://rpc-pulsechain.g4mm4.io"], "faucets": [], "infoURL": "https://pulsechain.com/"}, {"name": "Consta Testnet", "chainId": 371, "shortName": "tCNT", "networkId": 371, "nativeCurrency": {"name": "tCNT", "symbol": "tCNT", "decimals": 18}, "rpc": ["https://rpc-testnet.theconsta.com"], "faucets": [], "infoURL": "http://theconsta.com"}, {"name": "ZKAmoeba Testnet", "chainId": 380, "shortName": "zkamoeba-test", "networkId": 380, "nativeCurrency": {"name": "filecoin", "symbol": "FIL", "decimals": 18}, "rpc": ["https://rpc.testnet.zkamoeba.com:4050/", "https://rpc1.testnet.zkamoeba.com:4050/"], "faucets": [], "infoURL": "https://testnet.zkamoeba.com"}, {"name": "ZKAmoeba Mainnet", "chainId": 381, "shortName": "<PERSON>ka<PERSON>eb<PERSON>", "networkId": 381, "nativeCurrency": {"name": "filecoin", "symbol": "FIL", "decimals": 18}, "rpc": ["https://rpc.mainnet.zkamoeba.com/rpc"], "faucets": [], "infoURL": "https://www.zkamoeba.com"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 385, "shortName": "lisinski", "networkId": 385, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "LISINS", "decimals": 18}, "rpc": ["https://rpc-bitfalls1.lisinski.online"], "faucets": ["https://pipa.lisinski.online"], "infoURL": "https://lisinski.online"}, {"name": "CamDL Testnet", "chainId": 395, "shortName": "camdl-testnet", "networkId": 395, "nativeCurrency": {"name": "CADL", "symbol": "CADL", "decimals": 18}, "rpc": ["https://rpc1.testnet.camdl.gov.kh/"], "faucets": ["https://faucet.testnet.camdl.gov.kh/"], "infoURL": "https://camdl.gov.kh/"}, {"name": "Near Mainnet", "chainId": 397, "shortName": "near", "networkId": 397, "nativeCurrency": {"name": "NEAR", "symbol": "NEAR", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://near.org/"}, {"name": "Near Testnet", "chainId": 398, "shortName": "near-testnet", "networkId": 398, "nativeCurrency": {"name": "Testnet NEAR", "symbol": "NEAR", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://aurora.dev"}, {"name": "Nativ3 Mainnet", "chainId": 399, "shortName": "N3", "networkId": 399, "nativeCurrency": {"name": "USNT", "symbol": "USNT", "decimals": 18}, "rpc": ["https://rpc.nativ3.network", "wss://ws.nativ3.network"], "faucets": [], "infoURL": "https://nativ3.network"}, {"name": "HyperonChain TestNet", "chainId": 400, "shortName": "hpn", "networkId": 400, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "HPN", "decimals": 18}, "rpc": ["https://testnet-rpc.hyperonchain.com"], "faucets": ["https://faucet.hyperonchain.com"], "infoURL": "https://docs.hyperonchain.com"}, {"name": "Ozone Chain Testnet", "chainId": 401, "shortName": "ozo_tst", "networkId": 401, "nativeCurrency": {"name": "OZONE", "symbol": "OZO", "decimals": 18}, "rpc": ["https://node1.testnet.ozonechain.io"], "faucets": [], "infoURL": "https://ozonechain.io"}, {"name": "Syndr L3", "chainId": 404, "shortName": "syndr-l3", "networkId": 404, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.syndr.com", "wss://rpc.syndr.com/ws"], "faucets": [], "infoURL": "https://syndr.com"}, {"name": "Pepe Chain Mainnet", "chainId": 411, "shortName": "pepe", "networkId": 411, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "PEPE", "decimals": 18}, "rpc": ["https://rpc.pepe-chain.vip"], "faucets": [], "infoURL": "https://pepe-chain.vip"}, {"name": "SX Network Mainnet", "chainId": 416, "shortName": "SX", "networkId": 416, "nativeCurrency": {"name": "SX Network", "symbol": "SX", "decimals": 18}, "rpc": ["https://rpc.sx.technology"], "faucets": [], "infoURL": "https://www.sx.technology"}, {"name": "LaTestnet", "chainId": 418, "shortName": "latestnet", "networkId": 418, "nativeCurrency": {"name": "Test LaCoin", "symbol": "TLA", "decimals": 18}, "rpc": ["https://rpc.testnet.lachain.network", "https://lachain-testnet.rpc-nodes.cedalio.dev"], "faucets": ["https://faucet.lachain.network"], "infoURL": ""}, {"name": "Optimism <PERSON><PERSON><PERSON>net", "chainId": 420, "shortName": "ogor", "networkId": 420, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://goerli.optimism.io", "https://optimism-goerli-rpc.publicnode.com", "wss://optimism-goerli-rpc.publicnode.com", "https://optimism-goerli.gateway.tenderly.co", "wss://optimism-goerli.gateway.tenderly.co", "https://optimism-testnet.drpc.org", "wss://optimism-testnet.drpc.org"], "faucets": [], "infoURL": "https://optimism.io"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 422, "shortName": "vrd", "networkId": 422, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "VRD", "decimals": 18}, "rpc": ["https://mainnet-rpc.vrd.network"], "faucets": [], "infoURL": "https://viridis.network"}, {"name": "PGN (Public Goods Network)", "chainId": 424, "shortName": "PGN", "networkId": 424, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.publicgoods.network"], "faucets": [], "infoURL": "https://publicgoods.network/"}, {"name": "Zeeth Chain", "chainId": 427, "shortName": "zeeth", "networkId": 427, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "ZTH", "decimals": 18}, "rpc": ["https://rpc.zeeth.io"], "faucets": [], "infoURL": ""}, {"name": "Geso Verse", "chainId": 428, "shortName": "GSV", "networkId": 428, "nativeCurrency": {"name": "OAS", "symbol": "OAS", "decimals": 18}, "rpc": ["https://rpc.verse.gesoten.com/"], "faucets": [], "infoURL": "https://gesoten.com/"}, {"name": "Boyaa Mainnet", "chainId": 434, "shortName": "BYC", "networkId": 434, "nativeCurrency": {"name": "Boyaa mainnet native coin", "symbol": "BYC", "decimals": 18}, "rpc": ["https://evm-rpc.mainnet.boyaa.network"], "faucets": [], "infoURL": "https://boyaa.network"}, {"name": "Ten Testnet", "chainId": 443, "shortName": "ten-testnet", "networkId": 443, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://testnet.ten.xyz"], "faucets": [], "infoURL": "https://ten.xyz"}, {"name": "Synapse Chain Testnet", "chainId": 444, "shortName": "synapse-sepolia", "networkId": 444, "nativeCurrency": {"name": "Sepolia ETH", "symbol": "ETH", "decimals": 18}, "rpc": ["https://sepolia.synapseprotocol.com"], "faucets": [], "infoURL": "https://synapseprotocol.com"}, {"name": "ARZIO Chain", "chainId": 456, "shortName": "<PERSON><PERSON><PERSON>", "networkId": 456, "nativeCurrency": {"name": "ARZIO", "symbol": "AZO", "decimals": 18}, "rpc": ["https://chain-rpc.arzio.co"], "faucets": [], "infoURL": "https://chain.arzio.co"}, {"name": "Areon Network Testnet", "chainId": 462, "shortName": "tarea", "networkId": 462, "nativeCurrency": {"name": "Areon", "symbol": "TAREA", "decimals": 18}, "rpc": ["https://testnet-rpc.areon.network", "https://testnet-rpc2.areon.network", "https://testnet-rpc3.areon.network", "https://testnet-rpc4.areon.network", "https://testnet-rpc5.areon.network"], "faucets": [], "infoURL": "https://areon.network"}, {"name": "Areon Network Mainnet", "chainId": 463, "shortName": "area", "networkId": 463, "nativeCurrency": {"name": "Areon", "symbol": "AREA", "decimals": 18}, "rpc": ["https://mainnet-rpc.areon.network", "https://mainnet-rpc2.areon.network", "https://mainnet-rpc3.areon.network", "https://mainnet-rpc4.areon.network", "https://mainnet-rpc5.areon.network"], "faucets": [], "infoURL": "https://areon.network"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 499, "shortName": "rupx", "networkId": 499, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "RUPX", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://www.rupaya.io"}, {"name": "Camino C-Chain", "chainId": 500, "shortName": "Camino", "networkId": 1000, "nativeCurrency": {"name": "Camino", "symbol": "CAM", "decimals": 18}, "rpc": ["https://api.camino.network/ext/bc/C/rpc"], "faucets": [], "infoURL": "https://camino.network/"}, {"name": "Columbus Test Network", "chainId": 501, "shortName": "Columbus", "networkId": 1001, "nativeCurrency": {"name": "Camino", "symbol": "CAM", "decimals": 18}, "rpc": ["https://columbus.camino.network/ext/bc/C/rpc"], "faucets": [], "infoURL": "https://camino.network/"}, {"name": "Syndicate Chain", "chainId": 510, "shortName": "syndicate-chain-mainnet", "networkId": 510, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-mainnet.syndicate.io"], "faucets": [], "infoURL": "https://syndicate.io"}, {"name": "Double-A Chain Mainnet", "chainId": 512, "shortName": "aac", "networkId": 512, "nativeCurrency": {"name": "Acuteangle Native Token", "symbol": "AAC", "decimals": 18}, "rpc": ["https://rpc.acuteangle.com"], "faucets": [], "infoURL": "https://www.acuteangle.com/"}, {"name": "Double-A Chain Testnet", "chainId": 513, "shortName": "aact", "networkId": 513, "nativeCurrency": {"name": "Acuteangle Native Token", "symbol": "AAC", "decimals": 18}, "rpc": ["https://rpc-testnet.acuteangle.com"], "faucets": ["https://scan-testnet.acuteangle.com/faucet"], "infoURL": "https://www.acuteangle.com/"}, {"name": "Gear Zero Network Mainnet", "chainId": 516, "shortName": "gz-mainnet", "networkId": 516, "nativeCurrency": {"name": "Gear Zero Network Native Token", "symbol": "GZN", "decimals": 18}, "rpc": ["https://gzn.linksme.info"], "faucets": [], "infoURL": "https://token.gearzero.ca/mainnet"}, {"name": "XT Smart Chain Mainnet", "chainId": 520, "shortName": "xt", "networkId": 1024, "nativeCurrency": {"name": "XT Smart Chain Native Token", "symbol": "XT", "decimals": 18}, "rpc": ["https://datarpc1.xsc.pub", "https://datarpc2.xsc.pub", "https://datarpc3.xsc.pub"], "faucets": ["https://xsc.pub/faucet"], "infoURL": "https://xsc.pub/"}, {"name": "Firechain Mainnet", "chainId": 529, "shortName": "fire", "networkId": 529, "nativeCurrency": {"name": "Firechain", "symbol": "FIRE", "decimals": 18}, "rpc": ["https://rpc-mainnet.thefirechain.com"], "faucets": [], "infoURL": "https://thefirechain.com"}, {"name": "F(x)Core Mainnet Network", "chainId": 530, "shortName": "FxCore", "networkId": 530, "nativeCurrency": {"name": "Function X", "symbol": "FX", "decimals": 18}, "rpc": ["https://fx-json-web3.functionx.io:8545"], "faucets": [], "infoURL": "https://functionx.io/"}, {"name": "Candle", "chainId": 534, "shortName": "CNDL", "networkId": 534, "nativeCurrency": {"name": "CANDLE", "symbol": "CNDL", "decimals": 18}, "rpc": ["https://candle-rpc.com/", "https://rpc.cndlchain.com"], "faucets": [], "infoURL": "https://candlelabs.org/"}, {"name": "OpTrust Mainnet", "chainId": 537, "shortName": "optrust", "networkId": 537, "nativeCurrency": {"name": "BSC", "symbol": "BNB", "decimals": 18}, "rpc": ["https://rpc.optrust.io"], "faucets": [], "infoURL": "https://optrust.io"}, {"name": "PAWCHAIN Testnet", "chainId": 542, "shortName": "PAW", "networkId": 542, "nativeCurrency": {"name": "PAW", "symbol": "PAW", "decimals": 18}, "rpc": ["https://pawchainx.com/"], "faucets": [], "infoURL": "https://pawchainx.com/"}, {"name": "Testnet", "chainId": 545, "shortName": "flow-testnet", "networkId": 545, "nativeCurrency": {"name": "FLOW", "symbol": "FLOW", "decimals": 18}, "rpc": ["https://testnet.evm.nodes.onflow.org"], "faucets": ["https://testnet-faucet.onflow.org"], "infoURL": "https://developers.flow.com/evm/about"}, {"name": "Vela1 Chain Mainnet", "chainId": 555, "shortName": "CLASS", "networkId": 555, "nativeCurrency": {"name": "CLASS COIN", "symbol": "CLASS", "decimals": 18}, "rpc": ["https://rpc.velaverse.io"], "faucets": [], "infoURL": "https://velaverse.io"}, {"name": "Tao Network", "chainId": 558, "shortName": "tao", "networkId": 558, "nativeCurrency": {"name": "Tao", "symbol": "TAO", "decimals": 18}, "rpc": ["https://rpc.testnet.tao.network", "http://rpc.testnet.tao.network:8545", "https://rpc.tao.network", "wss://rpc.tao.network"], "faucets": [], "infoURL": "https://tao.network"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 568, "shortName": "dct", "networkId": 568, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "DOGE", "decimals": 18}, "rpc": ["https://rpc-testnet.dogechain.dog"], "faucets": ["https://faucet.dogechain.dog"], "infoURL": "https://dogechain.dog"}, {"name": "Rollux Mainnet", "chainId": 570, "shortName": "sys-rollux", "networkId": 570, "nativeCurrency": {"name": "Syscoin", "symbol": "SYS", "decimals": 18}, "rpc": ["https://rpc.rollux.com", "wss://rpc.rollux.com/wss", "https://rollux.rpc.syscoin.org", "wss://rollux.rpc.syscoin.org/wss"], "faucets": ["https://rollux.id/faucetapp"], "infoURL": "https://rollux.com"}, {"name": "MetaChain <PERSON>net", "chainId": 571, "shortName": "metatime", "networkId": 571, "nativeCurrency": {"name": "Metatime Coin", "symbol": "MTC", "decimals": 18}, "rpc": ["https://rpc.metatime.com"], "faucets": [], "infoURL": "https://metatime.com/en"}, {"name": "Filenova Mainnet", "chainId": 579, "shortName": "filenova", "networkId": 579, "nativeCurrency": {"name": "Filecoin", "symbol": "FIL", "decimals": 18}, "rpc": ["https://rpc.filenova.org"], "faucets": [], "infoURL": "https://filenova.org"}, {"name": "Metis Stardust Testnet", "chainId": 588, "shortName": "metis-stardust", "networkId": 588, "nativeCurrency": {"name": "tM<PERSON>s", "symbol": "METIS", "decimals": 18}, "rpc": ["https://stardust.metis.io/?owner=588"], "faucets": [], "infoURL": "https://www.metis.io"}, {"name": "Astar", "chainId": 592, "shortName": "astr", "networkId": 592, "nativeCurrency": {"name": "Astar", "symbol": "ASTR", "decimals": 18}, "rpc": ["https://rpc.astar.network:8545"], "faucets": [], "infoURL": "https://astar.network/"}, {"name": "Acala Mandala Testnet TC9", "chainId": 595, "shortName": "maca", "networkId": 595, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "mACA", "decimals": 18}, "rpc": ["https://eth-rpc-tc9.aca-staging.network", "wss://eth-rpc-tc9.aca-staging.network"], "faucets": [], "infoURL": "https://acala.network"}, {"name": "Karura Network Testnet", "chainId": 596, "shortName": "tkar", "networkId": 596, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "KAR", "decimals": 18}, "rpc": ["https://eth-rpc-karura-testnet.aca-staging.network", "wss://eth-rpc-karura-testnet.aca-staging.network"], "faucets": [], "infoURL": "https://karura.network"}, {"name": "Acala Network Testnet", "chainId": 597, "shortName": "taca", "networkId": 597, "nativeCurrency": {"name": "<PERSON><PERSON>a <PERSON>", "symbol": "ACA", "decimals": 18}, "rpc": ["https://eth-rpc-acala-testnet.aca-staging.network", "wss://eth-rpc-acala-testnet.aca-staging.network"], "faucets": [], "infoURL": "https://acala.network"}, {"name": "<PERSON><PERSON>", "chainId": 599, "shortName": "metis-goerli", "networkId": 599, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "METIS", "decimals": 18}, "rpc": ["https://goerli.gateway.metisdevops.link"], "faucets": ["https://goerli.faucet.metisdevops.link"], "infoURL": "https://www.metis.io"}, {"name": "Meshnyan testnet", "chainId": 600, "shortName": "mesh-chain-testnet", "networkId": 600, "nativeCurrency": {"name": "Meshnyan Testnet Native Token", "symbol": "MESHT", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": ""}, {"name": "<PERSON>e Testnet", "chainId": 601, "shortName": "VINE", "networkId": 601, "nativeCurrency": {"name": "VINE", "symbol": "VNE", "decimals": 18}, "rpc": ["https://rpc-testnet.vne.network"], "faucets": ["https://vne.network/rose"], "infoURL": "https://www.peer.inc"}, {"name": "EIOB Mainnet", "chainId": 612, "shortName": "eiob", "networkId": 612, "nativeCurrency": {"name": "EIOB", "symbol": "EIOB", "decimals": 18}, "rpc": ["https://rpc.eiob.xyz"], "faucets": [], "infoURL": ""}, {"name": "Graphlinq Blockchain Mainnet", "chainId": 614, "shortName": "glq", "networkId": 614, "nativeCurrency": {"name": "GLQ", "symbol": "GLQ", "decimals": 18}, "rpc": ["https://glq-dataseed.graphlinq.io"], "faucets": [], "infoURL": "https://graphlinq.io"}, {"name": "Avocado", "chainId": 634, "shortName": "avocado", "networkId": 634, "nativeCurrency": {"name": "USDC", "symbol": "USDC", "decimals": 18}, "rpc": ["https://rpc.avocado.instadapp.io"], "faucets": [], "infoURL": "https://avocado.instadapp.io"}, {"name": "Previewnet", "chainId": 646, "shortName": "flow-previewnet", "networkId": 646, "nativeCurrency": {"name": "FLOW", "symbol": "FLOW", "decimals": 18}, "rpc": ["https://previewnet.evm.nodes.onflow.org"], "faucets": ["https://previewnet-faucet.onflow.org"], "infoURL": "https://developers.flow.com/evm/about"}, {"name": "SX Network Testnet", "chainId": 647, "shortName": "SX-Testnet", "networkId": 647, "nativeCurrency": {"name": "SX Network", "symbol": "SX", "decimals": 18}, "rpc": ["https://rpc.toronto.sx.technology"], "faucets": ["https://faucet.toronto.sx.technology"], "infoURL": "https://www.sx.technology"}, {"name": "Endurance Smart Chain Mainnet", "chainId": 648, "shortName": "ace", "networkId": 648, "nativeCurrency": {"name": "Endurance Chain Native Token", "symbol": "ACE", "decimals": 18}, "rpc": ["https://rpc-endurance.fusionist.io/"], "faucets": [], "infoURL": "https://ace.fusionist.io/"}, {"name": "Kalichain Testnet", "chainId": 653, "shortName": "ka<PERSON><PERSON>", "networkId": 653, "nativeCurrency": {"name": "kalis", "symbol": "KALIS", "decimals": 18}, "rpc": ["https://rpc.kalichain.com"], "faucets": [], "infoURL": "https://kalichain.com"}, {"name": "Kali<PERSON><PERSON>", "chainId": 654, "shortName": "<PERSON><PERSON><PERSON>", "networkId": 654, "nativeCurrency": {"name": "kalis", "symbol": "KALIS", "decimals": 18}, "rpc": ["https://mainnet.kalichain.com"], "faucets": [], "infoURL": "https://kalichain.com"}, {"name": "UltronSmartchain", "chainId": 662, "shortName": "ultronsmartchain", "networkId": 662, "nativeCurrency": {"name": "ulc", "symbol": "ULC", "decimals": 18}, "rpc": ["https://rpc.ultronsmartchain.io"], "faucets": [], "infoURL": "https://ultronsmartchain.io"}, {"name": "Pixie Chain Testnet", "chainId": 666, "shortName": "pixie-chain-testnet", "networkId": 666, "nativeCurrency": {"name": "Pixie Chain Testnet Native Token", "symbol": "PCTT", "decimals": 18}, "rpc": ["https://http-testnet.chain.pixie.xyz", "wss://ws-testnet.chain.pixie.xyz"], "faucets": ["https://chain.pixie.xyz/faucet"], "infoURL": "https://scan-testnet.chain.pixie.xyz"}, {"name": "LAOS Arrakis", "chainId": 667, "shortName": "laos", "networkId": 667, "nativeCurrency": {"name": "LAOS", "symbol": "LAOS", "decimals": 18}, "rpc": ["https://arrakis.gorengine.com/own", "wss://arrakis.gorengine.com/own"], "faucets": [], "infoURL": "https://www.laosfoundation.io/"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 668, "shortName": "junca", "networkId": 668, "nativeCurrency": {"name": "JuncaChain Native Token", "symbol": "JGC", "decimals": 18}, "rpc": ["https://rpc.juncachain.com"], "faucets": [], "infoURL": "https://junca-cash.world"}, {"name": "Junca<PERSON>hain testnet", "chainId": 669, "shortName": "juncat", "networkId": 669, "nativeCurrency": {"name": "JuncaChain Testnet Native Token", "symbol": "JGCT", "decimals": 18}, "rpc": ["https://rpc-testnet.juncachain.com", "wss://ws-testnet.juncachain.com"], "faucets": ["https://faucet-testnet.juncachain.com"], "infoURL": "https://junca-cash.world"}, {"name": "Karura Network", "chainId": 686, "shortName": "kar", "networkId": 686, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "KAR", "decimals": 18}, "rpc": ["https://eth-rpc-karura.aca-api.network", "wss://eth-rpc-karura.aca-api.network"], "faucets": [], "infoURL": "https://acala.network/karura"}, {"name": "Redstone", "chainId": 690, "shortName": "redstone", "networkId": 690, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.redstonechain.com", "wss://rpc.redstonechain.com"], "faucets": [], "infoURL": "https://redstone.xyz"}, {"name": "Star Social Testnet", "chainId": 700, "shortName": "SNS", "networkId": 700, "nativeCurrency": {"name": "Social", "symbol": "SNS", "decimals": 18}, "rpc": ["https://avastar.cc/ext/bc/C/rpc"], "faucets": [], "infoURL": "https://info.avastar.cc"}, {"name": "BlockChain Station Mainnet", "chainId": 707, "shortName": "bcs", "networkId": 707, "nativeCurrency": {"name": "BCS Token", "symbol": "BCS", "decimals": 18}, "rpc": ["https://rpc-mainnet.bcsdev.io", "wss://rpc-ws-mainnet.bcsdev.io"], "faucets": [], "infoURL": "https://blockchainstation.io"}, {"name": "BlockChain Station Testnet", "chainId": 708, "shortName": "tbcs", "networkId": 708, "nativeCurrency": {"name": "BCS Testnet Token", "symbol": "tBCS", "decimals": 18}, "rpc": ["https://rpc-testnet.bcsdev.io", "wss://rpc-ws-testnet.bcsdev.io"], "faucets": ["https://faucet.bcsdev.io"], "infoURL": "https://blockchainstation.io"}, {"name": "Highbury", "chainId": 710, "shortName": "fury", "networkId": 710, "nativeCurrency": {"name": "Fury", "symbol": "FURY", "decimals": 18}, "rpc": ["https://highbury.furya.io", "https://rest.furya.io"], "faucets": [], "infoURL": "https://www.fury.black"}, {"name": "Vrcscan Mainnet", "chainId": 713, "shortName": "vrc", "networkId": 713, "nativeCurrency": {"name": "VRC Chain", "symbol": "VRC", "decimals": 18}, "rpc": ["https://rpc-mainnet-5.vrcscan.com", "https://rpc-mainnet-6.vrcscan.com", "https://rpc-mainnet-7.vrcscan.com", "https://rpc-mainnet-8.vrcscan.com"], "faucets": [], "infoURL": "https://vrccoin.com"}, {"name": "Shibarium Beta", "chainId": 719, "shortName": "shibarium", "networkId": 719, "nativeCurrency": {"name": "BONE", "symbol": "BONE", "decimals": 18}, "rpc": ["https://puppynet.shibrpc.com"], "faucets": [], "infoURL": "https://beta.shibariumtech.com"}, {"name": "Lycan Chain", "chainId": 721, "shortName": "LYC", "networkId": 721, "nativeCurrency": {"name": "Lycan", "symbol": "LYC", "decimals": 18}, "rpc": ["https://rpc.lycanchain.com/"], "faucets": [], "infoURL": "https://lycanchain.com"}, {"name": "<PERSON><PERSON>", "chainId": 727, "shortName": "blu", "networkId": 727, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "BLU", "decimals": 18}, "rpc": ["https://data.bluchain.pro"], "faucets": [], "infoURL": "https://www.blucrates.com"}, {"name": "Lovely Network Mainnet", "chainId": 730, "shortName": "LOVELY", "networkId": 730, "nativeCurrency": {"name": "Lovely", "symbol": "LOVELY", "decimals": 18}, "rpc": ["https://rpc.lovely.network"], "faucets": [], "infoURL": "https://lovely.network"}, {"name": "Canto Testnet", "chainId": 740, "shortName": "tcanto", "networkId": 740, "nativeCurrency": {"name": "Canto", "symbol": "CANTO", "decimals": 18}, "rpc": ["https://eth.plexnode.wtf/"], "faucets": [], "infoURL": "https://canto.io"}, {"name": "Vention Smart Chain Testnet", "chainId": 741, "shortName": "vsct", "networkId": 741, "nativeCurrency": {"name": "VNT", "symbol": "VNT", "decimals": 18}, "rpc": ["https://node-testnet.vention.network"], "faucets": ["https://faucet.vention.network"], "infoURL": "https://testnet.ventionscan.io"}, {"name": "Script Testnet", "chainId": 742, "shortName": "SPAY", "networkId": 742, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "SPAY", "decimals": 18}, "rpc": ["https://testeth-rpc-api.script.tv/rpc"], "faucets": [], "infoURL": "https://token.script.tv"}, {"name": "Mainnet", "chainId": 747, "shortName": "flow-mainnet", "networkId": 747, "nativeCurrency": {"name": "FLOW", "symbol": "FLOW", "decimals": 18}, "rpc": ["https://mainnet.evm.nodes.onflow.org"], "faucets": [], "infoURL": "https://developers.flow.com/evm/about"}, {"name": "QL1", "chainId": 766, "shortName": "qom", "networkId": 766, "nativeCurrency": {"name": "Shiba Predator", "symbol": "QOM", "decimals": 18}, "rpc": ["https://rpc.qom.one"], "faucets": [], "infoURL": "https://qom.one"}, {"name": "OpenChain <PERSON>net", "chainId": 776, "shortName": "opc", "networkId": 776, "nativeCurrency": {"name": "Openchain Testnet", "symbol": "TOPC", "decimals": 18}, "rpc": [], "faucets": ["https://faucet.openchain.info/"], "infoURL": "https://testnet.openchain.info/"}, {"name": "cheapETH", "chainId": 777, "shortName": "cth", "networkId": 777, "nativeCurrency": {"name": "cTH", "symbol": "cTH", "decimals": 18}, "rpc": ["https://node.cheapeth.org/rpc"], "faucets": [], "infoURL": "https://cheapeth.org/"}, {"name": "MAAL Chain", "chainId": 786, "shortName": "maal", "networkId": 786, "nativeCurrency": {"name": "MAAL", "symbol": "MAAL", "decimals": 18}, "rpc": ["https://node1-mainnet.maalscan.io/", "https://node2-mainnet.maalscan.io/", "https://node3-mainnet.maalscan.io/"], "faucets": [], "infoURL": "https://www.maalchain.com/"}, {"name": "Acala Network", "chainId": 787, "shortName": "aca", "networkId": 787, "nativeCurrency": {"name": "<PERSON><PERSON>a <PERSON>", "symbol": "ACA", "decimals": 18}, "rpc": ["https://eth-rpc-acala.aca-api.network", "wss://eth-rpc-acala.aca-api.network"], "faucets": [], "infoURL": "https://acala.network"}, {"name": "Aerochain Testnet", "chainId": 788, "shortName": "taero", "networkId": 788, "nativeCurrency": {"name": "Aerochain Testnet", "symbol": "TAero", "decimals": 18}, "rpc": ["https://testnet-rpc.aerochain.id/"], "faucets": ["https://faucet.aerochain.id/"], "infoURL": "https://aerochaincoin.org/"}, {"name": "Patex", "chainId": 789, "shortName": "peth", "networkId": 789, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.patex.io/"], "faucets": [], "infoURL": "https://patex.io/"}, {"name": "Rupaya Testnet", "chainId": 799, "shortName": "RupayaTestnet", "networkId": 799, "nativeCurrency": {"name": "Test Rupaya", "symbol": "TRUPX", "decimals": 18}, "rpc": ["https://rpc.testnet.rupaya.io"], "faucets": ["https://faucet.testnet.rupaya.io"], "infoURL": "https://www.rupaya.io"}, {"name": "Lucid Block<PERSON>in", "chainId": 800, "shortName": "LUCID", "networkId": 800, "nativeCurrency": {"name": "LUCID", "symbol": "LUCID", "decimals": 18}, "rpc": ["https://rpc.lucidcoin.io"], "faucets": ["https://faucet.lucidcoin.io"], "infoURL": "https://lucidcoin.io"}, {"name": "<PERSON><PERSON>", "chainId": 803, "shortName": "haic", "networkId": 803, "nativeCurrency": {"name": "Haicoin", "symbol": "HAIC", "decimals": 18}, "rpc": ["https://orig.haichain.io/"], "faucets": [], "infoURL": "https://www.haichain.io/"}, {"name": "Portal Fantasy Chain Test", "chainId": 808, "shortName": "PFTEST", "networkId": 808, "nativeCurrency": {"name": "Portal Fantasy Token", "symbol": "PFT", "decimals": 18}, "rpc": ["https://subnets.avax.network/portal-fantasy/testnet/rpc"], "faucets": [], "infoURL": "https://portalfantasy.io"}, {"name": "Haven1 Testnet", "chainId": 810, "shortName": "h1", "networkId": 810, "nativeCurrency": {"name": "Haven1", "symbol": "H1", "decimals": 18}, "rpc": ["https://testnet-rpc.haven1.org"], "faucets": ["https://www.haven1.org/faucet"], "infoURL": "https://www.haven1.org"}, {"name": "Qitmeer Network Mainnet", "chainId": 813, "shortName": "meer", "networkId": 813, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "MEER", "decimals": 18}, "rpc": ["https://evm-dataseed1.meerscan.io", "https://evm-dataseed2.meerscan.io", "https://evm-dataseed3.meerscan.io", "https://evm-dataseed.meerscan.com", "https://qng.rpc.qitmeer.io", "https://mainnet.meerlabs.com", "https://rpc.dimai.ai", "https://rpc.woowow.io"], "faucets": [], "infoURL": "https://github.com/Qitmeer"}, {"name": "Firechain zkEVM", "chainId": 814, "shortName": "firechan-zkEVM", "networkId": 814, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-zkevm.thefirechain.com"], "faucets": [], "infoURL": "https://docs.thefirechain.com/"}, {"name": "BeOne Chain Mainnet", "chainId": 818, "shortName": "BOC", "networkId": 818, "nativeCurrency": {"name": "BeOne Chain Mainnet", "symbol": "BOC", "decimals": 18}, "rpc": ["https://dataseed1.beonechain.com", "https://dataseed2.beonechain.com", "https://dataseed-us1.beonechain.com", "https://dataseed-us2.beonechain.com", "https://dataseed-uk1.beonechain.com", "https://dataseed-uk2.beonechain.com"], "faucets": [], "infoURL": "https://beonechain.com"}, {"name": "Callisto Mainnet", "chainId": 820, "shortName": "clo", "networkId": 1, "nativeCurrency": {"name": "Callisto", "symbol": "CLO", "decimals": 18}, "rpc": ["https://rpc.callisto.network/"], "faucets": [], "infoURL": "https://callisto.network"}, {"name": "Callisto Testnet Deprecated", "chainId": 821, "shortName": "tclo", "networkId": 2, "nativeCurrency": {"name": "Callisto Testnet Ether", "symbol": "TCLO", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://callisto.network"}, {"name": "Runic Chain Testnet", "chainId": 822, "shortName": "runic-testnet", "networkId": 822, "nativeCurrency": {"name": "Bitcoin", "symbol": "rBTC", "decimals": 18}, "rpc": ["https://rpc-testnet.runic.build"], "faucets": ["https://faucet.runic.build"], "infoURL": "https://runic.build"}, {"name": "CheckDot Blockchain Devnet", "chainId": 831, "shortName": "cdt", "networkId": 831, "nativeCurrency": {"name": "CDT", "symbol": "CDT", "decimals": 18}, "rpc": ["https://devnet.checkdot.io"], "faucets": [], "infoURL": "https://checkdot.io"}, {"name": "Taraxa Mainnet", "chainId": 841, "shortName": "tara", "networkId": 841, "nativeCurrency": {"name": "Tara", "symbol": "TARA", "decimals": 18}, "rpc": ["https://rpc.mainnet.taraxa.io/"], "faucets": [], "infoURL": "https://taraxa.io"}, {"name": "Taraxa <PERSON>net", "chainId": 842, "shortName": "taratest", "networkId": 842, "nativeCurrency": {"name": "Tara", "symbol": "TARA", "decimals": 18}, "rpc": ["https://rpc.testnet.taraxa.io/"], "faucets": [], "infoURL": "https://taraxa.io"}, {"name": "Zeeth Chain Dev", "chainId": 859, "shortName": "zeethdev", "networkId": 859, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "ZTH", "decimals": 18}, "rpc": ["https://rpc.dev.zeeth.io"], "faucets": [], "infoURL": ""}, {"name": "Fantasia Chain Mainnet", "chainId": 868, "shortName": "FSCMainnet", "networkId": 868, "nativeCurrency": {"name": "FST", "symbol": "FST", "decimals": 18}, "rpc": ["https://mainnet-data1.fantasiachain.com/", "https://mainnet-data2.fantasiachain.com/", "https://mainnet-data3.fantasiachain.com/"], "faucets": [], "infoURL": "https://fantasiachain.com/"}, {"name": "Bandai Namco Research Verse Mainnet", "chainId": 876, "shortName": "BNKEN", "networkId": 876, "nativeCurrency": {"name": "OAS", "symbol": "OAS", "decimals": 18}, "rpc": ["https://rpc.main.oasvrs.bnken.net"], "faucets": [], "infoURL": "https://www.bandainamco-mirai.com/en/"}, {"name": "Dexit Network", "chainId": 877, "shortName": "DXT", "networkId": 877, "nativeCurrency": {"name": "Dexit network", "symbol": "DXT", "decimals": 18}, "rpc": ["https://dxt.dexit.network"], "faucets": ["https://faucet.dexit.network"], "infoURL": "https://dexit.network"}, {"name": "Ambros Chain Mainnet", "chainId": 880, "shortName": "ambros", "networkId": 880, "nativeCurrency": {"name": "AMBROS", "symbol": "AMBROS", "decimals": 18}, "rpc": ["https://api.ambros.network"], "faucets": [], "infoURL": "https://ambros.network"}, {"name": "Wanchain", "chainId": 888, "shortName": "wan", "networkId": 888, "nativeCurrency": {"name": "Wancoin", "symbol": "WAN", "decimals": 18}, "rpc": ["https://gwan-ssl.wandevs.org:56891/"], "faucets": [], "infoURL": "https://www.wanscan.org"}, {"name": "MAXI Chain Testnet", "chainId": 898, "shortName": "maxi-testnet", "networkId": 898, "nativeCurrency": {"name": "MAXICOIN", "symbol": "MAXI", "decimals": 18}, "rpc": ["https://rpc-testnet.maxi.network"], "faucets": ["https://faucet.maxi.network"], "infoURL": "https://maxi.network"}, {"name": "Garizon Testnet Stage0", "chainId": 900, "shortName": "gar-test-s0", "networkId": 900, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "GAR", "decimals": 18}, "rpc": ["https://s0-testnet.garizon.net/rpc"], "faucets": ["https://faucet-testnet.garizon.com"], "infoURL": "https://garizon.com"}, {"name": "Garizon Testnet Stage1", "chainId": 901, "shortName": "gar-test-s1", "networkId": 901, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "GAR", "decimals": 18}, "rpc": ["https://s1-testnet.garizon.net/rpc"], "faucets": ["https://faucet-testnet.garizon.com"], "infoURL": "https://garizon.com"}, {"name": "Garizon Testnet Stage2", "chainId": 902, "shortName": "gar-test-s2", "networkId": 902, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "GAR", "decimals": 18}, "rpc": ["https://s2-testnet.garizon.net/rpc"], "faucets": ["https://faucet-testnet.garizon.com"], "infoURL": "https://garizon.com"}, {"name": "Garizon Testnet Stage3", "chainId": 903, "shortName": "gar-test-s3", "networkId": 903, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "GAR", "decimals": 18}, "rpc": ["https://s3-testnet.garizon.net/rpc"], "faucets": ["https://faucet-testnet.garizon.com"], "infoURL": "https://garizon.com"}, {"name": "Portal Fantasy Chain", "chainId": 909, "shortName": "PF", "networkId": 909, "nativeCurrency": {"name": "Portal Fantasy Token", "symbol": "PFT", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://portalfantasy.io"}, {"name": "DecentraBone Layer1 Testnet", "chainId": 910, "shortName": "DBONE", "networkId": 910, "nativeCurrency": {"name": "DecentraBone", "symbol": "DBONE", "decimals": 18}, "rpc": ["https://layer1test.decentrabone.com"], "faucets": [], "infoURL": "https://decentrabone.com"}, {"name": "TAPROOT Mainnet", "chainId": 911, "shortName": "TAPROOT-Mainnet", "networkId": 911, "nativeCurrency": {"name": "TBTC", "symbol": "TBTC", "decimals": 18}, "rpc": ["https://rpc.taprootchain.io"], "faucets": [], "infoURL": "https://taprootchain.io"}, {"name": "R<PERSON>net", "chainId": 917, "shortName": "tfire", "networkId": 917, "nativeCurrency": {"name": "Firechain", "symbol": "FIRE", "decimals": 18}, "rpc": ["https://rinia-rpc1.thefirechain.com"], "faucets": ["https://faucet.thefirechain.com"], "infoURL": "https://thefirechain.com"}, {"name": "Mode Testnet", "chainId": 919, "shortName": "modesep", "networkId": 919, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://sepolia.mode.network"], "faucets": ["https://sepoliafaucet.com/"], "infoURL": "https://docs.mode.network/"}, {"name": "Yidark Chain Mainnet", "chainId": 927, "shortName": "ydk", "networkId": 927, "nativeCurrency": {"name": "Yidark", "symbol": "YDK", "decimals": 18}, "rpc": ["https://rpc.yidark.io"], "faucets": [], "infoURL": "https://yidarkscan.com"}, {"name": "Pulse<PERSON>hain <PERSON>net", "chainId": 940, "shortName": "tpls", "networkId": 940, "nativeCurrency": {"name": "Test Pulse", "symbol": "tPLS", "decimals": 18}, "rpc": ["https://rpc.v2.testnet.pulsechain.com/", "wss://rpc.v2.testnet.pulsechain.com/"], "faucets": ["https://faucet.v2.testnet.pulsechain.com/"], "infoURL": "https://pulsechain.com/"}, {"name": "PulseChain Testnet v2b", "chainId": 941, "shortName": "t2bpls", "networkId": 941, "nativeCurrency": {"name": "Test Pulse", "symbol": "tPLS", "decimals": 18}, "rpc": ["https://rpc.v2b.testnet.pulsechain.com/", "wss://rpc.v2b.testnet.pulsechain.com/"], "faucets": ["https://faucet.v2b.testnet.pulsechain.com/"], "infoURL": "https://pulsechain.com/"}, {"name": "PulseChain Testnet v3", "chainId": 942, "shortName": "t3pls", "networkId": 942, "nativeCurrency": {"name": "Test Pulse", "symbol": "tPLS", "decimals": 18}, "rpc": ["https://rpc.v3.testnet.pulsechain.com/", "wss://rpc.v3.testnet.pulsechain.com/"], "faucets": ["https://faucet.v3.testnet.pulsechain.com/"], "infoURL": "https://pulsechain.com/"}, {"name": "PulseChain Testnet v4", "chainId": 943, "shortName": "t4pls", "networkId": 943, "nativeCurrency": {"name": "Test Pulse", "symbol": "tPLS", "decimals": 18}, "rpc": ["https://rpc.v4.testnet.pulsechain.com", "wss://rpc.v4.testnet.pulsechain.com", "https://pulsechain-testnet-rpc.publicnode.com", "wss://pulsechain-testnet-rpc.publicnode.com", "https://rpc-testnet-pulsechain.g4mm4.io", "wss://rpc-testnet-pulsechain.g4mm4.io"], "faucets": ["https://faucet.v4.testnet.pulsechain.com/"], "infoURL": "https://pulsechain.com"}, {"name": "muNode Testnet", "chainId": 956, "shortName": "munode", "networkId": 956, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://munode.dev/"}, {"name": "Lyra Chain", "chainId": 957, "shortName": "lyra", "networkId": 957, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.lyra.finance"], "faucets": [], "infoURL": "https://lyra.finance"}, {"name": "BTC20 Smart Chain", "chainId": 963, "shortName": "btc20", "networkId": 963, "nativeCurrency": {"name": "BTCC", "symbol": "BTCC", "decimals": 18}, "rpc": ["https://rpc.bitcoincode.technology/"], "faucets": [], "infoURL": "https://bitcoincode.technology"}, {"name": "Oort Mainnet", "chainId": 970, "shortName": "ccn", "networkId": 970, "nativeCurrency": {"name": "Oort", "symbol": "OORT", "decimals": 18}, "rpc": ["https://mainnet-rpc.oortech.com"], "faucets": [], "infoURL": "https://oortech.com"}, {"name": "Oort <PERSON>", "chainId": 971, "shortName": "<PERSON><PERSON><PERSON>", "networkId": 971, "nativeCurrency": {"name": "Oort", "symbol": "CCN", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://oortech.com"}, {"name": "<PERSON><PERSON>", "chainId": 972, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "networkId": 972, "nativeCurrency": {"name": "Oort", "symbol": "CCNA", "decimals": 18}, "rpc": ["https://ascraeus-rpc.oortech.com"], "faucets": [], "infoURL": "https://oortech.com"}, {"name": "Nepal Blockchain Network", "chainId": 977, "shortName": "yeti", "networkId": 977, "nativeCurrency": {"name": "Nepal Blockchain Network Ether", "symbol": "YETI", "decimals": 18}, "rpc": ["https://api.nepalblockchain.dev", "https://api.nepalblockchain.network"], "faucets": ["https://faucet.nepalblockchain.network"], "infoURL": "https://nepalblockchain.network"}, {"name": "EthXY Testnet", "chainId": 979, "shortName": "sexyTestnet", "networkId": 979, "nativeCurrency": {"name": "Settled EthXY Token", "symbol": "SEXY", "decimals": 18}, "rpc": ["https://rpc.testnet.ethxy.com"], "faucets": [], "infoURL": "https://ethxy.com"}, {"name": "TOP Mainnet EVM", "chainId": 980, "shortName": "top_evm", "networkId": 0, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://ethapi.topnetwork.org"], "faucets": [], "infoURL": "https://www.topnetwork.org/"}, {"name": "Memo Smart Chain Mainnet", "chainId": 985, "shortName": "memochain", "networkId": 985, "nativeCurrency": {"name": "Memo", "symbol": "CMEMO", "decimals": 18}, "rpc": ["https://chain.metamemo.one:8501", "wss://chain.metamemo.one:16801"], "faucets": ["https://faucet.metamemo.one/"], "infoURL": "www.memolabs.org"}, {"name": "TOP Mainnet", "chainId": 989, "shortName": "top", "networkId": 0, "nativeCurrency": {"name": "TOP", "symbol": "TOP", "decimals": 6}, "rpc": [], "faucets": [], "infoURL": "https://www.topnetwork.org/"}, {"name": "eLiberty Mainnet", "chainId": 990, "shortName": "ELm", "networkId": 990, "nativeCurrency": {"name": "eLiberty", "symbol": "$EL", "decimals": 18}, "rpc": ["https://rpc.eliberty.ngo"], "faucets": ["https://faucet.eliberty.ngo"], "infoURL": "https://eliberty.ngo"}, {"name": "5ireChain Thunder", "chainId": 997, "shortName": "5ire", "networkId": 997, "nativeCurrency": {"name": "5ire <PERSON>", "symbol": "5ire", "decimals": 18}, "rpc": ["https://rpc-testnet.5ire.network"], "faucets": ["https://explorer.5ire.network/faucet"], "infoURL": "https://5ire.org"}, {"name": "Lucky Network", "chainId": 998, "shortName": "ln", "networkId": 998, "nativeCurrency": {"name": "Lucky", "symbol": "L99", "decimals": 18}, "rpc": ["https://rpc.luckynetwork.org", "wss://ws.lnscan.org", "https://rpc.lnscan.org"], "faucets": [], "infoURL": "https://luckynetwork.org"}, {"name": "Wanchain Testnet", "chainId": 999, "shortName": "twan", "networkId": 999, "nativeCurrency": {"name": "Wancoin", "symbol": "WAN", "decimals": 18}, "rpc": ["https://gwan-ssl.wandevs.org:46891/"], "faucets": [], "infoURL": "https://testnet.wanscan.org"}, {"name": "GTON Mainnet", "chainId": 1000, "shortName": "gton", "networkId": 1000, "nativeCurrency": {"name": "GCD", "symbol": "GCD", "decimals": 18}, "rpc": ["https://rpc.gton.network/"], "faucets": [], "infoURL": "https://gton.capital"}, {"name": "Klaytn Testnet Baobab", "chainId": 1001, "shortName": "Baobab", "networkId": 1001, "nativeCurrency": {"name": "KLAY", "symbol": "KLAY", "decimals": 18}, "rpc": ["https://public-en-baobab.klaytn.net", "https://api.baobab.klaytn.net:8651"], "faucets": ["https://baobab.wallet.klaytn.com/access?next=faucet"], "infoURL": "https://klaytn.foundation"}, {"name": "Tectum Emission Token", "chainId": 1003, "shortName": "tet", "networkId": 1003, "nativeCurrency": {"name": "Tectum", "symbol": "TET", "decimals": 8}, "rpc": ["https://rpc.softnote.com/"], "faucets": [], "infoURL": "https://softnote.com"}, {"name": "T-EKTA", "chainId": 1004, "shortName": "t-ekta", "networkId": 1004, "nativeCurrency": {"name": "T-EKTA", "symbol": "T-EKTA", "decimals": 18}, "rpc": ["https://test.ekta.io:8545"], "faucets": [], "infoURL": "https://www.ekta.io"}, {"name": "<PERSON>", "chainId": 1007, "shortName": "tnew", "networkId": 1007, "nativeCurrency": {"name": "<PERSON>", "symbol": "NEW", "decimals": 18}, "rpc": ["https://rpc1.newchain.newtonproject.org"], "faucets": [], "infoURL": "https://www.newtonproject.org/"}, {"name": "<PERSON><PERSON>", "chainId": 1008, "shortName": "eun", "networkId": 1008, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "EUN", "decimals": 18}, "rpc": ["https://mainnet.eurus.network/"], "faucets": [], "infoURL": "https://eurus.network"}, {"name": "Jumbochain Mainnet", "chainId": 1009, "shortName": "Jumboscan", "networkId": 1009, "nativeCurrency": {"name": "JNFTC", "symbol": "JNFTC", "decimals": 18}, "rpc": ["https://rpcpriv.jumbochain.org"], "faucets": [], "infoURL": "https://jumbochain.org"}, {"name": "Evrice Network", "chainId": 1010, "shortName": "EVC", "networkId": 1010, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "EVC", "decimals": 18}, "rpc": ["https://meta.evrice.com"], "faucets": [], "infoURL": "https://evrice.com"}, {"name": "Rebus Mainnet", "chainId": 1011, "shortName": "rebus", "networkId": 1011, "nativeCurrency": {"name": "Rebus", "symbol": "REBUS", "decimals": 18}, "rpc": ["https://apievm.rebuschain.com/rpc"], "faucets": [], "infoURL": "https://www.rebuschain.com"}, {"name": "<PERSON>", "chainId": 1012, "shortName": "new", "networkId": 1012, "nativeCurrency": {"name": "<PERSON>", "symbol": "NEW", "decimals": 18}, "rpc": ["https://global.rpc.mainnet.newtonproject.org"], "faucets": [], "infoURL": "https://www.newtonproject.org/"}, {"name": "Sakura", "chainId": 1022, "shortName": "sku", "networkId": 1022, "nativeCurrency": {"name": "Sakura", "symbol": "SKU", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://clover.finance/sakura"}, {"name": "Clover Testnet", "chainId": 1023, "shortName": "tclv", "networkId": 1023, "nativeCurrency": {"name": "Clover", "symbol": "CLV", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://clover.finance"}, {"name": "CLV Parachain", "chainId": 1024, "shortName": "clv", "networkId": 1024, "nativeCurrency": {"name": "CLV", "symbol": "CLV", "decimals": 18}, "rpc": ["https://api-para.clover.finance"], "faucets": [], "infoURL": "https://clv.org"}, {"name": "BitTorrent Chain Testnet", "chainId": 1028, "shortName": "tbtt", "networkId": 1028, "nativeCurrency": {"name": "BitTorrent", "symbol": "BTT", "decimals": 18}, "rpc": ["https://testrpc.bittorrentchain.io/"], "faucets": [], "infoURL": "https://bittorrentchain.io/"}, {"name": "Conflux eSpace", "chainId": 1030, "shortName": "cfx", "networkId": 1030, "nativeCurrency": {"name": "CFX", "symbol": "CFX", "decimals": 18}, "rpc": ["https://evm.confluxrpc.com"], "faucets": [], "infoURL": "https://confluxnetwork.org"}, {"name": "Proxy Network Testnet", "chainId": 1031, "shortName": "prx", "networkId": 1031, "nativeCurrency": {"name": "PRX", "symbol": "PRX", "decimals": 18}, "rpc": ["http://128.199.94.183:8041"], "faucets": [], "infoURL": "https://theproxy.network"}, {"name": "Bronos Testnet", "chainId": 1038, "shortName": "bronos-testnet", "networkId": 1038, "nativeCurrency": {"name": "tBRO", "symbol": "tBRO", "decimals": 18}, "rpc": ["https://evm-testnet.bronos.org"], "faucets": ["https://faucet.bronos.org"], "infoURL": "https://bronos.org"}, {"name": "Bronos Mainnet", "chainId": 1039, "shortName": "bronos-mainnet", "networkId": 1039, "nativeCurrency": {"name": "BRO", "symbol": "BRO", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://bronos.org"}, {"name": "ShimmerEVM Testnet Deprecated", "chainId": 1071, "shortName": "shimmerevm-testnet-deprecated", "networkId": 1071, "nativeCurrency": {"name": "SMR", "symbol": "SMR", "decimals": 18}, "rpc": [], "faucets": ["https://evm-toolkit.evm.testnet.shimmer.network", "https://evm-faucet.testnet.shimmer.network"], "infoURL": "https://shimmer.network"}, {"name": "ShimmerEVM Testnet Deprecated 1072", "chainId": 1072, "shortName": "shimmerevm-testnet-deprecated-1072", "networkId": 1072, "nativeCurrency": {"name": "SMR", "symbol": "SMR", "decimals": 6}, "rpc": [], "faucets": ["https://evm-toolkit.evm.testnet.shimmer.network", "https://evm-faucet.testnet.shimmer.network"], "infoURL": "https://shimmer.network"}, {"name": "ShimmerEVM Testnet", "chainId": 1073, "shortName": "shimmerevm-testnet", "networkId": 1073, "nativeCurrency": {"name": "SMR", "symbol": "SMR", "decimals": 18}, "rpc": ["https://json-rpc.evm.testnet.shimmer.network"], "faucets": ["https://evm-toolkit.evm.testnet.shimmer.network", "https://evm-faucet.testnet.shimmer.network"], "infoURL": "https://shimmer.network"}, {"name": "IOTA EVM Testnet", "chainId": 1075, "shortName": "iotaevm-testnet", "networkId": 1075, "nativeCurrency": {"name": "IOTA", "symbol": "IOTA", "decimals": 18}, "rpc": ["https://json-rpc.evm.testnet.iotaledger.net"], "faucets": ["https://evm-toolkit.evm.testnet.iotaledger.net"], "infoURL": "https://www.iota.org"}, {"name": "Mintara Testnet", "chainId": 1079, "shortName": "mintara-testnet", "networkId": 1079, "nativeCurrency": {"name": "MINTARA", "symbol": "MNTR", "decimals": 18}, "rpc": ["https://subnets.avax.network/mintara/testnet/rpc"], "faucets": [], "infoURL": "https://playthink.co.jp"}, {"name": "Mintara Mainnet", "chainId": 1080, "shortName": "mintara", "networkId": 1080, "nativeCurrency": {"name": "MINTARA", "symbol": "MNTR", "decimals": 18}, "rpc": ["https://subnets.avax.network/mintara/mainnet/rpc"], "faucets": [], "infoURL": "https://playthink.co.jp"}, {"name": "Metis Andromeda Mainnet", "chainId": 1088, "shortName": "metis-andromeda", "networkId": 1088, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "METIS", "decimals": 18}, "rpc": ["https://andromeda.metis.io/?owner=1088", "https://metis.drpc.org", "wss://metis.drpc.org"], "faucets": [], "infoURL": "https://www.metis.io"}, {"name": "Humans.ai Mainnet", "chainId": 1089, "shortName": "humans", "networkId": 1089, "nativeCurrency": {"name": "HEART", "symbol": "HEART", "decimals": 18}, "rpc": ["https://jsonrpc.humans.nodestake.top", "https://humans-mainnet-evm.itrocket.net", "https://humans-evm-rpc.staketab.org:443", "https://evm.humans.stakepool.dev.br", "https://mainnet-humans-evm.konsortech.xyz", "https://evm-rpc.mainnet.humans.zone", "https://json-rpc.humans.bh.rocks", "https://evm-rpc.humans.huginn.tech"], "faucets": [], "infoURL": "https://humans.ai"}, {"name": "MOAC mainnet", "chainId": 1099, "shortName": "moac", "networkId": 1099, "nativeCurrency": {"name": "MOAC", "symbol": "mc", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://moac.io"}, {"name": "Dymension", "chainId": 1100, "shortName": "dymension", "networkId": 1100, "nativeCurrency": {"name": "DYM", "symbol": "DYM", "decimals": 18}, "rpc": ["https://dymension-evm.blockpi.network/v1/rpc/public", "https://dymension-evm-rpc.publicnode.com", "wss://dymension-evm-rpc.publicnode.com"], "faucets": [], "infoURL": "https://dymension.xyz"}, {"name": "Polygon zkEVM", "chainId": 1101, "shortName": "zkevm", "networkId": 1101, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://zkevm-rpc.com", "https://polygon-zkevm.drpc.org", "wss://polygon-zkevm.drpc.org"], "faucets": [], "infoURL": "https://polygon.technology/polygon-zkevm"}, {"name": "BLXq Testnet", "chainId": 1107, "shortName": "tblxq", "networkId": 1107, "nativeCurrency": {"name": "BLXQ", "symbol": "BLXQ", "decimals": 18}, "rpc": ["https://testnetq1.blx.org"], "faucets": [], "infoURL": "https://blx.org"}, {"name": "BLXq Mainnet", "chainId": 1108, "shortName": "blxq", "networkId": 1108, "nativeCurrency": {"name": "BLXQ", "symbol": "BLXQ", "decimals": 18}, "rpc": ["https://mainnet.blxq.org"], "faucets": [], "infoURL": "https://blx.org"}, {"name": "WEMIX3.0 Mainnet", "chainId": 1111, "shortName": "wemix", "networkId": 1111, "nativeCurrency": {"name": "WEMIX", "symbol": "WEMIX", "decimals": 18}, "rpc": ["https://api.wemix.com", "wss://ws.wemix.com"], "faucets": [], "infoURL": "https://wemix.com"}, {"name": "WEMIX3.0 Testnet", "chainId": 1112, "shortName": "twemix", "networkId": 1112, "nativeCurrency": {"name": "TestnetWEMIX", "symbol": "tWEMIX", "decimals": 18}, "rpc": ["https://api.test.wemix.com", "wss://ws.test.wemix.com"], "faucets": ["https://wallet.test.wemix.com/faucet"], "infoURL": "https://wemix.com"}, {"name": "B2 Hub Testnet", "chainId": 1113, "shortName": "B2Hub-testnet", "networkId": 1113, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "B2", "decimals": 18}, "rpc": ["https://testnet-hub-rpc.bsquared.network"], "faucets": [], "infoURL": "https://www.bsquared.network"}, {"name": "Core Blockchain Testnet", "chainId": 1115, "shortName": "tcore", "networkId": 1115, "nativeCurrency": {"name": "Core Blockchain Testnet Native Token", "symbol": "tCORE", "decimals": 18}, "rpc": ["https://rpc.test.btcs.network/"], "faucets": ["https://scan.test.btcs.network/faucet"], "infoURL": "https://www.coredao.org"}, {"name": "Core Blockchain Mainnet", "chainId": 1116, "shortName": "core", "networkId": 1116, "nativeCurrency": {"name": "Core Blockchain Native Token", "symbol": "CORE", "decimals": 18}, "rpc": ["https://rpc.coredao.org/", "https://rpc-core.icecreamswap.com", "https://core.drpc.org", "wss://core.drpc.org"], "faucets": [], "infoURL": "https://www.coredao.org"}, {"name": "Dogcoin Mainnet", "chainId": 1117, "shortName": "DOGSm", "networkId": 1117, "nativeCurrency": {"name": "Dogcoin", "symbol": "DOGS", "decimals": 18}, "rpc": ["https://mainnet-rpc.dogcoin.me"], "faucets": ["https://faucet.dogcoin.network"], "infoURL": "https://dogcoin.network"}, {"name": "B2 Testnet", "chainId": 1123, "shortName": "B2-testnet", "networkId": 1123, "nativeCurrency": {"name": "Bitcoin", "symbol": "BTC", "decimals": 18}, "rpc": ["https://b2-testnet.alt.technology"], "faucets": [], "infoURL": "https://www.bsquared.network"}, {"name": "DeFiChain EVM Network Mainnet", "chainId": 1130, "shortName": "DFI", "networkId": 1130, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "DFI", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://meta.defichain.com/"}, {"name": "DeFiChain EVM Network Testnet", "chainId": 1131, "shortName": "DFI-T", "networkId": 1131, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "DFI", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://meta.defichain.com/"}, {"name": "DeFiMeta<PERSON><PERSON><PERSON>", "chainId": 1133, "shortName": "changi", "networkId": 1133, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "DFI", "decimals": 18}, "rpc": ["https://dmc.mydefichain.com/changi", "https://testnet-dmc.mydefichain.com:20551"], "faucets": ["http://tc04.mydefichain.com/faucet"], "infoURL": "https://meta.defichain.com"}, {"name": "Lisk", "chainId": 1135, "shortName": "lisk", "networkId": 1135, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.api.lisk.com"], "faucets": [], "infoURL": "https://lisk.com"}, {"name": "AmStar Testnet", "chainId": 1138, "shortName": "ASARt", "networkId": 1138, "nativeCurrency": {"name": "SINSO", "symbol": "SINSO", "decimals": 18}, "rpc": ["https://testnet-rpc.amstarscan.com"], "faucets": [], "infoURL": "https://sinso.io"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 1139, "shortName": "MATH", "networkId": 1139, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "MATH", "decimals": 18}, "rpc": ["https://mathchain-asia.maiziqianbao.net/rpc", "https://mathchain-us.maiziqianbao.net/rpc"], "faucets": [], "infoURL": "https://mathchain.org"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 1140, "shortName": "tMATH", "networkId": 1140, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "MATH", "decimals": 18}, "rpc": ["https://galois-hk.maiziqianbao.net/rpc"], "faucets": ["https://scan.boka.network/#/Galois/faucet"], "infoURL": "https://mathchain.org"}, {"name": "Symplexia Smart Chain", "chainId": 1149, "shortName": "Plexchain", "networkId": 1149, "nativeCurrency": {"name": "Plex Native Token", "symbol": "PLEX", "decimals": 18}, "rpc": ["https://plex-rpc.plexfinance.us"], "faucets": [], "infoURL": "https://plexfinance.us/"}, {"name": "Origin Testnet", "chainId": 1170, "shortName": "auoc", "networkId": 1170, "nativeCurrency": {"name": "Origin", "symbol": "UOC", "decimals": 18}, "rpc": ["https://json-rpc.origin.uptick.network"], "faucets": [], "infoURL": "https://www.uptick.network"}, {"name": "Smart Host Teknoloji TESTNET", "chainId": 1177, "shortName": "sht", "networkId": 1177, "nativeCurrency": {"name": "Smart Host Teknoloji TESTNET", "symbol": "tSHT", "decimals": 18}, "rpc": ["https://s2.tl.web.tr:4041"], "faucets": [], "infoURL": "https://smart-host.com.tr"}, {"name": "ClubMos Mainnet", "chainId": 1188, "shortName": "MOS", "networkId": 1188, "nativeCurrency": {"name": "ClubMos", "symbol": "MOS", "decimals": 18}, "rpc": ["https://mainnet.mosscan.com"], "faucets": [], "infoURL": "https://www.mosscan.com"}, {"name": "Iora Chain", "chainId": 1197, "shortName": "iora", "networkId": 1197, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "IORA", "decimals": 18}, "rpc": ["https://dataseed.iorachain.com"], "faucets": [], "infoURL": "https://iorachain.com"}, {"name": "Evanesco Testnet", "chainId": 1201, "shortName": "avis", "networkId": 1201, "nativeCurrency": {"name": "AVIS", "symbol": "AVIS", "decimals": 18}, "rpc": ["https://seed5.evanesco.org:8547"], "faucets": [], "infoURL": "https://evanesco.org/"}, {"name": "World Trade Technical Chain Mainnet", "chainId": 1202, "shortName": "wtt", "networkId": 2048, "nativeCurrency": {"name": "World Trade Token", "symbol": "WTT", "decimals": 18}, "rpc": ["https://rpc.cadaut.com", "wss://rpc.cadaut.com/ws"], "faucets": [], "infoURL": "http://www.cadaut.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>(SBC)", "chainId": 1209, "shortName": "SBC", "networkId": 1209, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>(SBC)", "symbol": "STC", "decimals": 18}, "rpc": ["https://rpc-nodes.saitascan.io"], "faucets": [], "infoURL": "https://saitachain.com"}, {"name": "Popcateum Mainnet", "chainId": 1213, "shortName": "popcat", "networkId": 1213, "nativeCurrency": {"name": "Popcat", "symbol": "POP", "decimals": 18}, "rpc": ["https://dataseed.popcateum.org"], "faucets": [], "infoURL": "https://popcateum.org"}, {"name": "Enter<PERSON>hain <PERSON>", "chainId": 1214, "shortName": "enter", "networkId": 1214, "nativeCurrency": {"name": "EnterCoin", "symbol": "ENTER", "decimals": 18}, "rpc": ["https://tapi.entercoin.net/"], "faucets": [], "infoURL": "https://entercoin.net"}, {"name": "Cycle Network Testnet", "chainId": 1221, "shortName": "Cycle", "networkId": 1221, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-testnet.cyclenetwork.io"], "faucets": [], "infoURL": "https://www.cyclenetwork.io/"}, {"name": "Hybrid Testnet", "chainId": 1224, "shortName": "hyb", "networkId": 1224, "nativeCurrency": {"name": "Hybrid", "symbol": "HYB", "decimals": 18}, "rpc": ["https://testnet-rpc.buildonhybrid.com"], "faucets": [], "infoURL": "https://buildonhybrid.com"}, {"name": "Exzo Network Mainnet", "chainId": 1229, "shortName": "xzo", "networkId": 1229, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "XZO", "decimals": 18}, "rpc": ["https://mainnet.exzo.technology"], "faucets": [], "infoURL": "https://exzo.network"}, {"name": "Ultron Testnet", "chainId": 1230, "shortName": "UltronTestnet", "networkId": 1230, "nativeCurrency": {"name": "Ultron", "symbol": "ULX", "decimals": 18}, "rpc": ["https://ultron-dev.io"], "faucets": [], "infoURL": "https://ultron.foundation"}, {"name": "Ultron Mainnet", "chainId": 1231, "shortName": "UtronMainnet", "networkId": 1231, "nativeCurrency": {"name": "Ultron", "symbol": "ULX", "decimals": 18}, "rpc": ["https://ultron-rpc.net"], "faucets": [], "infoURL": "https://ultron.foundation"}, {"name": "Step Network", "chainId": 1234, "shortName": "step", "networkId": 1234, "nativeCurrency": {"name": "FITFI", "symbol": "FITFI", "decimals": 18}, "rpc": ["https://rpc.step.network"], "faucets": [], "infoURL": "https://step.network"}, {"name": "ARC Mainnet", "chainId": 1243, "shortName": "ARC", "networkId": 1243, "nativeCurrency": {"name": "ARC", "symbol": "ARC", "decimals": 18}, "rpc": ["https://rpc-main-1.archiechain.io"], "faucets": [], "infoURL": "https://archiechain.io/"}, {"name": "ARC Testnet", "chainId": 1244, "shortName": "TARC", "networkId": 1244, "nativeCurrency": {"name": "ARC", "symbol": "ARC", "decimals": 18}, "rpc": ["https://rpc-test-1.archiechain.io"], "faucets": ["https://faucet.archiechain.io"], "infoURL": "https://archiechain.io/"}, {"name": "OM Platform Mainnet", "chainId": 1246, "shortName": "om", "networkId": 1246, "nativeCurrency": {"name": "OMCOIN", "symbol": "OM", "decimals": 18}, "rpc": ["https://rpc-cnx.omplatform.com/"], "faucets": [], "infoURL": "https://omplatform.com/"}, {"name": "<PERSON><PERSON>er Mainnet", "chainId": 1248, "shortName": "<PERSON><PERSON><PERSON>", "networkId": 1248, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "dogeth", "decimals": 18}, "rpc": ["https://rpc.dogether.dog/"], "faucets": [], "infoURL": "https://www.dogether.dog/"}, {"name": "CIC Chain Testnet", "chainId": 1252, "shortName": "CICT", "networkId": 1252, "nativeCurrency": {"name": "Crazy Internet Coin", "symbol": "CICT", "decimals": 18}, "rpc": ["https://testapi.cicscan.com"], "faucets": ["https://cicfaucet.com"], "infoURL": "https://www.cicchain.net"}, {"name": "HALO Mainnet", "chainId": 1280, "shortName": "HO", "networkId": 1280, "nativeCurrency": {"name": "HALO", "symbol": "HO", "decimals": 18}, "rpc": ["https://nodes.halo.land"], "faucets": [], "infoURL": "https://halo.land/#/"}, {"name": "Moonbeam", "chainId": 1284, "shortName": "mbeam", "networkId": 1284, "nativeCurrency": {"name": "Glimmer", "symbol": "GLMR", "decimals": 18}, "rpc": ["https://rpc.api.moonbeam.network", "wss://wss.api.moonbeam.network", "https://moonbeam-rpc.publicnode.com", "wss://moonbeam-rpc.publicnode.com", "https://moonbeam.drpc.org", "wss://moonbeam.drpc.org"], "faucets": [], "infoURL": "https://moonbeam.network/networks/moonbeam/"}, {"name": "Moonriver", "chainId": 1285, "shortName": "mriver", "networkId": 1285, "nativeCurrency": {"name": "Moonriver", "symbol": "MOVR", "decimals": 18}, "rpc": ["https://rpc.api.moonriver.moonbeam.network", "wss://wss.api.moonriver.moonbeam.network", "https://moonriver-rpc.publicnode.com", "wss://moonriver-rpc.publicnode.com", "https://moonriver.drpc.org", "wss://moonriver.drpc.org"], "faucets": [], "infoURL": "https://moonbeam.network/networks/moonriver/"}, {"name": "Moonrock old", "chainId": 1286, "shortName": "mrock-old", "networkId": 1286, "nativeCurrency": {"name": "Rocs", "symbol": "ROC", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": ""}, {"name": "Moonbase Alpha", "chainId": 1287, "shortName": "mbase", "networkId": 1287, "nativeCurrency": {"name": "<PERSON>", "symbol": "DEV", "decimals": 18}, "rpc": ["https://rpc.api.moonbase.moonbeam.network", "wss://wss.api.moonbase.moonbeam.network", "https://moonbase-alpha.drpc.org", "wss://moonbase-alpha.drpc.org"], "faucets": [], "infoURL": "https://docs.moonbeam.network/networks/testnet/"}, {"name": "Moonrock", "chainId": 1288, "shortName": "mrock", "networkId": 1288, "nativeCurrency": {"name": "Rocs", "symbol": "ROC", "decimals": 18}, "rpc": ["https://rpc.api.moonrock.moonbeam.network", "wss://wss.api.moonrock.moonbeam.network"], "faucets": [], "infoURL": "https://docs.moonbeam.network/learn/platform/networks/overview/"}, {"name": "Swisstronik Testnet", "chainId": 1291, "shortName": "swtr", "networkId": 1291, "nativeCurrency": {"name": "Swisstronik", "symbol": "SWTR", "decimals": 18}, "rpc": ["https://json-rpc.testnet.swisstronik.com"], "faucets": ["https://faucet.testnet.swisstronik.com"], "infoURL": "https://www.swisstronik.com"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 1294, "shortName": "<PERSON><PERSON><PERSON>", "networkId": 1294, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "BOBA", "decimals": 18}, "rpc": ["https://bobabeam.boba.network", "wss://wss.bobabeam.boba.network", "https://replica.bobabeam.boba.network", "wss://replica-wss.bobabeam.boba.network"], "faucets": [], "infoURL": "https://boba.network"}, {"name": "Bobabase Testnet", "chainId": 1297, "shortName": "<PERSON><PERSON><PERSON>", "networkId": 1297, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "BOBA", "decimals": 18}, "rpc": ["https://bobabase.boba.network", "wss://wss.bobabase.boba.network", "https://replica.bobabase.boba.network", "wss://replica-wss.bobabase.boba.network"], "faucets": [], "infoURL": "https://boba.network"}, {"name": "Dos Fuji Subnet", "chainId": 1311, "shortName": "TDOS", "networkId": 1311, "nativeCurrency": {"name": "Dos Native Token", "symbol": "DOS", "decimals": 18}, "rpc": ["https://test.doschain.com/jsonrpc"], "faucets": [], "infoURL": "http://doschain.io/"}, {"name": "Alyx Mainnet", "chainId": 1314, "shortName": "alyx", "networkId": 1314, "nativeCurrency": {"name": "Alyx Chain Native Token", "symbol": "ALYX", "decimals": 18}, "rpc": ["https://rpc.alyxchain.com"], "faucets": [], "infoURL": "https://www.alyxchain.com"}, {"name": "AIA Mainnet", "chainId": 1319, "shortName": "aia", "networkId": 1319, "nativeCurrency": {"name": "AIA Mainnet", "symbol": "AIA", "decimals": 18}, "rpc": ["https://aia-dataseed1.aiachain.org", "https://aia-dataseed2.aiachain.org", "https://aia-dataseed3.aiachain.org", "https://aia-dataseed4.aiachain.org"], "faucets": [], "infoURL": "https://aiachain.org/"}, {"name": "AIA Testnet", "chainId": 1320, "shortName": "aiatestnet", "networkId": 1320, "nativeCurrency": {"name": "AIA Testnet", "symbol": "AIA", "decimals": 18}, "rpc": ["https://aia-dataseed1-testnet.aiachain.org"], "faucets": ["https://aia-faucet-testnet.aiachain.org"], "infoURL": "https://aiachain.org"}, {"name": "<PERSON><PERSON>", "chainId": 1337, "shortName": "geth", "networkId": 1337, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["http://127.0.0.1:8545"], "faucets": [], "infoURL": "https://geth.ethereum.org"}, {"name": "Elysium Testnet", "chainId": 1338, "shortName": "ELST", "networkId": 1338, "nativeCurrency": {"name": "LAVA", "symbol": "LAVA", "decimals": 18}, "rpc": ["https://elysium-test-rpc.vulcanforged.com"], "faucets": [], "infoURL": "https://elysiumscan.vulcanforged.com"}, {"name": "Elysium Mainnet", "chainId": 1339, "shortName": "ELSM", "networkId": 1339, "nativeCurrency": {"name": "LAVA", "symbol": "LAVA", "decimals": 18}, "rpc": ["https://rpc.elysiumchain.tech/"], "faucets": [], "infoURL": "https://elysiumscan.vulcanforged.com"}, {"name": "Blitz Subnet", "chainId": 1343, "shortName": "blitz", "networkId": 1343, "nativeCurrency": {"name": "BLITZ GAS", "symbol": "BGAS", "decimals": 18}, "rpc": ["https://subnets.avax.network/blitz/testnet/rpc"], "faucets": [], "infoURL": "https://blitz.gg"}, {"name": "CIC Chain Mainnet", "chainId": 1353, "shortName": "CIC", "networkId": 1353, "nativeCurrency": {"name": "Crazy Internet Coin", "symbol": "CIC", "decimals": 18}, "rpc": ["https://xapi.cicscan.com"], "faucets": [], "infoURL": "https://www.cicchain.net"}, {"name": "Zafirium Mainnet", "chainId": 1369, "shortName": "<PERSON><PERSON><PERSON>", "networkId": 1369, "nativeCurrency": {"name": "Zakumi Chain Native Token", "symbol": "ZAFIC", "decimals": 18}, "rpc": ["https://mainnet.zakumi.io"], "faucets": [], "infoURL": "https://www.zakumi.io"}, {"name": "<PERSON><PERSON>", "chainId": 1379, "shortName": "KLC", "networkId": 1379, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "KLC", "decimals": 18}, "rpc": ["https://rpc-api.kalarchain.tech"], "faucets": [], "infoURL": "https://kalarchain.tech"}, {"name": "AmStar Mainnet", "chainId": 1388, "shortName": "ASAR", "networkId": 1388, "nativeCurrency": {"name": "SINSO", "symbol": "SINSO", "decimals": 18}, "rpc": ["https://mainnet-rpc.amstarscan.com"], "faucets": [], "infoURL": "https://sinso.io"}, {"name": "Joseon Mainnet", "chainId": 1392, "shortName": "mun", "networkId": 1392, "nativeCurrency": {"name": "<PERSON><PERSON> Mun", "symbol": "JSM", "decimals": 18}, "rpc": ["https://rpc.modchain.net/blockchain.joseon.com/rpc"], "faucets": [], "infoURL": "https://www.joseon.com/"}, {"name": "Polygon zkEVM Testnet old", "chainId": 1402, "shortName": "zkevmtest", "networkId": 1402, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://polygon.technology/solutions/polygon-zkevm/"}, {"name": "Polygon zkEVM Testnet Pre Audit-Upgraded", "chainId": 1422, "shortName": "testnet-zkEVM-mango-pre-audit-upgraded", "networkId": 1422, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://polygon.technology/solutions/polygon-zkevm/"}, {"name": "Rikeza Network Mainnet", "chainId": 1433, "shortName": "RIK", "networkId": 1433, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "RIK", "decimals": 18}, "rpc": ["https://rpc.rikscan.com"], "faucets": [], "infoURL": "https://rikeza.io"}, {"name": "Living Assets Mainnet", "chainId": 1440, "shortName": "LAS", "networkId": 1440, "nativeCurrency": {"name": "LAS", "symbol": "LAS", "decimals": 18}, "rpc": ["https://beta.mainnet.livingassets.io/rpc", "https://gamma.mainnet.livingassets.io/rpc"], "faucets": [], "infoURL": "https://dev.livingassets.io/"}, {"name": "Polygon zkEVM Testnet", "chainId": 1442, "shortName": "testnet-zkEVM-mango", "networkId": 1442, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.public.zkevm-test.net", "https://polygon-zkevm-testnet.drpc.org", "wss://polygon-zkevm-testnet.drpc.org"], "faucets": [], "infoURL": "https://polygon.technology/solutions/polygon-zkevm/"}, {"name": "GIL Testnet", "chainId": 1452, "shortName": "gil", "networkId": 1452, "nativeCurrency": {"name": "GANG", "symbol": "GANG", "decimals": 18}, "rpc": ["https://rpc.giltestnet.com"], "faucets": [], "infoURL": "https://gaussgang.com/"}, {"name": "MetaChain Istanbul", "chainId": 1453, "shortName": "metatimeistanbul", "networkId": 1453, "nativeCurrency": {"name": "Metatime Coin", "symbol": "MTC", "decimals": 18}, "rpc": ["https://istanbul-rpc.metachain.dev"], "faucets": ["https://istanbul-faucet.metachain.dev"], "infoURL": "https://metatime.com/en"}, {"name": "Ctex Scan Blockchain", "chainId": 1455, "shortName": "CTEX", "networkId": 1455, "nativeCurrency": {"name": "CTEX", "symbol": "CTEX", "decimals": 18}, "rpc": ["https://mainnet-rpc.ctexscan.com/"], "faucets": ["https://faucet.ctexscan.com"], "infoURL": "https://ctextoken.io"}, {"name": "Vitruveo Mainnet", "chainId": 1490, "shortName": "vitruveo", "networkId": 1490, "nativeCurrency": {"name": "Vitruveo Coin", "symbol": "VTRU", "decimals": 18}, "rpc": ["https://rpc.vitruveo.xyz"], "faucets": [], "infoURL": "https://www.vitruveo.xyz"}, {"name": "iDos Games Chain Testnet", "chainId": 1499, "shortName": "IGC", "networkId": 1499, "nativeCurrency": {"name": "iDos Games Coin", "symbol": "IGC", "decimals": 18}, "rpc": ["https://rpc-testnet.idos.games"], "faucets": [], "infoURL": "https://idosgames.com/"}, {"name": "BEVM Canary", "chainId": 1501, "shortName": "chainx", "networkId": 1501, "nativeCurrency": {"name": "BTC", "symbol": "BTC", "decimals": 18}, "rpc": ["https://rpc-canary-1.bevm.io/", "https://rpc-canary-2.bevm.io/"], "faucets": [], "infoURL": "https://chainx.org"}, {"name": "Sherpax Mainnet", "chainId": 1506, "shortName": "<PERSON><PERSON><PERSON>", "networkId": 1506, "nativeCurrency": {"name": "KSX", "symbol": "KSX", "decimals": 18}, "rpc": ["https://mainnet.sherpax.io/rpc"], "faucets": [], "infoURL": "https://sherpax.io/"}, {"name": "Sherpax Testnet", "chainId": 1507, "shortName": "SherpaxTestnet", "networkId": 1507, "nativeCurrency": {"name": "KSX", "symbol": "KSX", "decimals": 18}, "rpc": ["https://sherpax-testnet.chainx.org/rpc"], "faucets": [], "infoURL": "https://sherpax.io/"}, {"name": "Beagle Messaging Chain", "chainId": 1515, "shortName": "beagle", "networkId": 1515, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "BG", "decimals": 18}, "rpc": ["https://beagle.chat/eth"], "faucets": ["https://faucet.beagle.chat/"], "infoURL": "https://beagle.chat/"}, {"name": "Tenet", "chainId": 1559, "shortName": "tenet", "networkId": 1559, "nativeCurrency": {"name": "TENET", "symbol": "TENET", "decimals": 18}, "rpc": ["https://rpc.tenet.org", "https://tenet-evm.publicnode.com", "wss://tenet-evm.publicnode.com"], "faucets": [], "infoURL": "https://tenet.org/"}, {"name": "Ethereum Inscription Mainnet", "chainId": 1617, "shortName": "etins", "networkId": 1617, "nativeCurrency": {"name": "Ethereum Inscription", "symbol": "ETINS", "decimals": 18}, "rpc": ["https://rpc.etins.org"], "faucets": [], "infoURL": "https://www.etins.org"}, {"name": "Catecoin Chain Mainnet", "chainId": 1618, "shortName": "cate", "networkId": 1618, "nativeCurrency": {"name": "Catecoin", "symbol": "CATE", "decimals": 18}, "rpc": ["https://send.catechain.com"], "faucets": [], "infoURL": "https://catechain.com"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 1620, "shortName": "ath", "networkId": 11235813, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "ATH", "decimals": 18}, "rpc": ["https://rpc.atheios.org/"], "faucets": [], "infoURL": "https://atheios.org"}, {"name": "Btachain", "chainId": 1657, "shortName": "bta", "networkId": 1657, "nativeCurrency": {"name": "Bitcoin Asset", "symbol": "BTA", "decimals": 18}, "rpc": ["https://dataseed1.btachain.com/"], "faucets": [], "infoURL": "https://bitcoinasset.io/"}, {"name": "Liquichain", "chainId": 1662, "shortName": "Liquichain", "networkId": 1662, "nativeCurrency": {"name": "Licoin", "symbol": "LCN", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://liquichain.io/"}, {"name": "Horizen Gobi Testnet", "chainId": 1663, "shortName": "Gobi", "networkId": 1663, "nativeCurrency": {"name": "Testnet Zen", "symbol": "tZEN", "decimals": 18}, "rpc": ["https://gobi-rpc.horizenlabs.io/ethv1", "https://rpc.ankr.com/horizen_gobi_testnet"], "faucets": ["https://faucet.horizen.io"], "infoURL": "https://horizen.io/"}, {"name": "Mint Testnet", "chainId": 1686, "shortName": "minttest", "networkId": 1686, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://testnet-rpc.mintchain.io"], "faucets": [], "infoURL": "https://www.mintchain.io"}, {"name": "Mint Sepolia Testnet", "chainId": 1687, "shortName": "mintsepoliatest", "networkId": 1687, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://sepolia-testnet-rpc.mintchain.io"], "faucets": [], "infoURL": "https://www.mintchain.io"}, {"name": "LUDAN Mainnet", "chainId": 1688, "shortName": "LUDAN", "networkId": 1688, "nativeCurrency": {"name": "LUDAN", "symbol": "LUDAN", "decimals": 18}, "rpc": ["https://rpc.ludan.org/"], "faucets": [], "infoURL": "https://www.ludan.org/"}, {"name": "Anytype EVM Chain", "chainId": 1701, "shortName": "Any<PERSON><PERSON><PERSON><PERSON>", "networkId": 1701, "nativeCurrency": {"name": "ANY", "symbol": "ANY", "decimals": 18}, "rpc": ["https://geth.anytype.io"], "faucets": ["https://evm.anytype.io/faucet"], "infoURL": "https://evm.anytype.io"}, {"name": "TBSI Mainnet", "chainId": 1707, "shortName": "TBSI", "networkId": 1707, "nativeCurrency": {"name": "Jin<PERSON>", "symbol": "JINDA", "decimals": 18}, "rpc": ["https://rpc.blockchain.or.th"], "faucets": [], "infoURL": "https://blockchain.or.th"}, {"name": "TBSI Testnet", "chainId": 1708, "shortName": "tTBSI", "networkId": 1708, "nativeCurrency": {"name": "Jin<PERSON>", "symbol": "JINDA", "decimals": 18}, "rpc": ["https://rpc.testnet.blockchain.or.th"], "faucets": ["https://faucet.blockchain.or.th"], "infoURL": "https://blockchain.or.th"}, {"name": "Doric Network", "chainId": 1717, "shortName": "DRC", "networkId": 1717, "nativeCurrency": {"name": "Doric Native Token", "symbol": "DRC", "decimals": 18}, "rpc": ["https://mainnet.doric.network"], "faucets": [], "infoURL": "https://doric.network"}, {"name": "Palette Chain Mainnet", "chainId": 1718, "shortName": "PCM", "networkId": 1718, "nativeCurrency": {"name": "Pa<PERSON>", "symbol": "PLT", "decimals": 18}, "rpc": ["https://palette-rpc.com:22000"], "faucets": [], "infoURL": "https://hashpalette.com/"}, {"name": "Reya Network", "chainId": 1729, "shortName": "<PERSON>ya", "networkId": 1729, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.reya.network", "wss://ws.reya.network"], "faucets": [], "infoURL": "https://reya.network"}, {"name": "Party<PERSON><PERSON>n", "chainId": 1773, "shortName": "TeaParty", "networkId": 1773, "nativeCurrency": {"name": "Grams", "symbol": "GRAMS", "decimals": 18}, "rpc": ["https://tea.mining4people.com/rpc", "http://172.104.194.36:8545"], "faucets": [], "infoURL": "TeaPartyCrypto.com"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 1777, "shortName": "gauss", "networkId": 1777, "nativeCurrency": {"name": "GANG", "symbol": "GANG", "decimals": 18}, "rpc": ["https://rpc.gaussgang.com"], "faucets": [], "infoURL": "https://gaussgang.com/"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 1804, "shortName": "ker<PERSON>o", "networkId": 1804, "nativeCurrency": {"name": "Climate awaReness Coin", "symbol": "CRC", "decimals": 18}, "rpc": ["https://cacib-saturn-test.francecentral.cloudapp.azure.com", "wss://cacib-saturn-test.francecentral.cloudapp.azure.com:9443"], "faucets": ["https://github.com/ethereum-pocr/kerleano/blob/main/docs/faucet.md"], "infoURL": "https://github.com/ethereum-pocr/kerleano"}, {"name": "Rabbit Analog Testnet Chain", "chainId": 1807, "shortName": "rAna", "networkId": 1807, "nativeCurrency": {"name": "Rabbit Analog Test Chain Native Token ", "symbol": "rAna", "decimals": 18}, "rpc": ["https://rabbit.analog-rpc.com"], "faucets": ["https://analogfaucet.com"], "infoURL": "https://rabbit.analogscan.com"}, {"name": "Cube Chain Mainnet", "chainId": 1818, "shortName": "cube", "networkId": 1818, "nativeCurrency": {"name": "Cube Chain Native Token", "symbol": "CUBE", "decimals": 18}, "rpc": ["https://http-mainnet.cube.network", "wss://ws-mainnet.cube.network", "https://http-mainnet-sg.cube.network", "wss://ws-mainnet-sg.cube.network", "https://http-mainnet-us.cube.network", "wss://ws-mainnet-us.cube.network"], "faucets": [], "infoURL": "https://www.cube.network"}, {"name": "Cube Chain Testnet", "chainId": 1819, "shortName": "cubet", "networkId": 1819, "nativeCurrency": {"name": "Cube Chain Test Native Token", "symbol": "CUBET", "decimals": 18}, "rpc": ["https://http-testnet.cube.network", "wss://ws-testnet.cube.network", "https://http-testnet-sg.cube.network", "wss://ws-testnet-sg.cube.network", "https://http-testnet-jp.cube.network", "wss://ws-testnet-jp.cube.network", "https://http-testnet-us.cube.network", "wss://ws-testnet-us.cube.network"], "faucets": ["https://faucet.cube.network"], "infoURL": "https://www.cube.network"}, {"name": "Ruby Smart Chain MAINNET", "chainId": 1821, "shortName": "RUBY", "networkId": 1821, "nativeCurrency": {"name": "RUBY Smart Chain Native Token", "symbol": "RUBY", "decimals": 18}, "rpc": ["https://mainnet-data.rubychain.io/", "https://mainnet.rubychain.io/"], "faucets": [], "infoURL": "https://rubychain.io"}, {"name": "Teslafunds", "chainId": 1856, "shortName": "tsf", "networkId": 1, "nativeCurrency": {"name": "Teslafunds Ether", "symbol": "TSF", "decimals": 18}, "rpc": ["https://tsfapi.europool.me"], "faucets": [], "infoURL": "https://teslafunds.io"}, {"name": "Whitechain", "chainId": 1875, "shortName": "wbt", "networkId": 1875, "nativeCurrency": {"name": "WhiteBIT Coin", "symbol": "WBT", "decimals": 18}, "rpc": ["https://rpc.whitechain.io"], "faucets": [], "infoURL": "https://whitechain.io"}, {"name": "Gitshock Cartenz Testnet", "chainId": 1881, "shortName": "gitshockchain", "networkId": 1881, "nativeCurrency": {"name": "Gits<PERSON>ck Cartenz", "symbol": "tGTFX", "decimals": 18}, "rpc": ["https://rpc.cartenz.works"], "faucets": [], "infoURL": "https://gitshock.com"}, {"name": "Lightlink Phoenix Mainnet", "chainId": 1890, "shortName": "lightlink_phoenix", "networkId": 1890, "nativeCurrency": {"name": "Ethereum", "symbol": "ETH", "decimals": 18}, "rpc": ["https://replicator.phoenix.lightlink.io/rpc/v1"], "faucets": [], "infoURL": "https://lightlink.io"}, {"name": "Lightlink Pegasus Testnet", "chainId": 1891, "shortName": "lightlink_pegasus", "networkId": 1891, "nativeCurrency": {"name": "Ethereum", "symbol": "ETH", "decimals": 18}, "rpc": ["https://replicator.pegasus.lightlink.io/rpc/v1"], "faucets": ["https://faucet.pegasus.lightlink.io/"], "infoURL": "https://lightlink.io"}, {"name": "BON Network", "chainId": 1898, "shortName": "boya", "networkId": 1, "nativeCurrency": {"name": "BOYACoin", "symbol": "BOY", "decimals": 18}, "rpc": ["http://rpc.boyanet.org:8545", "ws://rpc.boyanet.org:8546"], "faucets": [], "infoURL": "https://boyanet.org"}, {"name": "Sports Chain Network", "chainId": 1904, "shortName": "SCN", "networkId": 1904, "nativeCurrency": {"name": "SCN", "symbol": "SCN", "decimals": 18}, "rpc": ["https://rpc.sportschainnetwork.xyz/"], "faucets": [], "infoURL": "https://sportschainnetwork.xyz"}, {"name": "Bitcichain Mainnet", "chainId": 1907, "shortName": "bitci", "networkId": 1907, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "BITCI", "decimals": 18}, "rpc": ["https://rpc.bitci.com"], "faucets": [], "infoURL": "https://www.bitcichain.com"}, {"name": "Bitcichain Testnet", "chainId": 1908, "shortName": "tbitci", "networkId": 1908, "nativeCurrency": {"name": "Test Bitci", "symbol": "TBITCI", "decimals": 18}, "rpc": ["https://testnet.bitcichain.com"], "faucets": ["https://faucet.bitcichain.com"], "infoURL": "https://www.bitcichain.com"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 1909, "shortName": "MRK", "networkId": 1909, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "MRK", "decimals": 18}, "rpc": ["https://marklechain-rpc.merklescan.com"], "faucets": [], "infoURL": "https://merklescan.com"}, {"name": "Scalind", "chainId": 1911, "shortName": "scal", "networkId": 1911, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.scalind.com"], "faucets": [], "infoURL": "https://scalind.com"}, {"name": "Ruby Smart Chain Testnet", "chainId": 1912, "shortName": "tRUBY", "networkId": 1912, "nativeCurrency": {"name": "RUBY Smart Chain Native Token", "symbol": "tRUBY", "decimals": 18}, "rpc": ["https://testnet-rchain.rubychain.io/"], "faucets": ["https://claim-faucet.rubychain.io/"], "infoURL": "https://rubychain.io"}, {"name": "UPB CRESCDI Testnet", "chainId": 1918, "shortName": "UPBEth", "networkId": 1918, "nativeCurrency": {"name": "UPBEth", "symbol": "UPBEth", "decimals": 18}, "rpc": ["https://testnet.crescdi.pub.ro"], "faucets": [], "infoURL": "https://mobylab.docs.crescdi.pub.ro/blog/UPB-CRESCDI-Testnet"}, {"name": "ONUS Chain Testnet", "chainId": 1945, "shortName": "onus-testnet", "networkId": 1945, "nativeCurrency": {"name": "ONUS", "symbol": "ONUS", "decimals": 18}, "rpc": ["https://rpc-testnet.onuschain.io"], "faucets": [], "infoURL": "https://onuschain.io"}, {"name": "D-Chain Mainnet", "chainId": 1951, "shortName": "dchain-mainnet", "networkId": 1951, "nativeCurrency": {"name": "DOINX", "symbol": "DOINX", "decimals": 18}, "rpc": ["https://mainnet.d-chain.network/ext/bc/2ZiR1Bro5E59siVuwdNuRFzqL95NkvkbzyLBdrsYR9BLSHV7H4/rpc"], "faucets": [], "infoURL": ""}, {"name": "Selendra Network Testnet", "chainId": 1953, "shortName": "tSEL", "networkId": 1953, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "tSEL", "decimals": 18}, "rpc": ["https://rpc0-testnet.selendra.org", "https://rpc1-testnet.selendra.org"], "faucets": [], "infoURL": "https://selendra.org"}, {"name": "Dexilla Testnet", "chainId": 1954, "shortName": "Dexilla", "networkId": 1954, "nativeCurrency": {"name": "Dexilla Native Token", "symbol": "DXZ", "decimals": 18}, "rpc": ["https://rpc.dexilla.com"], "faucets": [], "infoURL": "https://dexilla.com"}, {"name": "Selendra Network Mainnet", "chainId": 1961, "shortName": "SEL", "networkId": 1961, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "SEL", "decimals": 18}, "rpc": ["https://rpc0.selendra.org", "https://rpc1.selendra.org"], "faucets": [], "infoURL": "https://selendra.org"}, {"name": "<PERSON>", "chainId": 1967, "shortName": "mtc", "networkId": 1967, "nativeCurrency": {"name": "<PERSON>", "symbol": "MTC", "decimals": 18}, "rpc": ["https://rpc.metatime.com/eleanor", "wss://ws.metatime.com/eleanor"], "faucets": ["https://faucet.metatime.com/eleanor"], "infoURL": "https://eleanor.metatime.com"}, {"name": "Super Smart Chain Testnet", "chainId": 1969, "shortName": "tscs", "networkId": 1969, "nativeCurrency": {"name": "Super Chain Native Token", "symbol": "TSCS", "decimals": 18}, "rpc": ["https://testnetrpc.scschain.com"], "faucets": ["https://testnet.scschain.com"], "infoURL": "https://testnet.scschain.com"}, {"name": "Super Smart Chain Mainnet", "chainId": 1970, "shortName": "scs", "networkId": 1970, "nativeCurrency": {"name": "Super Chain Native Token", "symbol": "SCS", "decimals": 18}, "rpc": ["https://rpc.scschain.com"], "faucets": [], "infoURL": "https://scschain.com"}, {"name": "Atelier", "chainId": 1971, "shortName": "atlr", "networkId": 1971, "nativeCurrency": {"name": "ATLR", "symbol": "ATLR", "decimals": 18}, "rpc": ["https://1971.network/atlr", "wss://1971.network/atlr"], "faucets": [], "infoURL": "https://1971.network/"}, {"name": "RedeCoin", "chainId": 1972, "shortName": "rede", "networkId": 1972, "nativeCurrency": {"name": "RedeCoin", "symbol": "REDEV2", "decimals": 18}, "rpc": ["https://rpc2.redecoin.eu"], "faucets": [], "infoURL": "https://www.redecoin.eu"}, {"name": "ONUS Chain Mainnet", "chainId": 1975, "shortName": "onus-mainnet", "networkId": 1975, "nativeCurrency": {"name": "ONUS", "symbol": "ONUS", "decimals": 18}, "rpc": ["https://rpc.onuschain.io", "wss://ws.onuschain.io"], "faucets": [], "infoURL": "https://onuschain.io"}, {"name": "<PERSON><PERSON>", "chainId": 1984, "shortName": "euntest", "networkId": 1984, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "EUN", "decimals": 18}, "rpc": ["https://testnet.eurus.network"], "faucets": [], "infoURL": "https://eurus.network"}, {"name": "SatoshIE", "chainId": 1985, "shortName": "satoshie", "networkId": 1985, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "TUSHY", "decimals": 18}, "rpc": ["http://rpc.satosh.ie"], "faucets": [], "infoURL": "https://satosh.ie"}, {"name": "SatoshIE Testnet", "chainId": 1986, "shortName": "satoshie_testnet", "networkId": 1986, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "TUSHY", "decimals": 18}, "rpc": ["http://testnet.satosh.ie"], "faucets": [], "infoURL": "https://satosh.ie"}, {"name": "EtherGem", "chainId": 1987, "shortName": "egem", "networkId": 1987, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "EGEM", "decimals": 18}, "rpc": ["https://jsonrpc.egem.io/custom"], "faucets": [], "infoURL": "https://egem.io"}, {"name": "Hubble Exchange", "chainId": 1992, "shortName": "hubblenet", "networkId": 1992, "nativeCurrency": {"name": "USD Coin", "symbol": "USDC", "decimals": 18}, "rpc": ["https://rpc.hubble.exchange", "wss://ws-rpc.hubble.exchange"], "faucets": [], "infoURL": "https://www.hubble.exchange"}, {"name": "Ekta", "chainId": 1994, "shortName": "ekta", "networkId": 1994, "nativeCurrency": {"name": "EKTA", "symbol": "EKTA", "decimals": 18}, "rpc": ["https://main.ekta.io"], "faucets": [], "infoURL": "https://www.ekta.io"}, {"name": "edeXa Testnet", "chainId": 1995, "shortName": "edx", "networkId": 1995, "nativeCurrency": {"name": "EDEXA", "symbol": "EDX", "decimals": 18}, "rpc": ["https://testnet.edexa.network/rpc", "https://io-dataseed1.testnet.edexa.io-market.com/rpc"], "faucets": ["https://faucet.edexa.com/"], "infoURL": "https://edexa.network/"}, {"name": "Kyoto Testnet", "chainId": 1998, "shortName": "kyoto-testnet", "networkId": 1998, "nativeCurrency": {"name": "Kyoto", "symbol": "KYOTO", "decimals": 18}, "rpc": ["https://rpc.testnet.kyotoprotocol.io:8545"], "faucets": ["https://faucet.kyotoprotocol.io"], "infoURL": "https://kyotoprotocol.io"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 2000, "shortName": "dc", "networkId": 2000, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "DOGE", "decimals": 18}, "rpc": ["https://rpc.dogechain.dog", "https://rpc01-sg.dogechain.dog", "https://rpc.ankr.com/dogechain"], "faucets": [], "infoURL": "https://dogechain.dog"}, {"name": "Milkomeda C1 Mainnet", "chainId": 2001, "shortName": "milkAda", "networkId": 2001, "nativeCurrency": {"name": "milkAda", "symbol": "mADA", "decimals": 18}, "rpc": ["https://rpc-mainnet-cardano-evm.c1.milkomeda.com", "wss://rpc-mainnet-cardano-evm.c1.milkomeda.com"], "faucets": [], "infoURL": "https://milkomeda.com"}, {"name": "Milkomeda A1 Mainnet", "chainId": 2002, "shortName": "milkALGO", "networkId": 2002, "nativeCurrency": {"name": "milkALGO", "symbol": "mALGO", "decimals": 18}, "rpc": ["https://rpc-mainnet-algorand-rollup.a1.milkomeda.com", "wss://rpc-mainnet-algorand-rollup.a1.milkomeda.com/ws"], "faucets": [], "infoURL": "https://milkomeda.com"}, {"name": "CloudWalk Testnet", "chainId": 2008, "shortName": "cloudwalk_testnet", "networkId": 2008, "nativeCurrency": {"name": "CloudWalk Native Token", "symbol": "CWN", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://cloudwalk.io"}, {"name": "CloudWalk Mainnet", "chainId": 2009, "shortName": "cloudwalk_mainnet", "networkId": 2009, "nativeCurrency": {"name": "CloudWalk Native Token", "symbol": "CWN", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://cloudwalk.io"}, {"name": "MainnetZ Mainnet", "chainId": 2016, "shortName": "netz", "networkId": 2016, "nativeCurrency": {"name": "MainnetZ", "symbol": "NetZ", "decimals": 18}, "rpc": ["https://mainnet-rpc.mainnetz.io", "https://eu-rpc.mainnetz.io"], "faucets": [], "infoURL": "https://mainnetz.io"}, {"name": "Adiri", "chainId": 2017, "shortName": "tel", "networkId": 2017, "nativeCurrency": {"name": "Telcoin", "symbol": "TEL", "decimals": 18}, "rpc": ["https://rpc.telcoin.network", "https://adiri.tel", "https://node1.telcoin.network", "https://node2.telcoin.network", "https://node3.telcoin.network", "https://node4.telcoin.network"], "faucets": ["https://telcoin.network/faucet"], "infoURL": "https://telcoin.network"}, {"name": "PublicMint Devnet", "chainId": 2018, "shortName": "pmint_dev", "networkId": 2018, "nativeCurrency": {"name": "USD", "symbol": "USD", "decimals": 18}, "rpc": ["https://rpc.dev.publicmint.io:8545"], "faucets": [], "infoURL": "https://publicmint.com"}, {"name": "PublicMint Testnet", "chainId": 2019, "shortName": "pmint_test", "networkId": 2019, "nativeCurrency": {"name": "USD", "symbol": "USD", "decimals": 18}, "rpc": ["https://rpc.tst.publicmint.io:8545"], "faucets": [], "infoURL": "https://publicmint.com"}, {"name": "PublicMint Mainnet", "chainId": 2020, "shortName": "pmint", "networkId": 2020, "nativeCurrency": {"name": "USD", "symbol": "USD", "decimals": 18}, "rpc": ["https://rpc.publicmint.io:8545"], "faucets": [], "infoURL": "https://publicmint.com"}, {"name": "Edgeware EdgeEVM Mainnet", "chainId": 2021, "shortName": "edg", "networkId": 2021, "nativeCurrency": {"name": "Edgeware", "symbol": "EDG", "decimals": 18}, "rpc": ["https://edgeware-evm.jelliedowl.net", "https://edgeware-evm0.jelliedowl.net", "https://edgeware-evm1.jelliedowl.net", "https://edgeware-evm2.jelliedowl.net", "https://edgeware-evm3.jelliedowl.net", "wss://edgeware.jelliedowl.net", "wss://edgeware-rpc0.jelliedowl.net", "wss://edgeware-rpc1.jelliedowl.net", "wss://edgeware-rpc2.jelliedowl.net", "wss://edgeware-rpc3.jelliedowl.net"], "faucets": [], "infoURL": "https://edgeware.io"}, {"name": "Beresheet BereEVM Testnet", "chainId": 2022, "shortName": "edgt", "networkId": 2022, "nativeCurrency": {"name": "Testnet EDG", "symbol": "tEDG", "decimals": 18}, "rpc": ["https://beresheet-evm.jelliedowl.net", "wss://beresheet.jelliedowl.net"], "faucets": [], "infoURL": "https://edgeware.io/build"}, {"name": "Taycan Testnet", "chainId": 2023, "shortName": "taycan-testnet", "networkId": 2023, "nativeCurrency": {"name": "test-Shuffle", "symbol": "tSFL", "decimals": 18}, "rpc": ["https://test-taycan.hupayx.io"], "faucets": ["https://ttaycan-faucet.hupayx.io/"], "infoURL": "https://hupayx.io"}, {"name": "Swan Saturn Testnet", "chainId": 2024, "shortName": "swan", "networkId": 2024, "nativeCurrency": {"name": "SWANETH", "symbol": "sETH", "decimals": 18}, "rpc": ["https://saturn-rpc.swanchain.io"], "faucets": [], "infoURL": "https://swanchain.io/"}, {"name": "Rangers Protocol Mainnet", "chainId": 2025, "shortName": "rpg", "networkId": 2025, "nativeCurrency": {"name": "Rangers Protocol Gas", "symbol": "RPG", "decimals": 18}, "rpc": ["https://mainnet.rangersprotocol.com/api/jsonrpc"], "faucets": [], "infoURL": "https://rangersprotocol.com"}, {"name": "Edgeless Network", "chainId": 2026, "shortName": "edgeless", "networkId": 2026, "nativeCurrency": {"name": "<PERSON><PERSON> Wrapped Eth", "symbol": "EwEth", "decimals": 18}, "rpc": ["https://rpc.edgeless.network/http"], "faucets": [], "infoURL": "https://edgeless.network"}, {"name": "Centrifuge", "chainId": 2031, "shortName": "cfg", "networkId": 2031, "nativeCurrency": {"name": "Centrifuge", "symbol": "CFG", "decimals": 18}, "rpc": ["https://fullnode.centrifuge.io", "wss://fullnode.centrifuge.io", "https://centrifuge-parachain.api.onfinality.io/public", "wss://centrifuge-parachain.api.onfinality.io/public-ws", "https://centrifuge-rpc.dwellir.com", "wss://centrifuge-rpc.dwellir.com", "https://rpc-centrifuge.luckyfriday.io", "wss://rpc-centrifuge.luckyfriday.io"], "faucets": [], "infoURL": "https://centrifuge.io"}, {"name": "Catalyst", "chainId": 2032, "shortName": "ncfg", "networkId": 2032, "nativeCurrency": {"name": "Catalyst CFG", "symbol": "NCFG", "decimals": 18}, "rpc": ["wss://fullnode.catalyst.cntrfg.com"], "faucets": [], "infoURL": "https://centrifuge.io"}, {"name": "Phala Network", "chainId": 2035, "shortName": "pha", "networkId": 2035, "nativeCurrency": {"name": "Phala", "symbol": "PHA", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://phala.network"}, {"name": "<PERSON><PERSON>", "chainId": 2037, "shortName": "kiwi", "networkId": 2037, "nativeCurrency": {"name": "Shrapgas", "symbol": "SHRAP", "decimals": 18}, "rpc": ["https://subnets.avax.network/kiwi/testnet/rpc"], "faucets": [], "infoURL": ""}, {"name": "Shrapnel Testnet", "chainId": 2038, "shortName": "shraptest", "networkId": 2038, "nativeCurrency": {"name": "SHRAPG", "symbol": "SHRAPG", "decimals": 18}, "rpc": ["https://subnets.avax.network/shrapnel/testnet/rpc"], "faucets": [], "infoURL": "https://www.shrapnel.com/"}, {"name": "<PERSON><PERSON>", "chainId": 2040, "shortName": "<PERSON><PERSON>", "networkId": 2040, "nativeCurrency": {"name": "VANRY", "symbol": "VANRY", "decimals": 18}, "rpc": ["https://rpc.vanarchain.com", "wss://ws.vanarchain.com"], "faucets": [], "infoURL": "https://vanarchain.com"}, {"name": "OriginTrail Parachain", "chainId": 2043, "shortName": "otp", "networkId": 2043, "nativeCurrency": {"name": "OriginTrail Parachain Token", "symbol": "OTP", "decimals": 12}, "rpc": ["https://astrosat.origintrail.network", "wss://parachain-rpc.origin-trail.network"], "faucets": [], "infoURL": "https://parachain.origintrail.io"}, {"name": "Shrapnel Subnet", "chainId": 2044, "shortName": "Shrapnel", "networkId": 2044, "nativeCurrency": {"name": "Shrapnel Gas Token", "symbol": "SHRAPG", "decimals": 18}, "rpc": ["https://subnets.avax.network/shrapnel/mainnet/rpc"], "faucets": [], "infoURL": "https://www.shrapnel.com/"}, {"name": "St<PERSON><PERSON>", "chainId": 2047, "shortName": "stos-testnet", "networkId": 2047, "nativeCurrency": {"name": "STOS", "symbol": "STOS", "decimals": 18}, "rpc": ["https://web3-rpc-mesos.thestratos.org"], "faucets": [], "infoURL": "https://www.thestratos.org"}, {"name": "Stratos", "chainId": 2048, "shortName": "stos-mainnet", "networkId": 2048, "nativeCurrency": {"name": "STOS", "symbol": "STOS", "decimals": 18}, "rpc": ["https://web3-rpc.thestratos.org"], "faucets": [], "infoURL": "https://www.thestratos.org"}, {"name": "Movo Smart Chain Mainnet", "chainId": 2049, "shortName": "movo", "networkId": 2049, "nativeCurrency": {"name": "Movo Smart Chain", "symbol": "MOVO", "decimals": 18}, "rpc": ["https://msc-rpc.movoscan.com", "https://msc-rpc.movochain.org", "https://msc-rpc.movoswap.com"], "faucets": [], "infoURL": "https://movo.uk"}, {"name": "Quokkacoin Mainnet", "chainId": 2077, "shortName": "QKA", "networkId": 2077, "nativeCurrency": {"name": "Qkacoin", "symbol": "QKA", "decimals": 18}, "rpc": ["https://rpc.qkacoin.org"], "faucets": [], "infoURL": "https://qkacoin.org"}, {"name": "Altair", "chainId": 2088, "shortName": "air", "networkId": 2088, "nativeCurrency": {"name": "Altair", "symbol": "AIR", "decimals": 18}, "rpc": ["wss://fullnode.altair.centrifuge.io", "wss://altair.api.onfinality.io/public-ws"], "faucets": [], "infoURL": "https://centrifuge.io"}, {"name": "Algol", "chainId": 2089, "shortName": "algl", "networkId": 2089, "nativeCurrency": {"name": "Algol", "symbol": "ALGL", "decimals": 18}, "rpc": ["wss://fullnode.algol.cntrfg.com"], "faucets": [], "infoURL": "https://centrifuge.io"}, {"name": "Ecoball Mainnet", "chainId": 2100, "shortName": "eco", "networkId": 2100, "nativeCurrency": {"name": "Ecoball Coin", "symbol": "ECO", "decimals": 18}, "rpc": ["https://api.ecoball.org/ecoball/"], "faucets": [], "infoURL": "https://ecoball.org"}, {"name": "Ecoball Testnet Espuma", "chainId": 2101, "shortName": "esp", "networkId": 2101, "nativeCurrency": {"name": "Espuma Coin", "symbol": "ECO", "decimals": 18}, "rpc": ["https://api.ecoball.org/espuma/"], "faucets": [], "infoURL": "https://ecoball.org"}, {"name": "Exosama Network", "chainId": 2109, "shortName": "exn", "networkId": 2109, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "SAMA", "decimals": 18}, "rpc": ["https://rpc.exosama.com", "wss://rpc.exosama.com"], "faucets": [], "infoURL": "https://moonsama.com"}, {"name": "<PERSON>ena <PERSON>", "chainId": 2121, "shortName": "cmcx", "networkId": 2121, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "CMCX", "decimals": 18}, "rpc": ["https://rpc1.catenarpc.com"], "faucets": [], "infoURL": "https://catena.network"}, {"name": "Metaplayerone Mainnet", "chainId": 2122, "shortName": "Metad", "networkId": 2122, "nativeCurrency": {"name": "METAD", "symbol": "METAD", "decimals": 18}, "rpc": ["https://rpc.metaplayer.one/"], "faucets": [], "infoURL": "https://docs.metaplayer.one/"}, {"name": "Metaplayerone Dubai Testnet", "chainId": 2124, "shortName": "MEU", "networkId": 2124, "nativeCurrency": {"name": "Metaunit", "symbol": "MEU", "decimals": 18}, "rpc": ["https://rpc-dubai.mp1network.com/"], "faucets": [], "infoURL": "https://docs.metaplayer.one/"}, {"name": "BigShortBets Testnet", "chainId": 2136, "shortName": "bigsb_testnet", "networkId": 2136, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "<PERSON><PERSON><PERSON>", "decimals": 18}, "rpc": ["https://test-market.bigsb.io", "wss://test-market.bigsb.io"], "faucets": [], "infoURL": "https://bigshortbets.com/"}, {"name": "BigShortBets", "chainId": 2137, "shortName": "bigsb", "networkId": 2137, "nativeCurrency": {"name": "USD Coin", "symbol": "USDC", "decimals": 18}, "rpc": ["https://market.bigsb.io", "wss://market.bigsb.io"], "faucets": [], "infoURL": "https://bigshortbets.com/"}, {"name": "Defi Oracle Meta Testnet", "chainId": 2138, "shortName": "dfio-meta-test", "networkId": 21, "nativeCurrency": {"name": "testEther", "symbol": "tETH", "decimals": 18}, "rpc": ["https://rpc.public-2138.defi-oracle.io", "wss://rpc.public-2138.defi-oracle.io"], "faucets": [], "infoURL": "https://defi-oracle.io/"}, {"name": "Oneness Network", "chainId": 2140, "shortName": "oneness", "networkId": 2140, "nativeCurrency": {"name": "BTC", "symbol": "BTC", "decimals": 18}, "rpc": ["https://rpc.onenesslabs.io/"], "faucets": [], "infoURL": ""}, {"name": "Oneness TestNet", "chainId": 2141, "shortName": "oneness-testnet", "networkId": 2141, "nativeCurrency": {"name": "BTC", "symbol": "BTC", "decimals": 18}, "rpc": ["https://rpc.testnet.onenesslabs.io/"], "faucets": [], "infoURL": ""}, {"name": "BOSagora Mainnet", "chainId": 2151, "shortName": "boa", "networkId": 2151, "nativeCurrency": {"name": "BOSAGORA", "symbol": "BOA", "decimals": 18}, "rpc": ["https://mainnet.bosagora.org", "https://rpc.bosagora.org"], "faucets": [], "infoURL": "https://docs.bosagora.org"}, {"name": "Findora Mainnet", "chainId": 2152, "shortName": "fra", "networkId": 2152, "nativeCurrency": {"name": "FRA", "symbol": "FRA", "decimals": 18}, "rpc": ["https://rpc-mainnet.findora.org"], "faucets": [], "infoURL": "https://findora.org/"}, {"name": "Findora Testnet", "chainId": 2153, "shortName": "findora-testnet", "networkId": 2153, "nativeCurrency": {"name": "FRA", "symbol": "FRA", "decimals": 18}, "rpc": ["https://prod-testnet.prod.findora.org:8545/"], "faucets": [], "infoURL": "https://findora.org/"}, {"name": "Findora <PERSON>ge", "chainId": 2154, "shortName": "findora-forge", "networkId": 2154, "nativeCurrency": {"name": "FRA", "symbol": "FRA", "decimals": 18}, "rpc": ["https://prod-forge.prod.findora.org:8545/"], "faucets": [], "infoURL": "https://findora.org/"}, {"name": "Moonsama Network", "chainId": 2199, "shortName": "msn", "networkId": 2199, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "SAMA", "decimals": 18}, "rpc": ["https://rpc.moonsama.com", "wss://rpc.moonsama.com/ws"], "faucets": ["https://multiverse.moonsama.com/faucet"], "infoURL": "https://moonsama.com"}, {"name": "Antofy Mainnet", "chainId": 2202, "shortName": "ABNm", "networkId": 2202, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "ABN", "decimals": 18}, "rpc": ["https://rpc.antofy.io"], "faucets": ["https://faucet.antofy.io"], "infoURL": "https://antofy.io"}, {"name": "Bitcoin EVM", "chainId": 2203, "shortName": "BTC", "networkId": 2203, "nativeCurrency": {"name": "Bitcoin", "symbol": "BTC", "decimals": 18}, "rpc": ["https://connect.bitcoinevm.com"], "faucets": [], "infoURL": "https://bitcoinevm.com"}, {"name": "Evanesco Mainnet", "chainId": 2213, "shortName": "evanesco", "networkId": 2213, "nativeCurrency": {"name": "EVA", "symbol": "EVA", "decimals": 18}, "rpc": ["https://seed4.evanesco.org:8546"], "faucets": [], "infoURL": "https://evanesco.org/"}, {"name": "<PERSON><PERSON>", "chainId": 2221, "shortName": "tkava", "networkId": 2221, "nativeCurrency": {"name": "TKava", "symbol": "TKAVA", "decimals": 18}, "rpc": ["https://evm.testnet.kava.io", "https://kava-evm-testnet.rpc.thirdweb.com", "wss://wevm.testnet.kava.io", "https://kava-testnet.drpc.org", "wss://kava-testnet.drpc.org"], "faucets": ["https://faucet.kava.io"], "infoURL": "https://www.kava.io"}, {"name": "<PERSON><PERSON>", "chainId": 2222, "shortName": "kava", "networkId": 2222, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "KAVA", "decimals": 18}, "rpc": ["https://evm.kava.io", "https://kava-rpc.gateway.pokt.network", "https://kava-evm.rpc.thirdweb.com", "wss://wevm.kava.io", "https://kava-evm-rpc.publicnode.com", "wss://kava-evm-rpc.publicnode.com", "https://evm.kava-rpc.com", "https://rpc.ankr.com/kava_evm", "wss://wevm.kava-rpc.com", "https://kava.drpc.org", "wss://kava.drpc.org"], "faucets": [], "infoURL": "https://www.kava.io"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 2223, "shortName": "<PERSON><PERSON><PERSON>", "networkId": 2223, "nativeCurrency": {"name": "VNDT", "symbol": "VNDT", "decimals": 18}, "rpc": ["https://bc.vcex.xyz"], "faucets": [], "infoURL": "https://bo.vcex.xyz/"}, {"name": "Krest Network", "chainId": 2241, "shortName": "KRST", "networkId": 2241, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "KRST", "decimals": 18}, "rpc": ["https://erpc-krest.peaq.network", "https://krest.unitedbloc.com"], "faucets": [], "infoURL": "https://www.peaq.network"}, {"name": "BOMB Chain", "chainId": 2300, "shortName": "bomb", "networkId": 2300, "nativeCurrency": {"name": "BOMB Token", "symbol": "BOMB", "decimals": 18}, "rpc": ["https://rpc.bombchain.com"], "faucets": [], "infoURL": "https://www.bombchain.com"}, {"name": "Ebro Network", "chainId": 2306, "shortName": "ebro", "networkId": 2306, "nativeCurrency": {"name": "Ebro", "symbol": "ebro", "decimals": 18}, "rpc": ["https://greendinoswap.com"], "faucets": [], "infoURL": "https://www.ebrochain.com"}, {"name": "Arevia", "chainId": 2309, "shortName": "arevia", "networkId": 2309, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ARÉV", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": ""}, {"name": "SOMA Network Testnet", "chainId": 2323, "shortName": "sma", "networkId": 2323, "nativeCurrency": {"name": "SMA", "symbol": "tSMA", "decimals": 18}, "rpc": ["https://data-testnet-v1.somanetwork.io/", "https://testnet-au-server-2.somanetwork.io", "https://testnet-au-server-1.somanetwork.io", "https://testnet-sg-server-1.somanetwork.io", "https://testnet-sg-server-2.somanetwork.io"], "faucets": ["https://faucet.somanetwork.io"], "infoURL": "https://somanetwork.io"}, {"name": "Altcoinchain", "chainId": 2330, "shortName": "alt", "networkId": 2330, "nativeCurrency": {"name": "Altcoin", "symbol": "ALT", "decimals": 18}, "rpc": ["https://rpc0.altcoinchain.org/rpc"], "faucets": [], "infoURL": "https://altcoinchain.org"}, {"name": "RSS3 VSL Sepolia Testnet", "chainId": 2331, "shortName": "rss3-testnet", "networkId": 2331, "nativeCurrency": {"name": "RSS3", "symbol": "RSS3", "decimals": 18}, "rpc": ["https://rpc.testnet.rss3.io"], "faucets": [], "infoURL": "https://rss3.io"}, {"name": "SOMA Network Mainnet", "chainId": 2332, "shortName": "smam", "networkId": 2332, "nativeCurrency": {"name": "Soma Native Token", "symbol": "SMA", "decimals": 18}, "rpc": ["https://data-mainnet-v1.somanetwork.io/", "https://id-mainnet.somanetwork.io", "https://hk-mainnet.somanetwork.io", "https://sg-mainnet.somanetwork.io"], "faucets": ["https://airdrop.somanetwork.io"], "infoURL": "https://somanetwork.io"}, {"name": "Atleta Olympia", "chainId": 2340, "shortName": "atla", "networkId": 2340, "nativeCurrency": {"name": "Atla", "symbol": "ATLA", "decimals": 18}, "rpc": ["wss://testnet-rpc.atleta.network:9944", "https://testnet-rpc.atleta.network:9944"], "faucets": ["https://faucet.atleta.network"], "infoURL": "https://atleta.network"}, {"name": "Omnia Chain", "chainId": 2342, "shortName": "omnia", "networkId": 2342, "nativeCurrency": {"name": "Omnia", "symbol": "OMNIA", "decimals": 18}, "rpc": ["https://rpc.omniaverse.io"], "faucets": ["https://www.omniaverse.io"], "infoURL": "https://www.omniaverse.io"}, {"name": "(deprecated) Kroma Sepolia", "chainId": 2357, "shortName": "deprecated-kroma-sepolia", "networkId": 2357, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://api.sepolia-deprecated.kroma.network"], "faucets": [], "infoURL": "https://kroma.network"}, {"name": "Kroma Sep<PERSON>", "chainId": 2358, "shortName": "kroma-sepolia", "networkId": 2358, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://api.sepolia.kroma.network"], "faucets": [], "infoURL": "https://kroma.network"}, {"name": "Nexis Network Testnet", "chainId": 2370, "shortName": "nzt", "networkId": 2370, "nativeCurrency": {"name": "Nexis", "symbol": "NZT", "decimals": 18}, "rpc": ["https://evm-testnet.nexis.network"], "faucets": ["https://evm-faucet.nexis.network"], "infoURL": "https://nexis.network/"}, {"name": "BOMB Chain Testnet", "chainId": 2399, "shortName": "bombt", "networkId": 2399, "nativeCurrency": {"name": "BOMB Token", "symbol": "tBOMB", "decimals": 18}, "rpc": ["https://bombchain-testnet.ankr.com/bas_full_rpc_1"], "faucets": ["https://faucet.bombchain-testnet.ankr.com/"], "infoURL": "https://www.bombmoney.com"}, {"name": "TCG Verse Mainnet", "chainId": 2400, "shortName": "TCGV", "networkId": 2400, "nativeCurrency": {"name": "OAS", "symbol": "OAS", "decimals": 18}, "rpc": ["https://rpc.tcgverse.xyz"], "faucets": [], "infoURL": "https://tcgverse.xyz/"}, {"name": "Karak <PERSON>net", "chainId": 2410, "shortName": "karak-mainnet", "networkId": 2410, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.karak.network"], "faucets": [], "infoURL": "https://karak.network"}, {"name": "XODEX", "chainId": 2415, "shortName": "xodex", "networkId": 10, "nativeCurrency": {"name": "XODEX Native Token", "symbol": "XODEX", "decimals": 18}, "rpc": ["https://mainnet.xo-dex.com/rpc", "https://xo-dex.io"], "faucets": [], "infoURL": "https://xo-dex.com"}, {"name": "King Of Legends Devnet", "chainId": 2425, "shortName": "kol", "networkId": 2425, "nativeCurrency": {"name": "King Of Legends", "symbol": "KOL", "decimals": 18}, "rpc": ["https://rpc-devnet.kinggamer.org/"], "faucets": [], "infoURL": "https://kingoflegends.net/"}, {"name": "Polygon zkEVM Cardona Testnet", "chainId": 2442, "shortName": "zkevm-testnet-cardona", "networkId": 2442, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.cardona.zkevm-rpc.com"], "faucets": [], "infoURL": "https://polygon.technology/polygon-zkevm"}, {"name": "Hybrid Chain Network Testnet", "chainId": 2458, "shortName": "thrc", "networkId": 2458, "nativeCurrency": {"name": "Hybrid Chain Native Token", "symbol": "tHRC", "decimals": 18}, "rpc": ["https://rpc-testnet.hybridchain.ai/"], "faucets": ["https://faucet-testnet.hybridchain.ai"], "infoURL": "https://hybridchain.ai"}, {"name": "Hybrid Chain Network Mainnet", "chainId": 2468, "shortName": "hrc", "networkId": 2468, "nativeCurrency": {"name": "Hybrid Chain Native Token", "symbol": "HRC", "decimals": 18}, "rpc": ["https://coredata-mainnet.hybridchain.ai/", "https://rpc-mainnet.hybridchain.ai"], "faucets": ["https://faucet-testnet.hybridchain.ai"], "infoURL": "https://hybridchain.ai"}, {"name": "Unicorn Ultra Nebulas Testnet", "chainId": 2484, "shortName": "u2u_nebulas", "networkId": 2484, "nativeCurrency": {"name": "Unicorn Ultra Nebulas Testnet", "symbol": "U2U", "decimals": 18}, "rpc": ["https://rpc-nebulas-testnet.uniultra.xyz"], "faucets": ["https://faucet.uniultra.xyz"], "infoURL": "https://uniultra.xyz"}, {"name": "<PERSON><PERSON>", "chainId": 2511, "shortName": "karak-goerli", "networkId": 2511, "nativeCurrency": {"name": "Karak", "symbol": "KRK", "decimals": 18}, "rpc": ["https://goerli.node1.karak.network"], "faucets": [], "infoURL": "https://karak.network"}, {"name": "Fraxtal Testnet", "chainId": 2522, "shortName": "fraxtal-testnet", "networkId": 2522, "nativeCurrency": {"name": "Fr<PERSON>", "symbol": "frxETH", "decimals": 18}, "rpc": ["https://rpc.testnet.frax.com"], "faucets": [], "infoURL": "https://testnet.frax.com"}, {"name": "inEVM Mainnet", "chainId": 2525, "shortName": "inevm", "networkId": 2525, "nativeCurrency": {"name": "Injective", "symbol": "INJ", "decimals": 18}, "rpc": ["https://mainnet.rpc.inevm.com/http"], "faucets": [], "infoURL": "https://inevm.com"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 2559, "shortName": "ktoc", "networkId": 2559, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "KTO", "decimals": 11}, "rpc": ["https://www.kortho-chain.com"], "faucets": [], "infoURL": "https://www.kortho.io/"}, {"name": "TechPay Mainnet", "chainId": 2569, "shortName": "tpc", "networkId": 2569, "nativeCurrency": {"name": "TechPay", "symbol": "TPC", "decimals": 18}, "rpc": ["https://api.techpay.io/"], "faucets": [], "infoURL": "https://techpay.io/"}, {"name": "PoCRNet", "chainId": 2606, "shortName": "p<PERSON><PERSON><PERSON>", "networkId": 2606, "nativeCurrency": {"name": "Climate awaReness Coin", "symbol": "CRC", "decimals": 18}, "rpc": ["https://pocrnet.westeurope.cloudapp.azure.com/http", "wss://pocrnet.westeurope.cloudapp.azure.com/ws"], "faucets": [], "infoURL": "https://github.com/ethereum-pocr/pocrnet"}, {"name": "Redlight Chain Mainnet", "chainId": 2611, "shortName": "REDLC", "networkId": 2611, "nativeCurrency": {"name": "Redlight Coin", "symbol": "REDLC", "decimals": 18}, "rpc": ["https://dataseed2.redlightscan.finance"], "faucets": [], "infoURL": "https://redlight.finance/"}, {"name": "EZChain C-Chain Mainnet", "chainId": 2612, "shortName": "EZ<PERSON>hain", "networkId": 2612, "nativeCurrency": {"name": "EZ<PERSON>hain", "symbol": "EZC", "decimals": 18}, "rpc": ["https://api.ezchain.com/ext/bc/C/rpc"], "faucets": [], "infoURL": "https://ezchain.com"}, {"name": "EZChain C-Chain Testnet", "chainId": 2613, "shortName": "Fuji-EZChain", "networkId": 2613, "nativeCurrency": {"name": "EZ<PERSON>hain", "symbol": "EZC", "decimals": 18}, "rpc": ["https://testnet-api.ezchain.com/ext/bc/C/rpc"], "faucets": ["https://testnet-faucet.ezchain.com"], "infoURL": "https://ezchain.com"}, {"name": "Whitechain Testnet", "chainId": 2625, "shortName": "twbt", "networkId": 2625, "nativeCurrency": {"name": "WhiteBIT Coin", "symbol": "WBT", "decimals": 18}, "rpc": ["https://rpc-testnet.whitechain.io"], "faucets": ["https://testnet.whitechain.io/faucet"], "infoURL": "https://whitechain.io"}, {"name": "APEX", "chainId": 2662, "shortName": "apexmainnet", "networkId": 2662, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://apexlayer.xyz/"}, {"name": "Morph Testnet", "chainId": 2710, "shortName": "tmorph", "networkId": 2710, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-testnet.morphl2.io"], "faucets": [], "infoURL": "https://morphl2.io"}, {"name": "K-LAOS", "chainId": 2718, "shortName": "k-laos", "networkId": 2718, "nativeCurrency": {"name": "KLAOS", "symbol": "KLAOS", "decimals": 18}, "rpc": ["https://rpc.klaos.laosfoundation.io", "wss://rpc.klaos.laosfoundation.io"], "faucets": [], "infoURL": "https://www.laosfoundation.io/"}, {"name": "XR Sepolia", "chainId": 2730, "shortName": "txr", "networkId": 2730, "nativeCurrency": {"name": "tXR", "symbol": "tXR", "decimals": 18}, "rpc": ["https://xr-sepolia-testnet.rpc.caldera.xyz/http"], "faucets": [], "infoURL": "https://xr-one.gitbook.io"}, {"name": "<PERSON>", "chainId": 2731, "shortName": "TIME", "networkId": 2731, "nativeCurrency": {"name": "TIME", "symbol": "TIME", "decimals": 18}, "rpc": ["https://testnet-rpc.timenetwork.io"], "faucets": [], "infoURL": "https://whitepaper.anttime.net/overview/anttime"}, {"name": "<PERSON><PERSON>", "chainId": 2748, "shortName": "<PERSON><PERSON>", "networkId": 2748, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.nanon.network"], "faucets": [], "infoURL": "https://www.nanon.network"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 2810, "shortName": "hmorph", "networkId": 2810, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-holesky.morphl2.io"], "faucets": [], "infoURL": "https://morphl2.io"}, {"name": "Boba Network Goerli Testnet", "chainId": 2888, "shortName": "<PERSON><PERSON>", "networkId": 2888, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://goerli.boba.network/", "wss://wss.goerli.boba.network/"], "faucets": [], "infoURL": "https://boba.network"}, {"name": "Elux Chain", "chainId": 2907, "shortName": "ELUX", "networkId": 2907, "nativeCurrency": {"name": "Elux Chain", "symbol": "ELUX", "decimals": 18}, "rpc": ["https://rpc.eluxscan.com"], "faucets": [], "infoURL": "https://eluxscan.com"}, {"name": "HYCHAIN", "chainId": 2911, "shortName": "hychain", "networkId": 2911, "nativeCurrency": {"name": "TOPIA", "symbol": "TOPIA", "decimals": 18}, "rpc": ["https://rpc.hychain.com/http"], "faucets": [], "infoURL": "https://www.hychain.com"}, {"name": "Xenon Chain Testnet", "chainId": 2941, "shortName": "xenon", "networkId": 2941, "nativeCurrency": {"name": "Xenon Testnet", "symbol": "tXEN", "decimals": 18}, "rpc": ["https://testnet-chain.xenonchain.com/", "https://testnet-dev.xenonchain.com/"], "faucets": ["https://xfaucet.xenonchain.com"], "infoURL": "https://xenonchain.com"}, {"name": "BitYuan Mainnet", "chainId": 2999, "shortName": "bty", "networkId": 2999, "nativeCurrency": {"name": "BTY", "symbol": "BTY", "decimals": 18}, "rpc": ["https://mainnet.bityuan.com/eth"], "faucets": [], "infoURL": "https://www.bityuan.com"}, {"name": "CENNZnet Rata", "chainId": 3000, "shortName": "cennz-r", "networkId": 3000, "nativeCurrency": {"name": "CPAY", "symbol": "CPAY", "decimals": 18}, "rpc": [], "faucets": ["https://app-faucet.centrality.me"], "infoURL": "https://cennz.net"}, {"name": "CENNZnet Nikau", "chainId": 3001, "shortName": "cennz-n", "networkId": 3001, "nativeCurrency": {"name": "CPAY", "symbol": "CPAY", "decimals": 18}, "rpc": ["https://nikau.centrality.me/public"], "faucets": ["https://app-faucet.centrality.me"], "infoURL": "https://cennz.net"}, {"name": "Canxium Mainnet", "chainId": 3003, "shortName": "cau", "networkId": 3003, "nativeCurrency": {"name": "Canxium", "symbol": "CAU", "decimals": 18}, "rpc": ["https://rpc.canxium.org"], "faucets": [], "infoURL": "https://canxium.org"}, {"name": "PLAYA3ULL GAMES", "chainId": 3011, "shortName": "3ULL", "networkId": 3011, "nativeCurrency": {"name": "3ULL", "symbol": "3ULL", "decimals": 18}, "rpc": ["https://api.mainnet.playa3ull.games"], "faucets": [], "infoURL": "https://playa3ull.games"}, {"name": "Orlando Chain", "chainId": 3031, "shortName": "ORL", "networkId": 3031, "nativeCurrency": {"name": "Orlando", "symbol": "ORL", "decimals": 18}, "rpc": ["https://rpc-testnet.orlchain.com"], "faucets": [], "infoURL": "https://orlchain.com"}, {"name": "Rebus Testnet", "chainId": 3033, "shortName": "rebus-testnet", "networkId": 3033, "nativeCurrency": {"name": "Rebus", "symbol": "REBUS", "decimals": 18}, "rpc": ["https://testnet.rebus.money/rpc"], "faucets": [], "infoURL": "https://www.rebuschain.com"}, {"name": "Bifrost Mainnet", "chainId": 3068, "shortName": "bfc", "networkId": 3068, "nativeCurrency": {"name": "Bifrost", "symbol": "BFC", "decimals": 18}, "rpc": ["https://public-01.mainnet.bifrostnetwork.com/rpc", "https://public-02.mainnet.bifrostnetwork.com/rpc"], "faucets": [], "infoURL": "https://bifrostnetwork.com"}, {"name": "Movement EVM", "chainId": 3073, "shortName": "move", "networkId": 3073, "nativeCurrency": {"name": "Move", "symbol": "MOVE", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://movementlabs.xyz"}, {"name": "Immu3 EVM", "chainId": 3100, "shortName": "Immu3", "networkId": 3100, "nativeCurrency": {"name": "IMMU", "symbol": "IMMU", "decimals": 18}, "rpc": ["https://fraa-flashbox-2800-rpc.a.stagenet.tanssi.network", "wss://fraa-flashbox-2800-rpc.a.stagenet.tanssi.network"], "faucets": [], "infoURL": "https://immu3.io"}, {"name": "Vulture EVM Beta", "chainId": 3102, "shortName": "VFI", "networkId": 3102, "nativeCurrency": {"name": "VFI", "symbol": "VFI", "decimals": 18}, "rpc": ["https://fraa-dancebox-3050-rpc.a.dancebox.tanssi.network", "wss://fraa-dancebox-3050-rpc.a.dancebox.tanssi.network"], "faucets": [], "infoURL": "https://vulture.finance"}, {"name": "SatoshiVM Alpha Mainnet", "chainId": 3109, "shortName": "SAVM", "networkId": 3109, "nativeCurrency": {"name": "BTC", "symbol": "BTC", "decimals": 18}, "rpc": ["https://alpha-rpc-node-http.svmscan.io"], "faucets": [], "infoURL": "https://www.satoshivm.io/"}, {"name": "SatoshiVM Testnet", "chainId": 3110, "shortName": "tSAVM", "networkId": 3110, "nativeCurrency": {"name": "BTC", "symbol": "BTC", "decimals": 18}, "rpc": ["https://test-rpc-node-http.svmscan.io"], "faucets": [], "infoURL": "https://www.satoshivm.io/"}, {"name": "Filecoin - Hyperspace testnet", "chainId": 3141, "shortName": "filecoin-hyperspace", "networkId": 3141, "nativeCurrency": {"name": "testnet filecoin", "symbol": "tFIL", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://filecoin.io"}, {"name": "Dubxcoin network", "chainId": 3269, "shortName": "dubx", "networkId": 3269, "nativeCurrency": {"name": "Dubxcoin mainnet", "symbol": "DUBX", "decimals": 18}, "rpc": ["https://rpcmain.arabianchain.org"], "faucets": [], "infoURL": "https://arabianchain.org"}, {"name": "Dubxcoin testnet", "chainId": 3270, "shortName": "testdubx", "networkId": 3270, "nativeCurrency": {"name": "Dubxcoin testnet", "symbol": "TDUBX", "decimals": 18}, "rpc": ["https://rpctestnet.arabianchain.org"], "faucets": ["https://faucet.arabianchain.org/"], "infoURL": "https://arabianchain.org"}, {"name": "Debounce Subnet Testnet", "chainId": 3306, "shortName": "debounce-devnet", "networkId": 3306, "nativeCurrency": {"name": "Debounce Network", "symbol": "DB", "decimals": 18}, "rpc": ["https://dev-rpc.debounce.network"], "faucets": [], "infoURL": "https://debounce.network"}, {"name": "ZCore Testnet", "chainId": 3331, "shortName": "zcrbeach", "networkId": 3331, "nativeCurrency": {"name": "ZCore", "symbol": "ZCR", "decimals": 18}, "rpc": ["https://rpc-testnet.zcore.cash"], "faucets": ["https://faucet.zcore.cash"], "infoURL": "https://zcore.cash"}, {"name": "EthStorage Testnet", "chainId": 3333, "shortName": "es-t", "networkId": 3333, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["http://testnet.ethstorage.io:9540"], "faucets": [], "infoURL": "https://ethstorage.io/"}, {"name": "Web3Q Galileo", "chainId": 3334, "shortName": "w3q-g", "networkId": 3334, "nativeCurrency": {"name": "Web3Q", "symbol": "W3Q", "decimals": 18}, "rpc": ["https://galileo.web3q.io:8545"], "faucets": [], "infoURL": "https://galileo.web3q.io/home.w3q/"}, {"name": "Paribu Net Mainnet", "chainId": 3400, "shortName": "prb", "networkId": 3400, "nativeCurrency": {"name": "PRB", "symbol": "PRB", "decimals": 18}, "rpc": ["https://rpc.paribu.network"], "faucets": [], "infoURL": "https://net.paribu.com"}, {"name": "EVOLVE Mainnet", "chainId": 3424, "shortName": "EVOm", "networkId": 3424, "nativeCurrency": {"name": "Evolve", "symbol": "EVO", "decimals": 18}, "rpc": ["https://rpc.evolveblockchain.io"], "faucets": [], "infoURL": "https://evolveblockchain.io"}, {"name": "SecureChain Testnet", "chainId": 3434, "shortName": "SCAIt", "networkId": 3434, "nativeCurrency": {"name": "SCAI", "symbol": "SCAI", "decimals": 18}, "rpc": ["https://testnet-rpc.securechain.ai"], "faucets": ["https://faucet.securechain.ai"], "infoURL": "https://securechain.ai"}, {"name": "Paribu Net Testnet", "chainId": 3500, "shortName": "prbtestnet", "networkId": 3500, "nativeCurrency": {"name": "PRB", "symbol": "PRB", "decimals": 18}, "rpc": ["https://rpc.testnet.paribuscan.com"], "faucets": ["https://faucet.paribuscan.com"], "infoURL": "https://net.paribu.com"}, {"name": "JFIN Chain", "chainId": 3501, "shortName": "JFIN", "networkId": 3501, "nativeCurrency": {"name": "JFIN Coin", "symbol": "JFIN", "decimals": 18}, "rpc": ["https://rpc.jfinchain.com"], "faucets": [], "infoURL": "https://jfinchain.com"}, {"name": "PandoProject Mainnet", "chainId": 3601, "shortName": "pando-mainnet", "networkId": 3601, "nativeCurrency": {"name": "pando-token", "symbol": "PTX", "decimals": 18}, "rpc": ["https://eth-rpc-api.pandoproject.org/rpc"], "faucets": [], "infoURL": "https://www.pandoproject.org/"}, {"name": "PandoProject Testnet", "chainId": 3602, "shortName": "pando-testnet", "networkId": 3602, "nativeCurrency": {"name": "pando-token", "symbol": "PTX", "decimals": 18}, "rpc": ["https://testnet.ethrpc.pandoproject.org/rpc"], "faucets": [], "infoURL": "https://www.pandoproject.org/"}, {"name": "Botanix Testnet", "chainId": 3636, "shortName": "BTNX", "networkId": 3636, "nativeCurrency": {"name": "Botanix", "symbol": "BTC", "decimals": 18}, "rpc": ["https://node.botanixlabs.dev"], "faucets": ["https://faucet.botanixlabs.dev"], "infoURL": "https://botanixlabs.xyz"}, {"name": "Botanix Mainnet", "chainId": 3637, "shortName": "BTCm", "networkId": 3637, "nativeCurrency": {"name": "Botanix", "symbol": "BTC", "decimals": 18}, "rpc": ["https://rpc.btxtestchain.com"], "faucets": ["https://faucet.btxtestchain.com"], "infoURL": "https://btxtestchain.com"}, {"name": "iChain Network", "chainId": 3639, "shortName": "ISLAMI", "networkId": 3639, "nativeCurrency": {"name": "ISLAMICOIN", "symbol": "ISLAMI", "decimals": 18}, "rpc": ["https://rpc.ichainscan.com"], "faucets": [], "infoURL": "https://islamicoin.finance"}, {"name": "Jouleverse Mainnet", "chainId": 3666, "shortName": "jouleverse", "networkId": 3666, "nativeCurrency": {"name": "J", "symbol": "J", "decimals": 18}, "rpc": ["https://rpc.jnsdao.com:8503"], "faucets": [], "infoURL": "https://jnsdao.com"}, {"name": "Bittex Mainnet", "chainId": 3690, "shortName": "btx", "networkId": 3690, "nativeCurrency": {"name": "Bittex", "symbol": "BTX", "decimals": 18}, "rpc": ["https://rpc1.bittexscan.info", "https://rpc2.bittexscan.info"], "faucets": [], "infoURL": "https://bittexscan.com"}, {"name": "Empire Network", "chainId": 3693, "shortName": "empire", "networkId": 3693, "nativeCurrency": {"name": "Empire", "symbol": "EMPIRE", "decimals": 18}, "rpc": ["https://rpc.empirenetwork.io"], "faucets": [], "infoURL": "https://www.empirenetwork.io/"}, {"name": "SenjePowers Testnet", "chainId": 3698, "shortName": "SPCt", "networkId": 3698, "nativeCurrency": {"name": "Senje<PERSON><PERSON><PERSON>", "symbol": "SPC", "decimals": 18}, "rpc": ["https://testnet-rpc.senjepowersscan.com"], "faucets": ["https://faucet.senjepowersscan.com"], "infoURL": "https://senjepowersscan.com"}, {"name": "SenjePowers Mainnet", "chainId": 3699, "shortName": "SPCm", "networkId": 3699, "nativeCurrency": {"name": "Senje<PERSON><PERSON><PERSON>", "symbol": "SPC", "decimals": 18}, "rpc": ["https://rpc.senjepowersscan.com"], "faucets": ["https://faucet.senjepowersscan.com"], "infoURL": "https://senjepowersscan.com"}, {"name": "Xpla Testnet", "chainId": 3701, "shortName": "xplatest", "networkId": 3701, "nativeCurrency": {"name": "XPLA", "symbol": "XPLA", "decimals": 18}, "rpc": ["https://dimension-rpc.xpla.dev"], "faucets": ["https://faucet.xpla.io"], "infoURL": "https://xpla.io"}, {"name": "<PERSON><PERSON>", "chainId": 3737, "shortName": "csb", "networkId": 3737, "nativeCurrency": {"name": "Crossbell Token", "symbol": "CSB", "decimals": 18}, "rpc": ["https://rpc.crossbell.io"], "faucets": ["https://faucet.crossbell.io"], "infoURL": "https://crossbell.io"}, {"name": "Astar zkEVM", "chainId": 3776, "shortName": "astrzk", "networkId": 3776, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.startale.com/astar-zkevm"], "faucets": [], "infoURL": "https://astar.network"}, {"name": "AlveyChain <PERSON>", "chainId": 3797, "shortName": "alv", "networkId": 3797, "nativeCurrency": {"name": "Alvey<PERSON><PERSON>n", "symbol": "ALV", "decimals": 18}, "rpc": ["https://elves-core1.alvey.io", "https://elves-core2.alvey.io", "https://elves-core3.alvey.io"], "faucets": [], "infoURL": "https://alveychain.com/"}, {"name": "Tangle Testnet", "chainId": 3799, "shortName": "tTangle", "networkId": 3799, "nativeCurrency": {"name": "Testnet Tangle Network Token", "symbol": "tTNT", "decimals": 18}, "rpc": ["https://testnet-rpc.tangle.tools", "https://testnet-rpc-archive.tangle.tools", "wss://testnet-rpc.tangle.tools", "wss://testnet-rpc-archive.tangle.tools"], "faucets": ["https://faucet.tangle.tools"], "infoURL": "https://docs.tangle.tools"}, {"name": "Firechain zkEVM Ghostrider", "chainId": 3885, "shortName": "firechain-zkEVM-testnet", "networkId": 3885, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-zkevm-ghostrider.thefirechain.com"], "faucets": ["zkevm-faucet.thefirechain.com"], "infoURL": "https://docs.thefirechain.com/"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 3888, "shortName": "kalymainnet", "networkId": 3888, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "KLC", "decimals": 18}, "rpc": ["https://rpc.kalychain.io/rpc"], "faucets": [], "infoURL": "https://kalychain.io"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 3889, "shortName": "ka<PERSON><PERSON><PERSON>", "networkId": 3889, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "KLC", "decimals": 18}, "rpc": ["https://testnetrpc.kalychain.io/rpc"], "faucets": [], "infoURL": "https://kalychain.io"}, {"name": "DRAC Network", "chainId": 3912, "shortName": "drac", "networkId": 3912, "nativeCurrency": {"name": "DRAC", "symbol": "DRAC", "decimals": 18}, "rpc": ["https://www.dracscan.com/rpc"], "faucets": ["https://www.dracscan.io/faucet"], "infoURL": "https://drac.io/"}, {"name": "DOS Tesnet", "chainId": 3939, "shortName": "dost", "networkId": 3939, "nativeCurrency": {"name": "DOS", "symbol": "DOS", "decimals": 18}, "rpc": ["https://test.doschain.com"], "faucets": [], "infoURL": "http://doschain.io/"}, {"name": "DYNO Mainnet", "chainId": 3966, "shortName": "dyno", "networkId": 3966, "nativeCurrency": {"name": "DYNO Token", "symbol": "DYNO", "decimals": 18}, "rpc": ["https://api.dynoprotocol.com"], "faucets": ["https://faucet.dynoscan.io"], "infoURL": "https://dynoprotocol.com"}, {"name": "DYNO Testnet", "chainId": 3967, "shortName": "tdyno", "networkId": 3967, "nativeCurrency": {"name": "DYNO Token", "symbol": "tDYNO", "decimals": 18}, "rpc": ["https://tapi.dynoprotocol.com"], "faucets": ["https://faucet.dynoscan.io"], "infoURL": "https://dynoprotocol.com"}, {"name": "APEX Testnet", "chainId": 3993, "shortName": "apexsep", "networkId": 3993, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-testnet.apexlayer.xyz"], "faucets": ["https://sepoliafaucet.com/"], "infoURL": "https://docs.apexlayer.xyz/"}, {"name": "YuanChain <PERSON>net", "chainId": 3999, "shortName": "ycc", "networkId": 3999, "nativeCurrency": {"name": "YCC", "symbol": "YCC", "decimals": 18}, "rpc": ["https://mainnet.yuan.org/eth"], "faucets": [], "infoURL": "https://www.yuan.org"}, {"name": "Ozone Chain Mainnet", "chainId": 4000, "shortName": "ozo", "networkId": 4000, "nativeCurrency": {"name": "OZONE", "symbol": "OZO", "decimals": 18}, "rpc": ["https://node1.ozonechain.io"], "faucets": [], "infoURL": "https://ozonechain.io"}, {"name": "Peperium Chain Testnet", "chainId": 4001, "shortName": "PERIUM", "networkId": 4001, "nativeCurrency": {"name": "Peperium Chain Testnet", "symbol": "PERIUM", "decimals": 18}, "rpc": ["https://rpc-testnet.peperium.io"], "faucets": [], "infoURL": "https://peperium.io"}, {"name": "Fantom Testnet", "chainId": 4002, "shortName": "tftm", "networkId": 4002, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "FTM", "decimals": 18}, "rpc": ["https://rpc.testnet.fantom.network", "https://fantom-testnet-rpc.publicnode.com", "wss://fantom-testnet-rpc.publicnode.com", "https://fantom-testnet.drpc.org", "wss://fantom-testnet.drpc.org"], "faucets": ["https://faucet.fantom.network"], "infoURL": "https://docs.fantom.foundation/quick-start/short-guide#fantom-testnet"}, {"name": "X1 Fastnet", "chainId": 4003, "shortName": "x1-fastnet", "networkId": 4003, "nativeCurrency": {"name": "XN", "symbol": "XN", "decimals": 18}, "rpc": ["https://x1-fastnet.xen.network"], "faucets": [], "infoURL": "https://docs.xen.network/go-x1/"}, {"name": "GAN Testnet", "chainId": 4048, "shortName": "GANTestnet", "networkId": 4048, "nativeCurrency": {"name": "GP Token", "symbol": "GP", "decimals": 18}, "rpc": ["https://rpc.gpu.net"], "faucets": [], "infoURL": "https://docs.gpu.net/"}, {"name": "Bobaopera Testnet", "chainId": 4051, "shortName": "BobaoperaTestnet", "networkId": 4051, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "BOBA", "decimals": 18}, "rpc": ["https://testnet.bobaopera.boba.network", "wss://wss.testnet.bobaopera.boba.network", "https://replica.testnet.bobaopera.boba.network", "wss://replica-wss.testnet.bobaopera.boba.network"], "faucets": [], "infoURL": "https://boba.network"}, {"name": "Nahmii 3 Mainnet", "chainId": 4061, "shortName": "Nahmii3Mainnet", "networkId": 4061, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://nahmii.io"}, {"name": "Nahmii 3 Testnet", "chainId": 4062, "shortName": "Nahmii3Testnet", "networkId": 4062, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://ngeth.testnet.n3.nahmii.io"], "faucets": [], "infoURL": "https://nahmii.io"}, {"name": "<PERSON><PERSON>", "chainId": 4078, "shortName": "muster", "networkId": 4078, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://muster.alt.technology"], "faucets": [], "infoURL": ""}, {"name": "Fastex Chain (Bahamut) Oasis Testnet", "chainId": 4090, "shortName": "Oasis", "networkId": 4090, "nativeCurrency": {"name": "FTN", "symbol": "FTN", "decimals": 18}, "rpc": ["https://rpc1.oasis.bahamutchain.com"], "faucets": ["https://faucet.oasis.fastexchain.com"], "infoURL": "https://fastexchain.com"}, {"name": "Bitindi Testnet", "chainId": 4096, "shortName": "BNIt", "networkId": 4096, "nativeCurrency": {"name": "BNI", "symbol": "$BNI", "decimals": 18}, "rpc": ["https://testnet-rpc.bitindi.org"], "faucets": ["https://faucet.bitindi.org"], "infoURL": "https://bitindi.org"}, {"name": "Bitindi Mainnet", "chainId": 4099, "shortName": "BNIm", "networkId": 4099, "nativeCurrency": {"name": "BNI", "symbol": "$BNI", "decimals": 18}, "rpc": ["https://mainnet-rpc.bitindi.org"], "faucets": ["https://faucet.bitindi.org"], "infoURL": "https://bitindi.org"}, {"name": "AIOZ Network Testnet", "chainId": 4102, "shortName": "aioz-testnet", "networkId": 4102, "nativeCurrency": {"name": "testAIOZ", "symbol": "AIOZ", "decimals": 18}, "rpc": ["https://eth-ds.testnet.aioz.network"], "faucets": [], "infoURL": "https://aioz.network"}, {"name": "Humans.ai Testnet", "chainId": 4139, "shortName": "humans_testnet", "networkId": 4139, "nativeCurrency": {"name": "HEART", "symbol": "HEART", "decimals": 18}, "rpc": ["https://evm-rpc.testnet.humans.zone"], "faucets": [], "infoURL": "https://humans.ai"}, {"name": "Tipboxcoin Testnet", "chainId": 4141, "shortName": "TPBXt", "networkId": 4141, "nativeCurrency": {"name": "Tipboxcoin", "symbol": "TPBX", "decimals": 18}, "rpc": ["https://testnet-rpc.tipboxcoin.net"], "faucets": ["https://faucet.tipboxcoin.net"], "infoURL": "https://tipboxcoin.net"}, {"name": "CrossFi Testnet", "chainId": 4157, "shortName": "crossfi-testnet", "networkId": 4157, "nativeCurrency": {"name": "XFI", "symbol": "XFI", "decimals": 18}, "rpc": ["https://rpc.testnet.ms"], "faucets": [], "infoURL": "https://crossfi.org/"}, {"name": "PHI Network V1", "chainId": 4181, "shortName": "PHIv1", "networkId": 4181, "nativeCurrency": {"name": "PHI", "symbol": "Φ", "decimals": 18}, "rpc": ["https://rpc1.phi.network", "https://rpc2.phi.network"], "faucets": [], "infoURL": "https://phi.network"}, {"name": "Merlin Mainnet", "chainId": 4200, "shortName": "Merlin-Mainnet", "networkId": 4200, "nativeCurrency": {"name": "BTC", "symbol": "BTC", "decimals": 18}, "rpc": ["https://rpc.merlinchain.io", "https://merlin-mainnet-enterprise.unifra.io"], "faucets": [], "infoURL": "https://merlinchain.io"}, {"name": "LUKSO Testnet", "chainId": 4201, "shortName": "lukso-testnet", "networkId": 4201, "nativeCurrency": {"name": "TestLYX", "symbol": "LYXt", "decimals": 18}, "rpc": ["https://rpc.testnet.lukso.network", "wss://ws-rpc.testnet.lukso.network"], "faucets": ["https://faucet.testnet.lukso.network"], "infoURL": "https://lukso.network"}, {"name": "Lisk Sepolia Testnet", "chainId": 4202, "shortName": "lisksep", "networkId": 4202, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.sepolia-api.lisk.com"], "faucets": [], "infoURL": "https://lisk.com"}, {"name": "Nexi Mainnet", "chainId": 4242, "shortName": "nexi", "networkId": 4242, "nativeCurrency": {"name": "Nexi", "symbol": "NEXI", "decimals": 18}, "rpc": ["https://rpc.chain.nexi.technology/", "https://chain.nexilix.com", "https://chain.nexi.evmnode.online"], "faucets": [], "infoURL": "https://www.nexi.technology/"}, {"name": "Nexi V2 Mainnet", "chainId": 4243, "shortName": "NexiV2", "networkId": 4243, "nativeCurrency": {"name": "NexiV2", "symbol": "NEXI", "decimals": 18}, "rpc": ["https://chain.nexiv2.nexilix.com", "https://rpc.chainv1.nexi.technology"], "faucets": [], "infoURL": "https://www.nexi.technology/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 4328, "shortName": "BobaFujiTestnet", "networkId": 4328, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "BOBA", "decimals": 18}, "rpc": ["https://testnet.avax.boba.network", "wss://wss.testnet.avax.boba.network", "https://replica.testnet.avax.boba.network"], "faucets": [], "infoURL": "https://boba.network"}, {"name": "<PERSON><PERSON>", "chainId": 4337, "shortName": "beam", "networkId": 4337, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "BEAM", "decimals": 18}, "rpc": ["https://build.onbeam.com/rpc", "wss://build.onbeam.com/ws", "https://subnets.avax.network/beam/mainnet/rpc", "wss://subnets.avax.network/beam/mainnet/ws"], "faucets": ["https://faucet.onbeam.com"], "infoURL": "https://www.onbeam.com"}, {"name": "Credit Smart Chain Mainnet", "chainId": 4400, "shortName": "CreditEdge", "networkId": 4400, "nativeCurrency": {"name": "Credit", "symbol": "CREDIT", "decimals": 18}, "rpc": ["https://rpc.creditsmartchain.com"], "faucets": [], "infoURL": "https://creditsmartchain.com"}, {"name": "Htmlcoin Mainnet", "chainId": 4444, "shortName": "html", "networkId": 4444, "nativeCurrency": {"name": "Htmlcoin", "symbol": "HTML", "decimals": 8}, "rpc": ["https://janus.htmlcoin.com/api/"], "faucets": ["https://gruvin.me/htmlcoin"], "infoURL": "https://htmlcoin.com"}, {"name": "Orderly Sepolia <PERSON>net", "chainId": 4460, "shortName": "orderlyl2", "networkId": 4460, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://l2-orderly-l2-4460-sepolia-8tc3sd7dvy.t.conduit.xyz"], "faucets": [], "infoURL": "www.orderly.network"}, {"name": "Emoney Network Testnet", "chainId": 4544, "shortName": "em<PERSON>", "networkId": 4544, "nativeCurrency": {"name": "Emoney Network", "symbol": "EMYC", "decimals": 18}, "rpc": ["https://testnet.emoney.network/"], "faucets": ["https://faucet.emoney.network/faucet"], "infoURL": "https://emoney.network/"}, {"name": "VERY Mainnet", "chainId": 4613, "shortName": "very", "networkId": 4613, "nativeCurrency": {"name": "VERY", "symbol": "VERY", "decimals": 18}, "rpc": ["https://rpc.verylabs.io"], "faucets": [], "infoURL": "https://www.verylabs.io/"}, {"name": "Gold Chain", "chainId": 4653, "shortName": "gold", "networkId": 4653, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://chain-rpc.gold.dev"], "faucets": [], "infoURL": "https://gold.dev"}, {"name": "IoTeX Network Mainnet", "chainId": 4689, "shortName": "iotex-mainnet", "networkId": 4689, "nativeCurrency": {"name": "IoTeX", "symbol": "IOTX", "decimals": 18}, "rpc": ["https://babel-api.mainnet.iotex.io"], "faucets": [], "infoURL": "https://iotex.io"}, {"name": "IoTeX Network Testnet", "chainId": 4690, "shortName": "iotex-testnet", "networkId": 4690, "nativeCurrency": {"name": "IoTeX", "symbol": "IOTX", "decimals": 18}, "rpc": ["https://babel-api.testnet.iotex.io"], "faucets": ["https://faucet.iotex.io/"], "infoURL": "https://iotex.io"}, {"name": "MEVerse Chain Testnet", "chainId": 4759, "shortName": "TESTMEV", "networkId": 4759, "nativeCurrency": {"name": "MEVerse", "symbol": "MEV", "decimals": 18}, "rpc": ["https://rpc.meversetestnet.io"], "faucets": [], "infoURL": "https://www.meverse.sg"}, {"name": "BlackFort Exchange Network Testnet", "chainId": 4777, "shortName": "TBXN", "networkId": 4777, "nativeCurrency": {"name": "BlackFort Testnet Token", "symbol": "TBXN", "decimals": 18}, "rpc": ["https://testnet.blackfort.network/rpc"], "faucets": [], "infoURL": "https://blackfort.exchange"}, {"name": "Globel Chain", "chainId": 4893, "shortName": "GC", "networkId": 4893, "nativeCurrency": {"name": "Globel Chain", "symbol": "GC", "decimals": 18}, "rpc": ["https://rpc.gcscan.io"], "faucets": [], "infoURL": "https://gcscan.io"}, {"name": "Venidium Testnet", "chainId": 4918, "shortName": "txvm", "networkId": 4918, "nativeCurrency": {"name": "Venidium", "symbol": "XVM", "decimals": 18}, "rpc": ["https://rpc-evm-testnet.venidium.io"], "faucets": [], "infoURL": "https://venidium.io"}, {"name": "Venidium Mainnet", "chainId": 4919, "shortName": "xvm", "networkId": 4919, "nativeCurrency": {"name": "Venidium", "symbol": "XVM", "decimals": 18}, "rpc": ["https://rpc.venidium.io"], "faucets": [], "infoURL": "https://venidium.io"}, {"name": "BlackFort Exchange Network", "chainId": 4999, "shortName": "BXN", "networkId": 4999, "nativeCurrency": {"name": "BlackFort <PERSON>", "symbol": "BXN", "decimals": 18}, "rpc": ["https://mainnet.blackfort.network/rpc", "https://mainnet-1.blackfort.network/rpc", "https://mainnet-2.blackfort.network/rpc", "https://mainnet-3.blackfort.network/rpc"], "faucets": [], "infoURL": "https://blackfort.exchange"}, {"name": "Mantle", "chainId": 5000, "shortName": "mantle", "networkId": 5000, "nativeCurrency": {"name": "Mantle", "symbol": "MNT", "decimals": 18}, "rpc": ["https://rpc.mantle.xyz", "https://mantle-rpc.publicnode.com", "wss://mantle-rpc.publicnode.com"], "faucets": [], "infoURL": "https://mantle.xyz"}, {"name": "Mantle Testnet", "chainId": 5001, "shortName": "mantle-testnet", "networkId": 5001, "nativeCurrency": {"name": "Testnet Mantle", "symbol": "MNT", "decimals": 18}, "rpc": ["https://rpc.testnet.mantle.xyz"], "faucets": ["https://faucet.testnet.mantle.xyz"], "infoURL": "https://mantle.xyz"}, {"name": "Treasurenet Mainnet Alpha", "chainId": 5002, "shortName": "treasurenet", "networkId": 5002, "nativeCurrency": {"name": "UNIT", "symbol": "UNIT", "decimals": 18}, "rpc": ["https://node0.treasurenet.io", "https://node1.treasurenet.io", "https://node2.treasurenet.io", "https://node3.treasurenet.io"], "faucets": [], "infoURL": "https://www.treasurenet.io"}, {"name": "Mantle Sepolia Testnet", "chainId": 5003, "shortName": "mnt-sep", "networkId": 5003, "nativeCurrency": {"name": "Sepolia Mantle", "symbol": "MNT", "decimals": 18}, "rpc": ["https://rpc.sepolia.mantle.xyz"], "faucets": ["https://faucet.sepolia.mantle.xyz"], "infoURL": "https://mantle.xyz"}, {"name": "Treasurenet Testnet", "chainId": 5005, "shortName": "tntest", "networkId": 5005, "nativeCurrency": {"name": "UNIT", "symbol": "UNIT", "decimals": 18}, "rpc": ["https://node0.testnet.treasurenet.io", "https://node1.testnet.treasurenet.io", "https://node2.testnet.treasurenet.io", "https://node3.testnet.treasurenet.io"], "faucets": [], "infoURL": "https://www.testnet.treasurenet.io"}, {"name": "ONIGIRI Test Subnet", "chainId": 5039, "shortName": "<PERSON><PERSON><PERSON>", "networkId": 5039, "nativeCurrency": {"name": "ONIGIRI", "symbol": "ONGR", "decimals": 18}, "rpc": ["https://subnets.avax.network/onigiri/testnet/rpc"], "faucets": [], "infoURL": "https://www.ongr.org/"}, {"name": "<PERSON><PERSON>", "chainId": 5051, "shortName": "nollie-testnet", "networkId": 5051, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://nollie-rpc.skatechain.org/"], "faucets": [], "infoURL": ""}, {"name": "Syndicate Testnet", "chainId": 5100, "shortName": "syndicate-chain-testnet", "networkId": 5100, "nativeCurrency": {"name": "S-Ether", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-testnet.syndicate.io"], "faucets": [], "infoURL": "https://syndicate.io"}, {"name": "Syndicate Frame Chain", "chainId": 5101, "shortName": "syndicate-chain-frame", "networkId": 5101, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-frame.syndicate.io"], "faucets": [], "infoURL": "https://syndicate.io"}, {"name": "SIC Testnet", "chainId": 5102, "shortName": "sic-testnet", "networkId": 5102, "nativeCurrency": {"name": "ETH", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-sic-testnet-zvr7tlkzsi.t.conduit.xyz"], "faucets": [], "infoURL": "https://www.fwb.help/"}, {"name": "Coordinape Testnet", "chainId": 5103, "shortName": "coordinape-testnet", "networkId": 5103, "nativeCurrency": {"name": "ETH", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-coordinape-testnet-vs9se3oc4v.t.conduit.xyz"], "faucets": [], "infoURL": "https://coordinape.com/"}, {"name": "Charmverse Testnet", "chainId": 5104, "shortName": "charmverse-testnet", "networkId": 5104, "nativeCurrency": {"name": "ETH", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-charmverse-testnet-g6blnaebes.t.conduit.xyz"], "faucets": [], "infoURL": "https://charmverse.io/"}, {"name": "Superloyalty Testnet", "chainId": 5105, "shortName": "superloyalty-testnet", "networkId": 5105, "nativeCurrency": {"name": "ETH", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-superloyalty-testnet-1m5gwjbsv1.t.conduit.xyz"], "faucets": [], "infoURL": "https://www.superloyal.com/"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 5106, "shortName": "azra-testnet", "networkId": 5106, "nativeCurrency": {"name": "ETH", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-azra-testnet-6hz86owb1n.t.conduit.xyz"], "faucets": [], "infoURL": "https://azragames.com"}, {"name": "Bahamut", "chainId": 5165, "shortName": "ftn", "networkId": 5165, "nativeCurrency": {"name": "FTN", "symbol": "FTN", "decimals": 18}, "rpc": ["https://rpc1.bahamut.io", "https://rpc2.bahamut.io", "wss://ws1.sahara.bahamutchain.com", "wss://ws2.sahara.bahamutchain.com", "https://bahamut-rpc.publicnode.com", "wss://bahamut-rpc.publicnode.com"], "faucets": [], "infoURL": "https://bahamut.io"}, {"name": "Smart Layer Network", "chainId": 5169, "shortName": "SLN", "networkId": 5169, "nativeCurrency": {"name": "Service Unit Token", "symbol": "SU", "decimals": 18}, "rpc": ["https://rpc.main.smartlayer.network"], "faucets": [], "infoURL": "https://www.smartlayer.network/"}, {"name": "TLChain Network Mainnet", "chainId": 5177, "shortName": "tlc", "networkId": 5177, "nativeCurrency": {"name": "TLChain Network", "symbol": "TLC", "decimals": 18}, "rpc": ["https://mainnet-rpc.tlxscan.com/"], "faucets": [], "infoURL": "https://tlchain.network/"}, {"name": "EraSwap Mainnet", "chainId": 5197, "shortName": "es", "networkId": 5197, "nativeCurrency": {"name": "EraSwap", "symbol": "ES", "decimals": 18}, "rpc": ["https://mainnet.eraswap.network", "https://rpc-mumbai.mainnet.eraswap.network"], "faucets": [], "infoURL": "https://eraswap.info/"}, {"name": "Humanode Mainnet", "chainId": 5234, "shortName": "hmnd", "networkId": 5234, "nativeCurrency": {"name": "eHMND", "symbol": "eHMND", "decimals": 18}, "rpc": ["https://explorer-rpc-http.mainnet.stages.humanode.io"], "faucets": [], "infoURL": "https://humanode.io"}, {"name": "Firechain Mainnet Old", "chainId": 5290, "shortName": "_old_fire", "networkId": 5290, "nativeCurrency": {"name": "Firechain", "symbol": "FIRE", "decimals": 18}, "rpc": ["https://mainnet.rpc1.thefirechain.com"], "faucets": [], "infoURL": "https://thefirechain.com"}, {"name": "Uzmi Network Mainnet", "chainId": 5315, "shortName": "UZMI", "networkId": 5315, "nativeCurrency": {"name": "UZMI", "symbol": "UZMI", "decimals": 18}, "rpc": ["https://network.uzmigames.com.br/"], "faucets": [], "infoURL": "https://uzmigames.com.br/"}, {"name": "OpTrust Testnet", "chainId": 5317, "shortName": "toptrust", "networkId": 5317, "nativeCurrency": {"name": "TestBSC", "symbol": "tBNB", "decimals": 18}, "rpc": ["https://rpctest.optrust.io"], "faucets": [], "infoURL": "https://optrust.io"}, {"name": "Tritanium Testnet", "chainId": 5353, "shortName": "ttrn", "networkId": 5353, "nativeCurrency": {"name": "Tritanium Native Token", "symbol": "tTRN", "decimals": 18}, "rpc": ["https://nodetestnet-station-one.tritanium.network/", "https://nodetestnet-station-two.tritanium.network/"], "faucets": ["https://faucet.tritanium.network"], "infoURL": "https://tritanium.network"}, {"name": "edeXa Mainnet", "chainId": 5424, "shortName": "edeXa", "networkId": 5424, "nativeCurrency": {"name": "EDEXA", "symbol": "EDX", "decimals": 18}, "rpc": ["https://mainnet.edexa.network/rpc", "https://mainnet.edexa.com/rpc", "https://io-dataseed1.mainnet.edexa.io-market.com/rpc"], "faucets": [], "infoURL": "https://edexa.network/"}, {"name": "Egochain", "chainId": 5439, "shortName": "egax", "networkId": 5439, "nativeCurrency": {"name": "EGAX", "symbol": "EGAX", "decimals": 18}, "rpc": ["https://mainnet.egochain.org"], "faucets": [], "infoURL": "https://docs.egochain.org/"}, {"name": "VEX EVM TESTNET", "chainId": 5522, "shortName": "VEX", "networkId": 5522, "nativeCurrency": {"name": "VEX EVM TESTNET", "symbol": "VEX", "decimals": 18}, "rpc": ["https://testnet.vexascan.com/evmapi"], "faucets": ["https://t.me/vexfaucetbot"], "infoURL": "https://vexanium.com"}, {"name": "<PERSON><PERSON><PERSON>net", "chainId": 5551, "shortName": "<PERSON><PERSON><PERSON>", "networkId": 5551, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://l2.nahmii.io"], "faucets": [], "infoURL": "https://nahmii.io"}, {"name": "<PERSON><PERSON><PERSON>net", "chainId": 5553, "shortName": "NahmiiTestnet", "networkId": 5553, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://l2.testnet.nahmii.io"], "faucets": [], "infoURL": "https://nahmii.io"}, {"name": "Chain Verse Mainnet", "chainId": 5555, "shortName": "cverse", "networkId": 5555, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "OAS", "decimals": 18}, "rpc": ["https://rpc.chainverse.info"], "faucets": [], "infoURL": "https://chainverse.info"}, {"name": "opBNB Testnet", "chainId": 5611, "shortName": "obnbt", "networkId": 5611, "nativeCurrency": {"name": "BNB Chain Native Token", "symbol": "tBNB", "decimals": 18}, "rpc": ["https://opbnb-testnet-rpc.bnbchain.org", "https://opbnb-testnet.nodereal.io/v1/64a9df0874fb4a93b9d0a3849de012d3", "wss://opbnb-testnet.nodereal.io/ws/v1/64a9df0874fb4a93b9d0a3849de012d3", "https://opbnb-testnet.nodereal.io/v1/e9a36765eb8a40b9bd12e680a1fd2bc5", "wss://opbnb-testnet.nodereal.io/ws/v1/e9a36765eb8a40b9bd12e680a1fd2bc5", "https://opbnb-testnet-rpc.publicnode.com", "wss://opbnb-testnet-rpc.publicnode.com"], "faucets": ["https://testnet.bnbchain.org/faucet-smart"], "infoURL": "https://opbnb.bnbchain.org/en"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 5615, "shortName": "arcturus-testnet", "networkId": 5615, "nativeCurrency": {"name": "tARC", "symbol": "tARC", "decimals": 18}, "rpc": ["https://rpc-testnet.arcturuschain.io/"], "faucets": ["https://faucet.arcturuschain.io"], "infoURL": "https://arcturuschain.io"}, {"name": "Arcturus Chain Testnet", "chainId": 5616, "shortName": "ARCT", "networkId": 5616, "nativeCurrency": {"name": "Test Arct", "symbol": "tARCT", "decimals": 18}, "rpc": ["http://185.99.196.3:8545"], "faucets": [], "infoURL": "https://arcturuschain.io"}, {"name": "QIE Blockchain", "chainId": 5656, "shortName": "QIE", "networkId": 5656, "nativeCurrency": {"name": "QIE Blockchain", "symbol": "QIE", "decimals": 18}, "rpc": ["https://rpc-main1.qiblockchain.online/", "https://rpc-main2.qiblockchain.online/"], "faucets": [], "infoURL": "https://qiblockchain.online/"}, {"name": "Filenova Testnet", "chainId": 5675, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "networkId": 5675, "nativeCurrency": {"name": "Test Filecoin", "symbol": "tFIL", "decimals": 18}, "rpc": ["https://rpctest.filenova.org"], "faucets": [], "infoURL": "https://filenova.org"}, {"name": "<PERSON><PERSON>", "chainId": 5678, "shortName": "tango", "networkId": 5678, "nativeCurrency": {"name": "TANGO", "symbol": "TANGO", "decimals": 18}, "rpc": ["https://fraa-dancebox-3001-rpc.a.dancebox.tanssi.network", "wss://fraa-dancebox-3001-rpc.a.dancebox.tanssi.network"], "faucets": [], "infoURL": "https://docs.tanssi.network/builders/tanssi-network/networks/dancebox/demo-evm-containerchain"}, {"name": "Syscoin <PERSON>baum Testnet", "chainId": 5700, "shortName": "tsys", "networkId": 5700, "nativeCurrency": {"name": "Testnet Syscoin", "symbol": "tSYS", "decimals": 18}, "rpc": ["https://rpc.tanenbaum.io", "wss://rpc.tanenbaum.io/wss", "https://syscoin-tanenbaum-evm.publicnode.com", "wss://syscoin-tanenbaum-evm.publicnode.com"], "faucets": ["https://faucet.tanenbaum.io"], "infoURL": "https://syscoin.org"}, {"name": "Hika Network Testnet", "chainId": 5729, "shortName": "hik", "networkId": 5729, "nativeCurrency": {"name": "Hik <PERSON>", "symbol": "HIK", "decimals": 18}, "rpc": ["https://rpc-testnet.hika.network/"], "faucets": [], "infoURL": "https://hika.network/"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>net", "chainId": 5758, "shortName": "satst", "networkId": 5758, "nativeCurrency": {"name": "<PERSON>shi<PERSON>hain Coin", "symbol": "SATS", "decimals": 18}, "rpc": ["https://testnet-rpc.satoshichain.io"], "faucets": ["https://faucet.satoshichain.io"], "infoURL": "https://satoshichain.net"}, {"name": "Gana<PERSON>", "chainId": 5777, "shortName": "ggui", "networkId": 5777, "nativeCurrency": {"name": "Ganache Test Ether", "symbol": "ETH", "decimals": 18}, "rpc": ["https://127.0.0.1:7545"], "faucets": [], "infoURL": "https://trufflesuite.com/ganache/"}, {"name": "<PERSON><PERSON>", "chainId": 5845, "shortName": "tangle", "networkId": 5845, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "TNT", "decimals": 18}, "rpc": ["https://rpc.tangle.tools", "wss://rpc.tangle.tools"], "faucets": [], "infoURL": "https://docs.tangle.tools"}, {"name": "Ontology Testnet", "chainId": 5851, "shortName": "OntologyTestnet", "networkId": 5851, "nativeCurrency": {"name": "ONG", "symbol": "ONG", "decimals": 18}, "rpc": ["http://polaris1.ont.io:20339", "http://polaris2.ont.io:20339", "http://polaris3.ont.io:20339", "http://polaris4.ont.io:20339", "https://polaris1.ont.io:10339", "https://polaris2.ont.io:10339", "https://polaris3.ont.io:10339", "https://polaris4.ont.io:10339"], "faucets": ["https://developer.ont.io/"], "infoURL": "https://ont.io/"}, {"name": "Wegochain Rubidium Mainnet", "chainId": 5869, "shortName": "rbd", "networkId": 5869, "nativeCurrency": {"name": "Rubid", "symbol": "RBD", "decimals": 18}, "rpc": ["https://proxy.wegochain.io", "http://wallet.wegochain.io:7764"], "faucets": [], "infoURL": "https://www.wegochain.io"}, {"name": "BounceBit Testnet", "chainId": 6000, "shortName": "bouncebit-testnet", "networkId": 6000, "nativeCurrency": {"name": "BounceBit", "symbol": "BB", "decimals": 18}, "rpc": ["https://fullnode-testnet.bouncebitapi.com/"], "faucets": [], "infoURL": "https://bouncebit.io"}, {"name": "BounceBit Mainnet", "chainId": 6001, "shortName": "bouncebit-mainnet", "networkId": 6001, "nativeCurrency": {"name": "BounceBit", "symbol": "BB", "decimals": 18}, "rpc": ["https://fullnode-mainnet.bouncebitapi.com/"], "faucets": [], "infoURL": "https://bouncebit.io"}, {"name": "Tres Testnet", "chainId": 6065, "shortName": "TRESTEST", "networkId": 6065, "nativeCurrency": {"name": "TRES", "symbol": "TRES", "decimals": 18}, "rpc": ["https://rpc-test.tresleches.finance/"], "faucets": ["http://faucet.tresleches.finance:8080"], "infoURL": "https://treschain.com"}, {"name": "Tres Mainnet", "chainId": 6066, "shortName": "TRESMAIN", "networkId": 6066, "nativeCurrency": {"name": "TRES", "symbol": "TRES", "decimals": 18}, "rpc": ["https://rpc.tresleches.finance/", "https://rpc.treschain.io/"], "faucets": [], "infoURL": "https://treschain.com"}, {"name": "Cascadia Testnet", "chainId": 6102, "shortName": "cascadia", "networkId": 6102, "nativeCurrency": {"name": "CC", "symbol": "tCC", "decimals": 18}, "rpc": ["https://testnet.cascadia.foundation"], "faucets": ["https://www.cascadia.foundation/faucet"], "infoURL": "https://www.cascadia.foundation"}, {"name": "UPTN Testnet", "chainId": 6118, "shortName": "UPTN-TEST", "networkId": 6118, "nativeCurrency": {"name": "UPTN", "symbol": "UPTN", "decimals": 18}, "rpc": ["https://node-api.alp.uptn.io/v1/ext/rpc"], "faucets": [], "infoURL": "https://uptn.io"}, {"name": "UPTN", "chainId": 6119, "shortName": "UPTN", "networkId": 6119, "nativeCurrency": {"name": "UPTN", "symbol": "UPTN", "decimals": 18}, "rpc": ["https://node-api.uptn.io/v1/ext/rpc"], "faucets": [], "infoURL": "https://uptn.io"}, {"name": "Aura Euphoria Testnet", "chainId": 6321, "shortName": "eaura", "networkId": 6321, "nativeCurrency": {"name": "test-<PERSON>ura", "symbol": "eAura", "decimals": 18}, "rpc": ["https://jsonrpc.euphoria.aura.network"], "faucets": [], "infoURL": "https://aura.network"}, {"name": "Digit Soul Smart Chain", "chainId": 6363, "shortName": "DGS", "networkId": 6363, "nativeCurrency": {"name": "Digit Coin", "symbol": "DGC", "decimals": 18}, "rpc": ["https://dsc-rpc.digitsoul.co.th"], "faucets": [], "infoURL": ""}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 6502, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "networkId": 6502, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "P2P", "decimals": 18}, "rpc": ["https://peerpay.su.gy/p2p"], "faucets": [], "infoURL": "https://peerpay.su.gy"}, {"name": "Scolcoin WeiC<PERSON>n Testnet", "chainId": 6552, "shortName": "SRC-test", "networkId": 6552, "nativeCurrency": {"name": "Scolcoin", "symbol": "SCOL", "decimals": 18}, "rpc": ["https://testnet-rpc.scolcoin.com"], "faucets": ["https://faucet.scolcoin.com"], "infoURL": "https://scolcoin.com"}, {"name": "Fox Testnet Network", "chainId": 6565, "shortName": "fox", "networkId": 6565, "nativeCurrency": {"name": "FOX Native Token", "symbol": "tFOX", "decimals": 18}, "rpc": ["https://rpc-testnet-v1.foxchain.app/", "https://rpc2-testnet-v1.foxchain.app/", "https://rpc3-testnet-v1.foxchain.app"], "faucets": ["https://faucet.foxchain.app"], "infoURL": "https://foxchain.app"}, {"name": "Pixie Chain Mainnet", "chainId": 6626, "shortName": "pixie-chain", "networkId": 6626, "nativeCurrency": {"name": "Pixie Chain Native Token", "symbol": "PIX", "decimals": 18}, "rpc": ["https://http-mainnet.chain.pixie.xyz", "wss://ws-mainnet.chain.pixie.xyz"], "faucets": [], "infoURL": "https://chain.pixie.xyz"}, {"name": "Latest Chain Testnet", "chainId": 6660, "shortName": "LATESTt", "networkId": 6660, "nativeCurrency": {"name": "Latest", "symbol": "LATEST", "decimals": 18}, "rpc": ["https://testnet-rpc.latestcoin.io"], "faucets": ["http://faucet.latestchain.io"], "infoURL": "https://latestcoin.io"}, {"name": "Cybria Mainnet", "chainId": 6661, "shortName": "cyba", "networkId": 6661, "nativeCurrency": {"name": "Cybria", "symbol": "CYBA", "decimals": 18}, "rpc": ["https://rpc-mainnet.cybria.io"], "faucets": [], "infoURL": "https://cybria.io"}, {"name": "Cybria Testnet", "chainId": 6666, "shortName": "tcyba", "networkId": 6666, "nativeCurrency": {"name": "Cybria", "symbol": "CYBA", "decimals": 18}, "rpc": ["https://l2-rpc.cybascan.io"], "faucets": ["https://faucet.cybascan.io"], "infoURL": "https://cybria.io"}, {"name": "IRIShub", "chainId": 6688, "shortName": "iris", "networkId": 6688, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ERIS", "decimals": 18}, "rpc": ["https://evmrpc.irishub-1.irisnet.org", "https://iris-evm.publicnode.com", "wss://iris-evm.publicnode.com"], "faucets": [], "infoURL": "https://www.irisnet.org"}, {"name": "PAXB Mainnet", "chainId": 6701, "shortName": "PAXB", "networkId": 6701, "nativeCurrency": {"name": "PAXB", "symbol": "PAXB", "decimals": 18}, "rpc": ["https://chain.paxb.io"], "faucets": [], "infoURL": "https://paxb.io/"}, {"name": "Compverse Mainnet", "chainId": 6779, "shortName": "compverse", "networkId": 6779, "nativeCurrency": {"name": "compverse", "symbol": "CPV", "decimals": 18}, "rpc": ["https://rpc.compverse.io/", "https://rpc-useast1.compverse.io/"], "faucets": [], "infoURL": "https://compverse.io"}, {"name": "Gold Smart Chain Mainnet", "chainId": 6789, "shortName": "STANDm", "networkId": 6789, "nativeCurrency": {"name": "Standard in Gold", "symbol": "STAND", "decimals": 18}, "rpc": ["https://rpc-mainnet.goldsmartchain.com"], "faucets": ["https://faucet.goldsmartchain.com"], "infoURL": "https://goldsmartchain.com"}, {"name": "Tomb Chain Mainnet", "chainId": 6969, "shortName": "tombchain", "networkId": 6969, "nativeCurrency": {"name": "Tomb", "symbol": "TOMB", "decimals": 18}, "rpc": ["https://rpc.tombchain.com/"], "faucets": [], "infoURL": "https://tombchain.com/"}, {"name": "PolySmartChain", "chainId": 6999, "shortName": "psc", "networkId": 6999, "nativeCurrency": {"name": "PSC", "symbol": "PSC", "decimals": 18}, "rpc": ["https://seed0.polysmartchain.com/", "https://seed1.polysmartchain.com/", "https://seed2.polysmartchain.com/"], "faucets": [], "infoURL": "https://www.polysmartchain.com/"}, {"name": "ZetaChain Mainnet", "chainId": 7000, "shortName": "zetachain-mainnet", "networkId": 7000, "nativeCurrency": {"name": "Zeta", "symbol": "ZETA", "decimals": 18}, "rpc": ["https://zetachain-evm.blockpi.network/v1/rpc/public", "https://zetachain-mainnet-archive.allthatnode.com:8545"], "faucets": [], "infoURL": "https://zetachain.com/docs/"}, {"name": "ZetaChain Athens 3 Testnet", "chainId": 7001, "shortName": "zetachain-athens", "networkId": 7001, "nativeCurrency": {"name": "Zeta", "symbol": "ZETA", "decimals": 18}, "rpc": ["https://zetachain-athens-evm.blockpi.network/v1/rpc/public", "wss://zetachain-athens.blockpi.network/rpc/v1/public/websocket", "https://zetachain-testnet-archive.allthatnode.com:8545"], "faucets": ["https://labs.zetachain.com/get-zeta"], "infoURL": "https://zetachain.com/docs"}, {"name": "BST Chain", "chainId": 7007, "shortName": "BSTC", "networkId": 7007, "nativeCurrency": {"name": "BST Chain", "symbol": "BSTC", "decimals": 18}, "rpc": ["https://rpc.bstchain.io/"], "faucets": [], "infoURL": "https://bstchain.io"}, {"name": "<PERSON> the heart", "chainId": 7027, "shortName": "ELLA", "networkId": 7027, "nativeCurrency": {"name": "<PERSON>", "symbol": "ELLA", "decimals": 18}, "rpc": ["https://rpc.ella.network"], "faucets": [], "infoURL": "https://ella.network"}, {"name": "Planq Mainnet", "chainId": 7070, "shortName": "planq", "networkId": 7070, "nativeCurrency": {"name": "Planq", "symbol": "PLQ", "decimals": 18}, "rpc": ["https://evm-rpc.planq.network"], "faucets": [], "infoURL": "https://planq.network"}, {"name": "Planq Atlas Testnet", "chainId": 7077, "shortName": "planq-atlas-testnet", "networkId": 7077, "nativeCurrency": {"name": "Planq", "symbol": "tPLQ", "decimals": 18}, "rpc": ["https://evm-rpc-atlas.planq.network"], "faucets": [], "infoURL": "https://planq.network"}, {"name": "Nume", "chainId": 7100, "shortName": "nume", "networkId": 7100, "nativeCurrency": {"name": "Dai Stablecoin", "symbol": "DAI", "decimals": 18}, "rpc": ["https://rpc.numecrypto.com"], "faucets": [], "infoURL": "https://numecrypto.com"}, {"name": "Help The Homeless", "chainId": 7118, "shortName": "hth", "networkId": 7118, "nativeCurrency": {"name": "Help The Homeless Coin", "symbol": "HTH", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://hth.world"}, {"name": "Bitrock Mainnet", "chainId": 7171, "shortName": "bitrock", "networkId": 7171, "nativeCurrency": {"name": "BITROCK", "symbol": "BROCK", "decimals": 18}, "rpc": ["https://connect.bit-rock.io", "https://brockrpc.io"], "faucets": [], "infoURL": "https://bit-rock.io"}, {"name": "KLYNTAR", "chainId": 7331, "shortName": "kly", "networkId": 7331, "nativeCurrency": {"name": "KLYNTAR", "symbol": "KLY", "decimals": 18}, "rpc": ["https://evm.klyntar.org/kly_evm_rpc", "https://evm.klyntarscan.org/kly_evm_rpc"], "faucets": [], "infoURL": "https://klyntar.org"}, {"name": "Horizen EON Mainnet", "chainId": 7332, "shortName": "EON", "networkId": 7332, "nativeCurrency": {"name": "Zencash", "symbol": "ZEN", "decimals": 18}, "rpc": ["https://eon-rpc.horizenlabs.io/ethv1", "https://rpc.ankr.com/horizen_eon"], "faucets": [], "infoURL": "https://horizen.io/"}, {"name": "Shyft Mainnet", "chainId": 7341, "shortName": "shyft", "networkId": 7341, "nativeCurrency": {"name": "Shyft", "symbol": "SHYFT", "decimals": 18}, "rpc": ["https://rpc.shyft.network/"], "faucets": [], "infoURL": "https://shyft.network"}, {"name": "Raba Network Mainnet", "chainId": 7484, "shortName": "raba", "networkId": 7484, "nativeCurrency": {"name": "Ra<PERSON>", "symbol": "RABA", "decimals": 18}, "rpc": ["https://rpc.x.raba.app/", "wss://rpc.x.raba.app/ws/"], "faucets": [], "infoURL": "https://x.raba.app/"}, {"name": "MEVerse Chain Mainnet", "chainId": 7518, "shortName": "MEV", "networkId": 7518, "nativeCurrency": {"name": "MEVerse", "symbol": "MEV", "decimals": 18}, "rpc": ["https://rpc.meversemainnet.io"], "faucets": [], "infoURL": "https://www.meverse.sg"}, {"name": "Cyber Mainnet", "chainId": 7560, "shortName": "cyeth", "networkId": 7560, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://cyber.alt.technology/", "wss://cyber-ws.alt.technology/", "https://rpc.cyber.co/", "wss://rpc.cyber.co/"], "faucets": [], "infoURL": "https://cyber.co/"}, {"name": "ADIL Testnet", "chainId": 7575, "shortName": "tadil", "networkId": 7575, "nativeCurrency": {"name": "Testnet ADIL", "symbol": "ADIL", "decimals": 18}, "rpc": ["https://testnet.adilchain-rpc.io"], "faucets": ["https://testnet-faucet.adil-scan.io"], "infoURL": "https://adilchain.io"}, {"name": "Adil Chain V2 Mainnet", "chainId": 7576, "shortName": "adil", "networkId": 7576, "nativeCurrency": {"name": "ADIL", "symbol": "ADIL", "decimals": 18}, "rpc": ["https://adilchain-rpc.io"], "faucets": [], "infoURL": "https://adilchain.io"}, {"name": "The Root Network - Mainnet", "chainId": 7668, "shortName": "trn-mainnet", "networkId": 7668, "nativeCurrency": {"name": "XRP", "symbol": "XRP", "decimals": 6}, "rpc": ["https://root.rootnet.live/archive", "wss://root.rootnet.live/archive/ws"], "faucets": [], "infoURL": "https://www.futureverse.com/technology/root"}, {"name": "The Root Network - Porcini Testnet", "chainId": 7672, "shortName": "trn-porcini", "networkId": 7672, "nativeCurrency": {"name": "XRP", "symbol": "XRP", "decimals": 6}, "rpc": ["https://porcini.rootnet.app/archive", "wss://porcini.rootnet.app/archive/ws"], "faucets": [], "infoURL": "https://www.futureverse.com/technology/root"}, {"name": "Canto", "chainId": 7700, "shortName": "canto", "networkId": 7700, "nativeCurrency": {"name": "Canto", "symbol": "CANTO", "decimals": 18}, "rpc": ["https://canto.slingshot.finance", "https://canto-rpc.ansybl.io", "https://mainnode.plexnode.org:8545", "https://canto.gravitychain.io/"], "faucets": [], "infoURL": "https://canto.io"}, {"name": "Canto Tesnet", "chainId": 7701, "shortName": "TestnetCanto", "networkId": 7701, "nativeCurrency": {"name": "Testnet Canto", "symbol": "CANTO", "decimals": 18}, "rpc": ["https://testnet-archive.plexnode.wtf"], "faucets": [], "infoURL": "https://canto.io"}, {"name": "Bitrock Testnet", "chainId": 7771, "shortName": "tbitrock", "networkId": 7771, "nativeCurrency": {"name": "BITROCK", "symbol": "BROCK", "decimals": 18}, "rpc": ["https://testnet.bit-rock.io"], "faucets": ["https://faucet.bit-rock.io"], "infoURL": "https://bit-rock.io"}, {"name": "GDCC TESTNET", "chainId": 7775, "shortName": "GDCC", "networkId": 7775, "nativeCurrency": {"name": "GDCC", "symbol": "GDCC", "decimals": 18}, "rpc": ["https://testnet-rpc1.gdccscan.io"], "faucets": [], "infoURL": "https://gdcchain.com"}, {"name": "Rise of the Warbots Testnet", "chainId": 7777, "shortName": "RiseOfTheWarbotsTestnet", "networkId": 7777, "nativeCurrency": {"name": "Nano Machines", "symbol": "NMAC", "decimals": 18}, "rpc": ["https://testnet1.riseofthewarbots.com", "https://testnet2.riseofthewarbots.com", "https://testnet3.riseofthewarbots.com", "https://testnet4.riseofthewarbots.com", "https://testnet5.riseofthewarbots.com"], "faucets": [], "infoURL": "https://riseofthewarbots.com/"}, {"name": "Orenium Mainnet Protocol", "chainId": 7778, "shortName": "ore", "networkId": 7778, "nativeCurrency": {"name": "ORENIUM", "symbol": "ORE", "decimals": 18}, "rpc": ["https://validator-mainnet.orenium.org", "https://rpc-oracle-mainnet.orenium.org", "https://portalmainnet.orenium.org"], "faucets": [], "infoURL": "https://orenium.org"}, {"name": "OpenEX LONG Testnet", "chainId": 7798, "shortName": "oex", "networkId": 7798, "nativeCurrency": {"name": "USDT Testnet", "symbol": "USDT", "decimals": 18}, "rpc": ["https://long.rpc.openex.network/"], "faucets": ["https://long.hub.openex.network/faucet"], "infoURL": "https://openex.network"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 7860, "shortName": "maal-test", "networkId": 7860, "nativeCurrency": {"name": "MAAL", "symbol": "MAAL", "decimals": 18}, "rpc": ["https://node1.maalscan.io/", "https://rpc-bntest.maalscan.io/"], "faucets": ["https://faucet-testnet.maalscan.io/"], "infoURL": "https://www.maalchain.com/"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 7878, "shortName": "tscas", "networkId": 7878, "nativeCurrency": {"name": "Hazlor Test Coin", "symbol": "TSCAS", "decimals": 18}, "rpc": ["https://hatlas.rpc.hazlor.com:8545", "wss://hatlas.rpc.hazlor.com:8546"], "faucets": ["https://faucet.hazlor.com"], "infoURL": "https://hazlor.com"}, {"name": "<PERSON><PERSON>", "chainId": 7887, "shortName": "kintoMain<PERSON>", "networkId": 7887, "nativeCurrency": {"name": "Ethereum", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.kinto.xyz/http", "https://kinto-mainnet.calderachain.xyz/http"], "faucets": [], "infoURL": "https://kinto.xyz"}, {"name": "ARDENIUM Athena", "chainId": 7895, "shortName": "ard", "networkId": 7895, "nativeCurrency": {"name": "ARD", "symbol": "tARD", "decimals": 18}, "rpc": ["https://rpc-athena.ardescan.com/"], "faucets": ["https://faucet-athena.ardescan.com/"], "infoURL": "https://ardenium.org"}, {"name": "Dot Blox", "chainId": 7923, "shortName": "DTBX", "networkId": 7923, "nativeCurrency": {"name": "Dot Blox", "symbol": "DTBX", "decimals": 18}, "rpc": ["https://rpc.dotblox.io"], "faucets": [], "infoURL": "https://explorer.dotblox.io"}, {"name": "DOS Chain", "chainId": 7979, "shortName": "dos", "networkId": 7979, "nativeCurrency": {"name": "DOS", "symbol": "DOS", "decimals": 18}, "rpc": ["https://main.doschain.com"], "faucets": [], "infoURL": "https://doschain.io"}, {"name": "Teleport", "chainId": 8000, "shortName": "teleport", "networkId": 8000, "nativeCurrency": {"name": "Tele", "symbol": "TELE", "decimals": 18}, "rpc": ["https://evm-rpc.teleport.network"], "faucets": [], "infoURL": "https://teleport.network"}, {"name": "Teleport Testnet", "chainId": 8001, "shortName": "teleport-testnet", "networkId": 8001, "nativeCurrency": {"name": "Tele", "symbol": "TELE", "decimals": 18}, "rpc": ["https://evm-rpc.testnet.teleport.network"], "faucets": ["https://chain-docs.teleport.network/testnet/faucet.html"], "infoURL": "https://teleport.network"}, {"name": "MDGL Testnet", "chainId": 8029, "shortName": "mdgl", "networkId": 8029, "nativeCurrency": {"name": "MDGL Token", "symbol": "MDGLT", "decimals": 18}, "rpc": ["https://testnet.mdgl.io"], "faucets": [], "infoURL": "https://mdgl.io"}, {"name": "Karak <PERSON>", "chainId": 8054, "shortName": "karak-se<PERSON>a", "networkId": 8054, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.sepolia.karak.network"], "faucets": [], "infoURL": "https://karak.network"}, {"name": "Shardeum Liberty 1.X", "chainId": 8080, "shortName": "Liberty10", "networkId": 8080, "nativeCurrency": {"name": "Shardeum SHM", "symbol": "SHM", "decimals": 18}, "rpc": ["https://liberty10.shardeum.org/"], "faucets": ["https://faucet.liberty10.shardeum.org"], "infoURL": "https://docs.shardeum.org/"}, {"name": "Shardeum Liberty 2.X", "chainId": 8081, "shortName": "Liberty20", "networkId": 8081, "nativeCurrency": {"name": "Shardeum SHM", "symbol": "SHM", "decimals": 18}, "rpc": ["https://liberty20.shardeum.org/"], "faucets": ["https://faucet.liberty20.shardeum.org"], "infoURL": "https://docs.shardeum.org/"}, {"name": "Shardeum Sphinx 1.X", "chainId": 8082, "shortName": "Sphinx10", "networkId": 8082, "nativeCurrency": {"name": "Shardeum SHM", "symbol": "SHM", "decimals": 18}, "rpc": ["https://sphinx.shardeum.org/"], "faucets": ["https://faucet-sphinx.shardeum.org/"], "infoURL": "https://docs.shardeum.org/"}, {"name": "Bitcoin Chain", "chainId": 8086, "shortName": "Bitcoin", "networkId": 8086, "nativeCurrency": {"name": "Bitcoin", "symbol": "BTC", "decimals": 18}, "rpc": ["https://rpc.biteth.org"], "faucets": [], "infoURL": "https://biteth.org"}, {"name": "E-Dollar", "chainId": 8087, "shortName": "E-Dollar", "networkId": 8087, "nativeCurrency": {"name": "E-Dollar", "symbol": "USD", "decimals": 18}, "rpc": ["https://rpc.e-dollar.org"], "faucets": [], "infoURL": "https://e-dollar.org"}, {"name": "StreamuX Blockchain", "chainId": 8098, "shortName": "StreamuX", "networkId": 8098, "nativeCurrency": {"name": "StreamuX", "symbol": "SmuX", "decimals": 18}, "rpc": ["https://u0ma6t6heb:<EMAIL>/"], "faucets": [], "infoURL": "https://www.streamux.cloud"}, {"name": "Qitmeer Network Testnet", "chainId": 8131, "shortName": "meertest", "networkId": 8131, "nativeCurrency": {"name": "Qitmeer Testnet", "symbol": "MEER-T", "decimals": 18}, "rpc": ["https://testnet-qng.rpc.qitmeer.io", "https://testnet.meerlabs.com", "https://meer.testnet.meerfans.club"], "faucets": ["https://faucet.qitmeer.io"], "infoURL": "https://github.com/Qitmeer"}, {"name": "Qitmeer Network Mixnet", "chainId": 8132, "shortName": "meermix", "networkId": 8132, "nativeCurrency": {"name": "Qitmeer Mixnet", "symbol": "MEER-M", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://github.com/Qitmeer"}, {"name": "Qitmeer Network Privnet", "chainId": 8133, "shortName": "<PERSON><PERSON><PERSON><PERSON>v", "networkId": 8133, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "MEER-P", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://github.com/Qitmeer"}, {"name": "<PERSON><PERSON>", "chainId": 8134, "shortName": "amana", "networkId": 8134, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "MEER", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://github.com/Qitmeer"}, {"name": "<PERSON><PERSON>", "chainId": 8135, "shortName": "flana", "networkId": 8135, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "MEER", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://github.com/Qitmeer"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 8136, "shortName": "<PERSON><PERSON>a", "networkId": 8136, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "MEER", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://github.com/Qitmeer"}, {"name": "Testnet BeOne Chain", "chainId": 8181, "shortName": "tBOC", "networkId": 8181, "nativeCurrency": {"name": "Testnet BeOne Chain", "symbol": "tBOC", "decimals": 18}, "rpc": ["https://pre-boc1.beonechain.com"], "faucets": ["https://testnet.beonescan.com/faucet"], "infoURL": "https://testnet.beonescan.com"}, {"name": "<PERSON><PERSON>", "chainId": 8192, "shortName": "tqf", "networkId": 8192, "nativeCurrency": {"name": "TQF", "symbol": "TQF", "decimals": 18}, "rpc": ["https://rpc.toruschain.com"], "faucets": [], "infoURL": "https://docs.toruschain.com"}, {"name": "<PERSON><PERSON>", "chainId": 8194, "shortName": "ttqf", "networkId": 8194, "nativeCurrency": {"name": "tTQF", "symbol": "TTQF", "decimals": 18}, "rpc": ["https://rpc.testnet.toruschain.com"], "faucets": [], "infoURL": "https://docs.toruschain.com"}, {"name": "Klaytn Mainnet Cypress", "chainId": 8217, "shortName": "Cypress", "networkId": 8217, "nativeCurrency": {"name": "KLAY", "symbol": "KLAY", "decimals": 18}, "rpc": ["https://public-en-cypress.klaytn.net"], "faucets": [], "infoURL": "https://klaytn.foundation"}, {"name": "Blockton Blockchain", "chainId": 8272, "shortName": "BTON", "networkId": 8272, "nativeCurrency": {"name": "BLOCKTON", "symbol": "BTON", "decimals": 18}, "rpc": ["https://rpc.blocktonscan.com/"], "faucets": ["https://faucet.blocktonscan.com/"], "infoURL": "https://blocktoncoin.com"}, {"name": "KorthoTest", "chainId": 8285, "shortName": "<PERSON><PERSON><PERSON>", "networkId": 8285, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "KTO", "decimals": 11}, "rpc": ["https://www.krotho-test.net"], "faucets": [], "infoURL": "https://www.kortho.io/"}, {"name": "Dracones Financial Services", "chainId": 8387, "shortName": "fuck", "networkId": 8387, "nativeCurrency": {"name": "Functionally Universal Coin Kind", "symbol": "FUCK", "decimals": 18}, "rpc": ["https://api.dracones.net/"], "faucets": [], "infoURL": "https://wolfery.com"}, {"name": "Base", "chainId": 8453, "shortName": "base", "networkId": 8453, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://mainnet.base.org/", "https://developer-access-mainnet.base.org/", "https://base.gateway.tenderly.co", "wss://base.gateway.tenderly.co", "https://base-rpc.publicnode.com", "wss://base-rpc.publicnode.com"], "faucets": [], "infoURL": "https://base.org"}, {"name": "Toki Network", "chainId": 8654, "shortName": "toki", "networkId": 8654, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "TOKI", "decimals": 18}, "rpc": ["https://mainnet.buildwithtoki.com/v0/rpc"], "faucets": [], "infoURL": "https://www.buildwithtoki.com"}, {"name": "<PERSON><PERSON>", "chainId": 8655, "shortName": "toki-testnet", "networkId": 8655, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "TOKI", "decimals": 18}, "rpc": ["https://testnet.buildwithtoki.com/v0/rpc"], "faucets": [], "infoURL": "https://www.buildwithtoki.com"}, {"name": "Hela Official Runtime Mainnet", "chainId": 8668, "shortName": "hela", "networkId": 8668, "nativeCurrency": {"name": "<PERSON>la HLUS<PERSON>", "symbol": "HLUSD", "decimals": 18}, "rpc": ["https://mainnet-rpc.helachain.com"], "faucets": [], "infoURL": "https://helalabs.com"}, {"name": "TOOL Global Mainnet", "chainId": 8723, "shortName": "olo", "networkId": 8723, "nativeCurrency": {"name": "TOOL Global", "symbol": "OLO", "decimals": 18}, "rpc": ["https://mainnet-web3.wolot.io"], "faucets": [], "infoURL": "https://ibdt.io"}, {"name": "TOOL Global Testnet", "chainId": 8724, "shortName": "tolo", "networkId": 8724, "nativeCurrency": {"name": "TOOL Global", "symbol": "OLO", "decimals": 18}, "rpc": ["https://testnet-web3.wolot.io"], "faucets": ["https://testnet-explorer.wolot.io"], "infoURL": "https://testnet-explorer.wolot.io"}, {"name": "Storagechain Mainnet", "chainId": 8726, "shortName": "stor", "networkId": 8726, "nativeCurrency": {"name": "Storagechain", "symbol": "STOR", "decimals": 18}, "rpc": ["https://mainnet-validator.storagechain.io"], "faucets": [], "infoURL": "https://storagechain.io/about-us"}, {"name": "Storagechain Testnet", "chainId": 8727, "shortName": "tstor", "networkId": 8727, "nativeCurrency": {"name": "Storagechain", "symbol": "STOR", "decimals": 18}, "rpc": ["https://testnet-validator.storagechain.io"], "faucets": [], "infoURL": "https://storagechain.io/about-us"}, {"name": "Alph Network", "chainId": 8738, "shortName": "alph", "networkId": 8738, "nativeCurrency": {"name": "Alph Network", "symbol": "ALPH", "decimals": 18}, "rpc": ["https://rpc.alph.network", "wss://rpc.alph.network"], "faucets": [], "infoURL": "https://alph.network"}, {"name": "TMY Chain", "chainId": 8768, "shortName": "tmy", "networkId": 8768, "nativeCurrency": {"name": "TMY", "symbol": "TMY", "decimals": 18}, "rpc": ["https://node1.tmyblockchain.org/rpc"], "faucets": ["https://faucet.tmychain.org/"], "infoURL": "https://tmychain.org/"}, {"name": "IOTA EVM", "chainId": 8822, "shortName": "iotaevm", "networkId": 8822, "nativeCurrency": {"name": "IOTA", "symbol": "IOTA", "decimals": 18}, "rpc": ["https://json-rpc.evm.iotaledger.net", "https://ws.json-rpc.evm.iotaledger.net"], "faucets": [], "infoURL": "https://www.iota.org"}, {"name": "MARO Blockchain Mainnet", "chainId": 8848, "shortName": "maro", "networkId": 8848, "nativeCurrency": {"name": "MARO", "symbol": "MARO", "decimals": 18}, "rpc": ["https://rpc-mainnet.ma.ro"], "faucets": [], "infoURL": "https://ma.ro/"}, {"name": "SuperLumio", "chainId": 8866, "shortName": "superlumio", "networkId": 8866, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://mainnet.lumio.io/"], "faucets": [], "infoURL": "https://lumio.io/"}, {"name": "Unique", "chainId": 8880, "shortName": "unq", "networkId": 8880, "nativeCurrency": {"name": "Unique", "symbol": "UNQ", "decimals": 18}, "rpc": ["https://rpc.unique.network", "https://eu-rpc.unique.network", "https://asia-rpc.unique.network", "https://us-rpc.unique.network"], "faucets": [], "infoURL": "https://unique.network"}, {"name": "Quartz by Unique", "chainId": 8881, "shortName": "qtz", "networkId": 8881, "nativeCurrency": {"name": "Quartz", "symbol": "QTZ", "decimals": 18}, "rpc": ["https://rpc-quartz.unique.network", "https://quartz.api.onfinality.io/public-ws", "https://eu-rpc-quartz.unique.network", "https://asia-rpc-quartz.unique.network", "https://us-rpc-quartz.unique.network"], "faucets": [], "infoURL": "https://unique.network"}, {"name": "Opal testnet by Unique", "chainId": 8882, "shortName": "opl", "networkId": 8882, "nativeCurrency": {"name": "Opal", "symbol": "UNQ", "decimals": 18}, "rpc": ["https://rpc-opal.unique.network", "https://us-rpc-opal.unique.network", "https://eu-rpc-opal.unique.network", "https://asia-rpc-opal.unique.network"], "faucets": ["https://t.me/unique2faucet_opal_bot"], "infoURL": "https://unique.network"}, {"name": "Sapphire by <PERSON><PERSON>", "chainId": 8883, "shortName": "sph", "networkId": 8883, "nativeCurrency": {"name": "Quartz", "symbol": "QTZ", "decimals": 18}, "rpc": ["https://rpc-sapphire.unique.network", "https://us-rpc-sapphire.unique.network", "https://eu-rpc-sapphire.unique.network", "https://asia-rpc-sapphire.unique.network"], "faucets": [], "infoURL": "https://unique.network"}, {"name": "XANAChain", "chainId": 8888, "shortName": "XANAChain", "networkId": 8888, "nativeCurrency": {"name": "XETA", "symbol": "XETA", "decimals": 18}, "rpc": ["https://mainnet.xana.net/rpc"], "faucets": [], "infoURL": "https://xanachain.xana.net/"}, {"name": "Vyvo Smart Chain", "chainId": 8889, "shortName": "vsc", "networkId": 8889, "nativeCurrency": {"name": "VSC", "symbol": "VSC", "decimals": 18}, "rpc": ["https://vsc-dataseed.vyvo.org:8889"], "faucets": [], "infoURL": "https://vsc-dataseed.vyvo.org"}, {"name": "Orenium Testnet Protocol", "chainId": 8890, "shortName": "tore", "networkId": 8890, "nativeCurrency": {"name": "ORENIUM", "symbol": "tORE", "decimals": 18}, "rpc": ["https://rpc-dev-testnet.orenium.org/", "https://rpc-testnet.orenium.org/", "https://rpc-orc.oredex.finance", "https://testnet-rpc.oredex.finance", "https://oredex-node.oredex.finance"], "faucets": ["https://faucetcoin.orenium.org"], "infoURL": "https://orenium.org"}, {"name": "Mammoth Mainnet", "chainId": 8898, "shortName": "mmt", "networkId": 8898, "nativeCurrency": {"name": "Mammoth Token", "symbol": "MMT", "decimals": 18}, "rpc": ["https://dataseed.mmtscan.io", "https://dataseed1.mmtscan.io", "https://dataseed2.mmtscan.io"], "faucets": ["https://faucet.mmtscan.io/"], "infoURL": "https://mmtchain.io/"}, {"name": "JIBCHAIN L1", "chainId": 8899, "shortName": "jbc", "networkId": 8899, "nativeCurrency": {"name": "JIBCOIN", "symbol": "JBC", "decimals": 18}, "rpc": ["https://rpc-l1.jibchain.net", "https://jib-rpc.inan.in.th"], "faucets": [], "infoURL": "https://jibchain.net"}, {"name": "Giant Mammoth Mainnet", "chainId": 8989, "shortName": "gmmt", "networkId": 8989, "nativeCurrency": {"name": "Giant Mammoth Coin", "symbol": "GMMT", "decimals": 18}, "rpc": ["https://rpc-asia.gmmtchain.io"], "faucets": [], "infoURL": "https://gmmtchain.io/"}, {"name": "b<PERSON><PERSON><PERSON>", "chainId": 8995, "shortName": "berg", "networkId": 8995, "nativeCurrency": {"name": "BERG", "symbol": "U+25B3", "decimals": 18}, "rpc": ["https://core.bloxberg.org"], "faucets": ["https://faucet.bloxberg.org/"], "infoURL": "https://bloxberg.org"}, {"name": "Evmos Testnet", "chainId": 9000, "shortName": "evmos-testnet", "networkId": 9000, "nativeCurrency": {"name": "test-Evmos", "symbol": "tEVMOS", "decimals": 18}, "rpc": ["https://evmos-testnet.lava.build", "https://eth.bd.evmos.dev:8545"], "faucets": ["https://faucet.evmos.dev"], "infoURL": "https://evmos.org"}, {"name": "Evmos", "chainId": 9001, "shortName": "evmos", "networkId": 9001, "nativeCurrency": {"name": "Evmos", "symbol": "EVMOS", "decimals": 18}, "rpc": ["https://evmos.lava.build", "wss://evmos.lava.build/websocket", "https://evmos-evm.publicnode.com", "wss://evmos-evm.publicnode.com"], "faucets": [], "infoURL": "https://evmos.org"}, {"name": "Shido Testnet Block", "chainId": 9007, "shortName": "ShidoTestnet", "networkId": 9007, "nativeCurrency": {"name": "Shido Testnet Token", "symbol": "SHIDO", "decimals": 18}, "rpc": ["https://rpc-testnet-nodes.shidoscan.com", "wss://wss-testnet-nodes.shidoscan.com"], "faucets": ["https://testnet.shidoscan.com/faucet"], "infoURL": "https://www.nexablock.io"}, {"name": "Shido Mainnet Block", "chainId": 9008, "shortName": "Shido", "networkId": 9008, "nativeCurrency": {"name": "Shido Mainnet Token", "symbol": "SHIDO", "decimals": 18}, "rpc": ["https://rpc-nodes.shidoscan.com", "wss://wss-nodes.shidoscan.com", "https://rpc-delta-nodes.shidoscan.com", "wss://wss-delta-nodes.shidoscan.com"], "faucets": [], "infoURL": "https://shido.io"}, {"name": "BerylBit Mainnet", "chainId": 9012, "shortName": "brb", "networkId": 9012, "nativeCurrency": {"name": "BerylBit Chain Native Token", "symbol": "BRB", "decimals": 18}, "rpc": ["https://mainnet.berylbit.io"], "faucets": ["https://t.me/BerylBit"], "infoURL": "https://www.beryl-bit.com"}, {"name": "Nexa Testnet Block", "chainId": 9024, "shortName": "NexaTestnet", "networkId": 9024, "nativeCurrency": {"name": "Nexa Testnet Token", "symbol": "NEXB", "decimals": 18}, "rpc": ["https://rpc-testnet-nodes.nexablockscan.io"], "faucets": ["https://testnet.nexablockscan.io/faucet"], "infoURL": "https://www.nexablock.io"}, {"name": "Nexa Mainnet Block", "chainId": 9025, "shortName": "Nexa", "networkId": 9025, "nativeCurrency": {"name": "Nexa Mainnet Token", "symbol": "NEXB", "decimals": 18}, "rpc": ["https://rpc-nodes.nexablockscan.io", "wss://wss-nodes.nexablockscan.io", "https://rpc-nodes-delta.nexablockscan.io"], "faucets": [], "infoURL": "https://www.nexablock.io"}, {"name": "Genesis Coin", "chainId": 9100, "shortName": "GENEC", "networkId": 9100, "nativeCurrency": {"name": "GN Coin", "symbol": "GNC", "decimals": 18}, "rpc": ["https://genesis-gn.com", "wss://genesis-gn.com"], "faucets": [], "infoURL": "https://genesis-gn.com"}, {"name": "Rinia Testnet Old", "chainId": 9170, "shortName": "_old_tfire", "networkId": 9170, "nativeCurrency": {"name": "Firechain", "symbol": "FIRE", "decimals": 18}, "rpc": [], "faucets": ["https://faucet.thefirechain.com"], "infoURL": "https://thefirechain.com"}, {"name": "Codefin Mainnet", "chainId": 9223, "shortName": "COF", "networkId": 9223, "nativeCurrency": {"name": "Codefin", "symbol": "COF", "decimals": 18}, "rpc": ["https://chain-rpc.codefin.pro"], "faucets": [], "infoURL": "https://network.codefin.pro"}, {"name": "Dogcoin Testnet", "chainId": 9339, "shortName": "DOGSt", "networkId": 9339, "nativeCurrency": {"name": "Dogcoin", "symbol": "DOGS", "decimals": 18}, "rpc": ["https://testnet-rpc.dogcoin.me"], "faucets": ["https://faucet.dogcoin.network"], "infoURL": "https://dogcoin.network"}, {"name": "Dela Sepolia Testnet", "chainId": 9393, "shortName": "del<PERSON><PERSON>", "networkId": 9393, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://sepolia-dela.deperp.com"], "faucets": [], "infoURL": "https://www.deperp.com/dela"}, {"name": "Evoke Mainnet", "chainId": 9395, "shortName": "MTHN", "networkId": 9395, "nativeCurrency": {"name": "MTHN", "symbol": "MTHN", "decimals": 18}, "rpc": ["https://mainnet-rpc.evokescan.org"], "faucets": [], "infoURL": "https://explorer.evokescan.org"}, {"name": "Rangers Protocol Testnet Robin", "chainId": 9527, "shortName": "trpg", "networkId": 9527, "nativeCurrency": {"name": "Rangers Protocol Gas", "symbol": "tRPG", "decimals": 18}, "rpc": ["https://robin.rangersprotocol.com/api/jsonrpc"], "faucets": ["https://robin-faucet.rangersprotocol.com"], "infoURL": "https://rangersprotocol.com"}, {"name": "QEasyWeb3 Testnet", "chainId": 9528, "shortName": "QETTest", "networkId": 9528, "nativeCurrency": {"name": "QET", "symbol": "QET", "decimals": 18}, "rpc": ["https://qeasyweb3.com"], "faucets": ["http://faucet.qeasyweb3.com"], "infoURL": "https://www.qeasyweb3.com"}, {"name": "Neonlink Testnet", "chainId": 9559, "shortName": "testneon", "networkId": 9559, "nativeCurrency": {"name": "Neonlink Native Token", "symbol": "tNEON", "decimals": 18}, "rpc": ["https://testnet.neonlink.io"], "faucets": ["https://faucet.neonlink.io/"], "infoURL": "https://neonlink.io"}, {"name": "Oort MainnetDev", "chainId": 9700, "shortName": "MainnetDev", "networkId": 9700, "nativeCurrency": {"name": "Oort", "symbol": "OORT", "decimals": 18}, "rpc": ["https://dev-rpc.oortech.com"], "faucets": [], "infoURL": "https://oortech.com"}, {"name": "Boba BNB Testnet", "chainId": 9728, "shortName": "BobaBnbTestnet", "networkId": 9728, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "BOBA", "decimals": 18}, "rpc": ["https://testnet.bnb.boba.network", "wss://wss.testnet.bnb.boba.network", "https://replica.testnet.bnb.boba.network", "wss://replica-wss.testnet.bnb.boba.network", "https://boba-bnb-testnet.gateway.tenderly.co", "wss://boba-bnb-testnet.gateway.tenderly.co"], "faucets": [], "infoURL": "https://boba.network"}, {"name": "MainnetZ Testnet", "chainId": 9768, "shortName": "NetZt", "networkId": 9768, "nativeCurrency": {"name": "MainnetZ", "symbol": "NetZ", "decimals": 18}, "rpc": ["https://testnet-rpc.mainnetz.io"], "faucets": ["https://faucet.mainnetz.io"], "infoURL": "https://testnet.mainnetz.io"}, {"name": "PepeNetwork Mainnet", "chainId": 9779, "shortName": "pn", "networkId": 9779, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "WPEPE", "decimals": 18}, "rpc": ["https://rpc-mainnet.pepenetwork.io"], "faucets": [], "infoURL": "https://pepenetwork.io"}, {"name": "Carbon EVM", "chainId": 9790, "shortName": "carbon", "networkId": 9790, "nativeCurrency": {"name": "swth", "symbol": "SWTH", "decimals": 18}, "rpc": ["https://evm-api.carbon.network/"], "faucets": [], "infoURL": "https://carbon.network/"}, {"name": "Carbon EVM Testnet", "chainId": 9792, "shortName": "carbon-testnet", "networkId": 9792, "nativeCurrency": {"name": "swth", "symbol": "SWTH", "decimals": 18}, "rpc": ["https://test-evm-api.carbon.network/"], "faucets": [], "infoURL": "https://carbon.network/"}, {"name": "IMPERIUM TESTNET", "chainId": 9818, "shortName": "tIMP", "networkId": 9818, "nativeCurrency": {"name": "tIMP", "symbol": "tIMP", "decimals": 18}, "rpc": ["https://data-aws-testnet.imperiumchain.com", "https://data-aws2-testnet.imperiumchain.com"], "faucets": ["https://faucet.imperiumchain.com/"], "infoURL": "https://imperiumchain.com"}, {"name": "IMPERIUM MAINNET", "chainId": 9819, "shortName": "IMP", "networkId": 9819, "nativeCurrency": {"name": "IMP", "symbol": "IMP", "decimals": 18}, "rpc": ["https://data-aws-mainnet.imperiumchain.com", "https://data-aws2-mainnet.imperiumchain.com"], "faucets": ["https://faucet.imperiumchain.com/"], "infoURL": "https://imperiumchain.com"}, {"name": "Dogelayer Mainnet", "chainId": 9888, "shortName": "<PERSON><PERSON><PERSON>", "networkId": 9888, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "DOGE", "decimals": 18}, "rpc": ["https://dl-rpc.dogelayer.org"], "faucets": [], "infoURL": "https://dogelayer.org"}, {"name": "Larissa <PERSON>", "chainId": 9898, "shortName": "lrs", "networkId": 1, "nativeCurrency": {"name": "Larissa", "symbol": "LRS", "decimals": 18}, "rpc": ["https://rpc.larissa.network"], "faucets": [], "infoURL": "https://larissa.network"}, {"name": "Espento Mainnet", "chainId": 9911, "shortName": "spent", "networkId": 9911, "nativeCurrency": {"name": "ESPENTO", "symbol": "SPENT", "decimals": 18}, "rpc": ["https://rpc.escscan.com/"], "faucets": [], "infoURL": "https://espento.network"}, {"name": "Mind Smart Chain Testnet", "chainId": 9977, "shortName": "tMIND", "networkId": 9977, "nativeCurrency": {"name": "MIND Coin", "symbol": "tMIND", "decimals": 18}, "rpc": ["https://testnet-msc.mindchain.info/", "wss://testnet-msc.mindchain.info/ws"], "faucets": ["https://faucet.mindchain.info/"], "infoURL": "https://mindchain.info"}, {"name": "Combo Mainnet", "chainId": 9980, "shortName": "combo-mainnet", "networkId": 9980, "nativeCurrency": {"name": "BNB Chain Native Token", "symbol": "BNB", "decimals": 18}, "rpc": ["https://rpc.combonetwork.io"], "faucets": [], "infoURL": "https://combonetwork.io"}, {"name": "Agung Network", "chainId": 9990, "shortName": "AGNG", "networkId": 9990, "nativeCurrency": {"name": "<PERSON><PERSON>g", "symbol": "AGNG", "decimals": 18}, "rpc": ["https://rpcpc1-qa.agung.peaq.network"], "faucets": [], "infoURL": "https://www.peaq.network"}, {"name": "Mind Smart Chain Mainnet", "chainId": 9996, "shortName": "MIND", "networkId": 9996, "nativeCurrency": {"name": "MIND Coin", "symbol": "MIND", "decimals": 18}, "rpc": ["https://rpc-msc.mindchain.info/", "https://seednode.mindchain.info", "https://archive.mindchain.info/", "https://mind-smart-chain.rpc.thirdweb.com", "wss://archive.mindchain.info/ws", "wss://seednode.mindchain.info/ws"], "faucets": [], "infoURL": "https://mindchain.info"}, {"name": "AltLayer Testnet", "chainId": 9997, "shortName": "alt-testnet", "networkId": 9997, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://testnet-rollup-api.altlayer.io"], "faucets": [], "infoURL": "https://altlayer.io"}, {"name": "Ztc Mainnet", "chainId": 9998, "shortName": "ZTC", "networkId": 9998, "nativeCurrency": {"name": "Ztcer", "symbol": "ZTC", "decimals": 5}, "rpc": ["https://zitcoin.us"], "faucets": [], "infoURL": "https://ztc.best"}, {"name": "myOwn Testnet", "chainId": 9999, "shortName": "myn", "networkId": 9999, "nativeCurrency": {"name": "MYN", "symbol": "MYN", "decimals": 18}, "rpc": ["https://geth.dev.bccloud.net"], "faucets": [], "infoURL": "https://docs.bccloud.net/"}, {"name": "Smart Bitcoin Cash", "chainId": 10000, "shortName": "smartbch", "networkId": 10000, "nativeCurrency": {"name": "Bitcoin Cash", "symbol": "BCH", "decimals": 18}, "rpc": ["https://smartbch.greyh.at", "https://rpc-mainnet.smartbch.org", "https://smartbch.fountainhead.cash/mainnet", "https://smartbch.devops.cash/mainnet"], "faucets": [], "infoURL": "https://smartbch.org/"}, {"name": "Smart Bitcoin Cash Testnet", "chainId": 10001, "shortName": "smartbchtest", "networkId": 10001, "nativeCurrency": {"name": "Bitcoin Cash Test Token", "symbol": "BCHT", "decimals": 18}, "rpc": ["https://rpc-testnet.smartbch.org", "https://smartbch.devops.cash/testnet"], "faucets": [], "infoURL": "http://smartbch.org/"}, {"name": "Gon Chain", "chainId": 10024, "shortName": "gon", "networkId": 10024, "nativeCurrency": {"name": "Gon <PERSON>ken", "symbol": "GT", "decimals": 18}, "rpc": ["https://node1.testnet.gaiaopen.network", "https://node1.mainnet.gon.network", "https://node2.mainnet.gon.network", "https://node3.mainnet.gon.network", "https://node4.mainnet.gon.network"], "faucets": [], "infoURL": ""}, {"name": "Japan Open Chain Testnet", "chainId": 10081, "shortName": "joct", "networkId": 10081, "nativeCurrency": {"name": "Japan Open Chain Testnet Token", "symbol": "JOCT", "decimals": 18}, "rpc": ["https://rpc-1.testnet.japanopenchain.org:8545", "https://rpc-2.testnet.japanopenchain.org:8545"], "faucets": [], "infoURL": "https://www.japanopenchain.org/"}, {"name": "SJATSH", "chainId": 10086, "shortName": "SJ", "networkId": 10086, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["http://geth.free.idcfengye.com"], "faucets": [], "infoURL": "https://sjis.me"}, {"name": "Blockchain Genesis Mainnet", "chainId": 10101, "shortName": "GEN", "networkId": 10101, "nativeCurrency": {"name": "GEN", "symbol": "GEN", "decimals": 18}, "rpc": ["https://eu.mainnet.xixoio.com", "https://us.mainnet.xixoio.com", "https://asia.mainnet.xixoio.com"], "faucets": [], "infoURL": "https://www.xixoio.com/"}, {"name": "Gnosis Chiado Testnet", "chainId": 10200, "shortName": "chi", "networkId": 10200, "nativeCurrency": {"name": "Chiado xDAI", "symbol": "XDAI", "decimals": 18}, "rpc": ["https://rpc.chiadochain.net", "https://rpc.chiado.gnosis.gateway.fm", "wss://rpc.chiadochain.net/wss", "https://gnosis-chiado-rpc.publicnode.com", "wss://gnosis-chiado-rpc.publicnode.com", "https://gnosis-chiado.drpc.org", "wss://gnosis-chiado.drpc.org"], "faucets": ["https://gnosisfaucet.com"], "infoURL": "https://docs.gnosischain.com"}, {"name": "MaxxChain Mainnet", "chainId": 10201, "shortName": "PWR", "networkId": 10201, "nativeCurrency": {"name": "Power", "symbol": "PWR", "decimals": 18}, "rpc": ["https://rpc.maxxchain.org", "https://rpc1.maxxchain.org", "https://rpc2.maxxchain.org"], "faucets": ["https://faucet.maxxchain.org"], "infoURL": "https://www.maxxchain.org/"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 10242, "shortName": "aa", "networkId": 10242, "nativeCurrency": {"name": "<PERSON><PERSON>a", "symbol": "AA", "decimals": 18}, "rpc": ["https://rpc.arthera.net"], "faucets": [], "infoURL": "https://docs.arthera.net/build/developing-sc/using-hardhat"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 10243, "shortName": "aat", "networkId": 10243, "nativeCurrency": {"name": "<PERSON><PERSON>a", "symbol": "AA", "decimals": 18}, "rpc": ["https://rpc-test.arthera.net"], "faucets": ["https://faucet.arthera.net"], "infoURL": "https://docs.arthera.net"}, {"name": "0XTade", "chainId": 10248, "shortName": "0xt", "networkId": 10248, "nativeCurrency": {"name": "0XT", "symbol": "0XT", "decimals": 18}, "rpc": ["https://node.0xtchain.com"], "faucets": [], "infoURL": "https://www.0xtrade.finance/"}, {"name": "WorldLand Testnet", "chainId": 10395, "shortName": "TWLC", "networkId": 10395, "nativeCurrency": {"name": "Worldland", "symbol": "WLC", "decimals": 18}, "rpc": ["https://gwangju.worldland.foundation"], "faucets": [], "infoURL": "https://worldland.foundation"}, {"name": "Numbers Mainnet", "chainId": 10507, "shortName": "<PERSON>", "networkId": 10507, "nativeCurrency": {"name": "NUM Token", "symbol": "NUM", "decimals": 18}, "rpc": ["https://mainnetrpc.num.network"], "faucets": [], "infoURL": "https://numbersprotocol.io"}, {"name": "Numbers Testnet", "chainId": 10508, "shortName": "Snow", "networkId": 10508, "nativeCurrency": {"name": "NUM Token", "symbol": "NUM", "decimals": 18}, "rpc": ["https://testnetrpc.num.network"], "faucets": ["https://faucet.avax.network/?subnet=num", "https://faucet.num.network"], "infoURL": "https://numbersprotocol.io"}, {"name": "CryptoCoinPay", "chainId": 10823, "shortName": "CCP", "networkId": 10823, "nativeCurrency": {"name": "CryptoCoinPay", "symbol": "CCP", "decimals": 18}, "rpc": ["http://node106.cryptocoinpay.info:8545", "ws://node106.cryptocoinpay.info:8546"], "faucets": [], "infoURL": "https://www.cryptocoinpay.co"}, {"name": "Quadrans Blockchain", "chainId": 10946, "shortName": "quadrans", "networkId": 10946, "nativeCurrency": {"name": "Quadrans Coin", "symbol": "QDC", "decimals": 18}, "rpc": ["https://rpc.quadrans.io", "https://rpcna.quadrans.io", "https://rpceu.quadrans.io"], "faucets": [], "infoURL": "https://quadrans.io"}, {"name": "Quadrans Blockchain Testnet", "chainId": 10947, "shortName": "quadranstestnet", "networkId": 10947, "nativeCurrency": {"name": "Quadrans Testnet Coin", "symbol": "tQDC", "decimals": 18}, "rpc": ["https://rpctest.quadrans.io", "https://rpctest2.quadrans.io"], "faucets": ["https://faucetpage.quadrans.io"], "infoURL": "https://quadrans.io"}, {"name": "Astra", "chainId": 11110, "shortName": "astra", "networkId": 11110, "nativeCurrency": {"name": "Astra", "symbol": "ASA", "decimals": 18}, "rpc": ["https://rpc.astranaut.io", "https://rpc1.astranaut.io"], "faucets": [], "infoURL": "https://astranaut.io"}, {"name": "WAGMI", "chainId": 11111, "shortName": "WAGMI", "networkId": 11111, "nativeCurrency": {"name": "WAGMI", "symbol": "WGM", "decimals": 18}, "rpc": ["https://subnets.avax.network/wagmi/wagmi-chain-testnet/rpc"], "faucets": ["https://faucet.avax.network/?subnet=wagmi"], "infoURL": "https://subnets-test.avax.network/wagmi/details"}, {"name": "Astra Testnet", "chainId": 11115, "shortName": "astra-testnet", "networkId": 11115, "nativeCurrency": {"name": "test-<PERSON><PERSON>", "symbol": "tASA", "decimals": 18}, "rpc": ["https://rpc.astranaut.dev"], "faucets": ["https://faucet.astranaut.dev"], "infoURL": "https://astranaut.io"}, {"name": "HashBit Mainnet", "chainId": 11119, "shortName": "hbit", "networkId": 11119, "nativeCurrency": {"name": "HashBit Native Token", "symbol": "HBIT", "decimals": 18}, "rpc": ["https://mainnet-rpc.hashbit.org", "https://rpc.hashbit.org"], "faucets": [], "infoURL": "https://hashbit.org"}, {"name": "Shine Chain", "chainId": 11221, "shortName": "SC20", "networkId": 11221, "nativeCurrency": {"name": "<PERSON>", "symbol": "SC20", "decimals": 18}, "rpc": ["https://rpc.shinescan.io"], "faucets": [], "infoURL": "https://shinechain.tech"}, {"name": "Haqq Network", "chainId": 11235, "shortName": "ISLM", "networkId": 11235, "nativeCurrency": {"name": "Islamic Coin", "symbol": "ISLM", "decimals": 18}, "rpc": ["https://rpc.eth.haqq.network", "https://haqq-evm-rpc.publicnode.com", "wss://haqq-evm-rpc.publicnode.com", "https://haqq.drpc.org", "wss://haqq.drpc.org"], "faucets": [], "infoURL": "https://islamiccoin.net"}, {"name": "Shyft Testnet", "chainId": 11437, "shortName": "shyftt", "networkId": 11437, "nativeCurrency": {"name": "Shyft Test Token", "symbol": "SHYFTT", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://shyft.network"}, {"name": "BEVM Mainnet", "chainId": 11501, "shortName": "bevm", "networkId": 11501, "nativeCurrency": {"name": "BTC", "symbol": "BTC", "decimals": 18}, "rpc": ["https://rpc-mainnet-1.bevm.io/", "https://rpc-mainnet-2.bevm.io/"], "faucets": [], "infoURL": "https://bevm.io"}, {"name": "BEVM Testnet", "chainId": 11503, "shortName": "bevm-test", "networkId": 11503, "nativeCurrency": {"name": "BTC", "symbol": "BTC", "decimals": 18}, "rpc": ["https://testnet.bevm.io/"], "faucets": [], "infoURL": "https://bevm.io"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 11612, "shortName": "SRDXt", "networkId": 11612, "nativeCurrency": {"name": "<PERSON>rdis", "symbol": "SRDX", "decimals": 18}, "rpc": ["https://testnet-rpc.sardisnetwork.com"], "faucets": ["https://faucet.sardisnetwork.com"], "infoURL": "https://mysardis.com"}, {"name": "Santiment Intelligence Network", "chainId": 11888, "shortName": "SAN", "networkId": 11888, "nativeCurrency": {"name": "SANR", "symbol": "SANR", "decimals": 18}, "rpc": ["https://sanrchain-node.santiment.net"], "faucets": [], "infoURL": "https://sanr.app"}, {"name": "Polygon Supernet Arianee", "chainId": 11891, "shortName": "<PERSON><PERSON>", "networkId": 11891, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ARIA20", "decimals": 18}, "rpc": ["https://rpc.polygonsupernet.public.arianee.net"], "faucets": [], "infoURL": "https://arianee.org"}, {"name": "SatoshiChain Mainnet", "chainId": 12009, "shortName": "sats", "networkId": 12009, "nativeCurrency": {"name": "<PERSON>shi<PERSON>hain Coin", "symbol": "SATS", "decimals": 18}, "rpc": ["https://mainnet-rpc.satoshichain.io"], "faucets": [], "infoURL": "https://satoshichain.net"}, {"name": "Aternos", "chainId": 12020, "shortName": "ATR", "networkId": 12020, "nativeCurrency": {"name": "Aternos", "symbol": "ATR", "decimals": 18}, "rpc": ["https://rpc.aternoschain.com"], "faucets": ["https://faucet.aternoschain.com"], "infoURL": "https://aternoschain.com"}, {"name": "Singularity ZERO Testnet", "chainId": 12051, "shortName": "tZERO", "networkId": 12051, "nativeCurrency": {"name": "ZERO", "symbol": "tZERO", "decimals": 18}, "rpc": ["https://betaenv.singularity.gold:18545"], "faucets": ["https://nft.singularity.gold"], "infoURL": "https://www.singularity.gold"}, {"name": "Singularity ZERO Mainnet", "chainId": 12052, "shortName": "ZERO", "networkId": 12052, "nativeCurrency": {"name": "ZERO", "symbol": "ZERO", "decimals": 18}, "rpc": ["https://zerorpc.singularity.gold"], "faucets": ["https://zeroscan.singularity.gold"], "infoURL": "https://www.singularity.gold"}, {"name": "BRC Chain Mainnet", "chainId": 12123, "shortName": "BRC", "networkId": 12123, "nativeCurrency": {"name": "BRC Chain mainnet native token", "symbol": "BRC", "decimals": 18}, "rpc": ["https://rpc.brcchain.io"], "faucets": ["https://faucet.brcchain.io"], "infoURL": "https://bridge.brcchain.io"}, {"name": "Fibonacci Mainnet", "chainId": 12306, "shortName": "fibo", "networkId": 1230, "nativeCurrency": {"name": "FIBONACCI UTILITY TOKEN", "symbol": "FIBO", "decimals": 18}, "rpc": ["https://node1.fibo-api.asia", "https://node2.fibo-api.asia", "https://node3.fibo-api.asia", "https://node4.fibo-api.asia", "https://node5.fibo-api.asia", "https://node6.fibo-api.asia", "https://node7.fibo-api.asia", "https://node1.fibo-rpc.asia", "https://node2.fibo-rpc.asia", "https://node3.fibo-rpc.asia", "https://node4.fibo-rpc.asia", "https://node5.fibo-rpc.asia", "https://node6.fibo-rpc.asia", "https://node7.fibo-rpc.asia"], "faucets": ["https://test.fibochain.org/faucets"], "infoURL": "https://fibochain.org"}, {"name": "BLG Testnet", "chainId": 12321, "shortName": "blgchain", "networkId": 12321, "nativeCurrency": {"name": "Blg", "symbol": "BLG", "decimals": 18}, "rpc": ["https://rpc.blgchain.com"], "faucets": ["https://faucet.blgchain.com"], "infoURL": "https://blgchain.com"}, {"name": "Step Testnet", "chainId": 12345, "shortName": "steptest", "networkId": 12345, "nativeCurrency": {"name": "FITFI", "symbol": "FITFI", "decimals": 18}, "rpc": ["https://rpc.testnet.step.network"], "faucets": ["https://faucet.step.network"], "infoURL": "https://step.network"}, {"name": "RSS3 VSL Mainnet", "chainId": 12553, "shortName": "rss3", "networkId": 12553, "nativeCurrency": {"name": "RSS3", "symbol": "RSS3", "decimals": 18}, "rpc": ["https://rpc.rss3.io"], "faucets": [], "infoURL": "https://rss3.io"}, {"name": "Rikeza Network Testnet", "chainId": 12715, "shortName": "tRIK", "networkId": 12715, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "RIK", "decimals": 18}, "rpc": ["https://testnet-rpc.rikscan.com"], "faucets": [], "infoURL": "https://rikeza.io"}, {"name": "Quantum Chain Testnet", "chainId": 12890, "shortName": "tqnet", "networkId": 12890, "nativeCurrency": {"name": "Quantum Chain", "symbol": "tQNET", "decimals": 18}, "rpc": ["https://testnet-rpc.quantumscan.org"], "faucets": [], "infoURL": "https://quantumnetwork.gg"}, {"name": "SPS", "chainId": 13000, "shortName": "SPS", "networkId": 13000, "nativeCurrency": {"name": "ECG", "symbol": "ECG", "decimals": 18}, "rpc": ["https://rpc.ssquad.games"], "faucets": [], "infoURL": "https://ssquad.games/"}, {"name": "Credit Smart Chain", "chainId": 13308, "shortName": "Credit", "networkId": 13308, "nativeCurrency": {"name": "Credit", "symbol": "CREDIT", "decimals": 18}, "rpc": ["https://rpc.creditsmartchain.com"], "faucets": [], "infoURL": "https://creditsmartchain.com"}, {"name": "<PERSON><PERSON>", "chainId": 13337, "shortName": "beam-testnet", "networkId": 13337, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "BEAM", "decimals": 18}, "rpc": ["https://build.onbeam.com/rpc/testnet", "wss://build.onbeam.com/ws/testnet", "https://subnets.avax.network/beam/testnet/rpc", "wss://subnets.avax.network/beam/testnet/ws"], "faucets": ["https://faucet.avax.network/?subnet=beam", "https://faucet.onbeam.com"], "infoURL": "https://www.onbeam.com"}, {"name": "Immutable zkEVM", "chainId": 13371, "shortName": "imx", "networkId": 13371, "nativeCurrency": {"name": "IMX", "symbol": "IMX", "decimals": 18}, "rpc": ["https://rpc.immutable.com", "https://immutable-zkevm.drpc.org", "wss://immutable-zkevm.drpc.org"], "faucets": ["https://docs.immutable.com/docs/zkEVM/guides/faucet"], "infoURL": "https://www.immutable.com"}, {"name": "Phoenix Mainnet", "chainId": 13381, "shortName": "Phoenix", "networkId": 13381, "nativeCurrency": {"name": "Phoenix", "symbol": "PHX", "decimals": 18}, "rpc": ["https://rpc.phoenixplorer.com/"], "faucets": [], "infoURL": "https://cryptophoenix.org/phoenix"}, {"name": "<PERSON><PERSON>", "chainId": 13396, "shortName": "masa", "networkId": 13396, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "MASA", "decimals": 18}, "rpc": ["https://subnets.avax.network/masanetwork/mainnet/rpc"], "faucets": [], "infoURL": "https://masa.finance"}, {"name": "Immutable zkEVM Testnet", "chainId": 13473, "shortName": "imx-testnet", "networkId": 13473, "nativeCurrency": {"name": "Test IMX", "symbol": "tIMX", "decimals": 18}, "rpc": ["https://rpc.testnet.immutable.com", "https://immutable-zkevm-testnet.drpc.org", "wss://immutable-zkevm-testnet.drpc.org"], "faucets": ["https://docs.immutable.com/docs/zkEVM/guides/faucet"], "infoURL": "https://www.immutable.com"}, {"name": "Kronobit Mainnet", "chainId": 13600, "shortName": "KNB", "networkId": 13600, "nativeCurrency": {"name": "Kronobit", "symbol": "KNB", "decimals": 18}, "rpc": ["https://mainnet-rpc.qbitscan.com"], "faucets": [], "infoURL": "https://kronobit.org"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 13812, "shortName": "sus", "networkId": 13812, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "OPN", "decimals": 18}, "rpc": ["https://gateway.opn.network/node/ext/bc/2VsZe5DstWw2bfgdx3YbjKcMsJnNDjni95sZorBEdk9L9Qr9Fr/rpc"], "faucets": [], "infoURL": ""}, {"name": "SPS Testnet", "chainId": 14000, "shortName": "SPS-Test", "networkId": 14000, "nativeCurrency": {"name": "ECG", "symbol": "ECG", "decimals": 18}, "rpc": ["https://www.3sps.net"], "faucets": [], "infoURL": "https://ssquad.games/"}, {"name": "EVOLVE Testnet", "chainId": 14324, "shortName": "evo", "networkId": 14324, "nativeCurrency": {"name": "Evolve", "symbol": "EVO", "decimals": 18}, "rpc": ["https://testnet-rpc.evolveblockchain.io"], "faucets": ["https://faucet.evolveblockchain.io"], "infoURL": "https://evolveblockchain.io"}, {"name": "Vitruveo Testnet", "chainId": 14333, "shortName": "vitruveo-test", "networkId": 14333, "nativeCurrency": {"name": "Vitruveo Test Coin", "symbol": "tVTRU", "decimals": 18}, "rpc": ["https://test-rpc.vitruveo.xyz"], "faucets": ["https://faucet.vitruveo.xyz"], "infoURL": "https://www.vitruveo.xyz"}, {"name": "Humanode Testnet 5 Israfel", "chainId": 14853, "shortName": "hmnd-t5", "networkId": 14853, "nativeCurrency": {"name": "eHMND", "symbol": "eHMND", "decimals": 18}, "rpc": ["https://explorer-rpc-http.testnet5.stages.humanode.io"], "faucets": ["https://t.me/HumanodeTestnet5FaucetBot"], "infoURL": "https://humanode.io"}, {"name": "Immutable zkEVM Devnet", "chainId": 15003, "shortName": "imx-devnet", "networkId": 15003, "nativeCurrency": {"name": "Dev IMX", "symbol": "dIMX", "decimals": 18}, "rpc": ["https://rpc.dev.immutable.com"], "faucets": ["https://docs.immutable.com/docs/zkEVM/guides/faucet"], "infoURL": "https://www.immutable.com"}, {"name": "LoopNetwork Mainnet", "chainId": 15551, "shortName": "loop", "networkId": 15551, "nativeCurrency": {"name": "LOOP", "symbol": "LOOP", "decimals": 18}, "rpc": ["https://api.mainnetloop.com"], "faucets": [], "infoURL": "http://theloopnetwork.org/"}, {"name": "Trust EVM Testnet", "chainId": 15555, "shortName": "TrustTestnet", "networkId": 15555, "nativeCurrency": {"name": "Trust EVM", "symbol": "EVM", "decimals": 18}, "rpc": ["https://api.testnet-dev.trust.one"], "faucets": ["https://faucet.testnet-dev.trust.one/"], "infoURL": "https://www.trust.one/"}, {"name": "EOS EVM Network Testnet", "chainId": 15557, "shortName": "eos-testnet", "networkId": 15557, "nativeCurrency": {"name": "EOS", "symbol": "EOS", "decimals": 18}, "rpc": ["https://api.testnet.evm.eosnetwork.com"], "faucets": [], "infoURL": "https://eosnetwork.com/eos-evm"}, {"name": "MetaDot Mainnet", "chainId": 16000, "shortName": "mtt", "networkId": 16000, "nativeCurrency": {"name": "MetaDot Token", "symbol": "MTT", "decimals": 18}, "rpc": ["https://mainnet.metadot.network"], "faucets": [], "infoURL": "https://metadot.network"}, {"name": "MetaDot Testnet", "chainId": 16001, "shortName": "mtttest", "networkId": 16001, "nativeCurrency": {"name": "MetaDot Token TestNet", "symbol": "MTTest", "decimals": 18}, "rpc": ["https://testnet.metadot.network"], "faucets": ["https://faucet.metadot.network/"], "infoURL": "https://metadot.network"}, {"name": "Genesys Mainnet", "chainId": 16507, "shortName": "Genesys", "networkId": 16507, "nativeCurrency": {"name": "Genesys", "symbol": "GSYS", "decimals": 18}, "rpc": ["https://rpc.genesys.network"], "faucets": [], "infoURL": "https://www.genesys.network/"}, {"name": "IRIShub Testnet", "chainId": 16688, "shortName": "nyancat", "networkId": 16688, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ERIS", "decimals": 18}, "rpc": ["https://evmrpc.nyancat.irisnet.org"], "faucets": [], "infoURL": "https://www.irisnet.org"}, {"name": "AirDAO Mainnet", "chainId": 16718, "shortName": "airdao", "networkId": 16718, "nativeCurrency": {"name": "Amber", "symbol": "AMB", "decimals": 18}, "rpc": ["https://network.ambrosus.io"], "faucets": [], "infoURL": "https://airdao.io"}, {"name": "IVAR Chain Testnet", "chainId": 16888, "shortName": "tivar", "networkId": 16888, "nativeCurrency": {"name": "tIvar", "symbol": "tIVAR", "decimals": 18}, "rpc": ["https://testnet-rpc.ivarex.com"], "faucets": ["https://tfaucet.ivarex.com/"], "infoURL": "https://ivarex.com"}, {"name": "<PERSON><PERSON>", "chainId": 17000, "shortName": "holesky", "networkId": 17000, "nativeCurrency": {"name": "Testnet ETH", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.holesky.ethpandaops.io", "https://ethereum-holesky-rpc.publicnode.com", "wss://ethereum-holesky-rpc.publicnode.com", "https://holesky.drpc.org", "wss://holesky.drpc.org"], "faucets": ["https://faucet.holesky.ethpandaops.io", "https://holesky-faucet.pk910.de"], "infoURL": "https://holesky.ethpandaops.io"}, {"name": "Redstone Holesky Testnet", "chainId": 17001, "shortName": "redstone-holesky", "networkId": 17001, "nativeCurrency": {"name": "Redstone Testnet Ether", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.holesky.redstone.xyz"], "faucets": [], "infoURL": "https://redstone.xyz/docs/network-info"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 17069, "shortName": "garnet", "networkId": 17069, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.garnetchain.com", "wss://rpc.garnetchain.com"], "faucets": [], "infoURL": "https://redstone.xyz"}, {"name": "G8Chain Mainnet", "chainId": 17171, "shortName": "G8Cm", "networkId": 17171, "nativeCurrency": {"name": "G8Chain", "symbol": "G8C", "decimals": 18}, "rpc": ["https://mainnet-rpc.oneg8.network"], "faucets": ["https://faucet.oneg8.network"], "infoURL": "https://oneg8.one"}, {"name": "Eclipse Subnet", "chainId": 17172, "shortName": "eclipse", "networkId": 17172, "nativeCurrency": {"name": "Eclipse", "symbol": "ECLP", "decimals": 16}, "rpc": ["https://subnets.avax.network/eclipse/testnet/rpc"], "faucets": [], "infoURL": "http://eclipsenet.io"}, {"name": "Palette Chain Testnet", "chainId": 17180, "shortName": "PCT", "networkId": 17180, "nativeCurrency": {"name": "Pa<PERSON>", "symbol": "PLT", "decimals": 18}, "rpc": ["https://palette-opennet.com:22000"], "faucets": [], "infoURL": "https://hashpalette.com/"}, {"name": "KONET Mainnet", "chainId": 17217, "shortName": "KONET", "networkId": 17217, "nativeCurrency": {"name": "KONET", "symbol": "KONET", "decimals": 18}, "rpc": ["https://api.kon-wallet.com"], "faucets": [], "infoURL": "https://konetmain.com"}, {"name": "EOS EVM Network", "chainId": 17777, "shortName": "eos", "networkId": 17777, "nativeCurrency": {"name": "EOS", "symbol": "EOS", "decimals": 18}, "rpc": ["https://api.evm.eosnetwork.com"], "faucets": [], "infoURL": "https://eosnetwork.com/eos-evm"}, {"name": "Frontier of Dreams Testnet", "chainId": 18000, "shortName": "ZKST", "networkId": 18000, "nativeCurrency": {"name": "ZKST", "symbol": "ZKST", "decimals": 18}, "rpc": ["https://rpc.fod.games/"], "faucets": [], "infoURL": "https://goexosphere.com"}, {"name": "Smart Trade Networks", "chainId": 18122, "shortName": "STN", "networkId": 18122, "nativeCurrency": {"name": "STN", "symbol": "STN", "decimals": 18}, "rpc": ["https://beefledgerwallet.com:8544"], "faucets": [], "infoURL": "https://www.smarttradenetworks.com"}, {"name": "Proof Of Memes", "chainId": 18159, "shortName": "pom", "networkId": 18159, "nativeCurrency": {"name": "Proof Of Memes", "symbol": "POM", "decimals": 18}, "rpc": ["https://mainnet-rpc.memescan.io", "https://mainnet-rpc2.memescan.io", "https://mainnet-rpc3.memescan.io", "https://mainnet-rpc4.memescan.io"], "faucets": [], "infoURL": "https://proofofmemes.org"}, {"name": "G8Chain Testnet", "chainId": 18181, "shortName": "G8Ct", "networkId": 18181, "nativeCurrency": {"name": "G8Coin", "symbol": "G8C", "decimals": 18}, "rpc": ["https://testnet-rpc.oneg8.network"], "faucets": ["https://faucet.oneg8.network"], "infoURL": "https://oneg8.one"}, {"name": "unreal-old", "chainId": 18231, "shortName": "unreal-old", "networkId": 18231, "nativeCurrency": {"name": "unreal Ether", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.unreal.gelato.digital", "wss://ws.unreal.gelato.digital"], "faucets": [], "infoURL": "https://raas.gelato.network/rollups/details/public/unreal"}, {"name": "unreal", "chainId": 18233, "shortName": "unreal", "networkId": 18233, "nativeCurrency": {"name": "unreal Ether", "symbol": "reETH", "decimals": 18}, "rpc": ["https://rpc.unreal-orbit.gelato.digital", "wss://ws.unreal-orbit.gelato.digital"], "faucets": [], "infoURL": "https://raas.gelato.network/rollups/details/public/unreal"}, {"name": "MXC zkEVM Moonchain", "chainId": 18686, "shortName": "MXCzkEVM", "networkId": 18686, "nativeCurrency": {"name": "MXC zkEVM Moonchain", "symbol": "MXC", "decimals": 18}, "rpc": ["https://rpc.mxc.com"], "faucets": [], "infoURL": "https://doc.mxc.com/docs/intro"}, {"name": "Titan (TKX)", "chainId": 18888, "shortName": "titan_tkx", "networkId": 18888, "nativeCurrency": {"name": "Titan tkx", "symbol": "TKX", "decimals": 18}, "rpc": ["https://titan-json-rpc.titanlab.io", "https://titan-json-rpc-tokyo.titanlab.io", "https://titan-json-rpc-seoul.titanlab.io", "https://titan-json-rpc-hongkong.titanlab.io"], "faucets": [], "infoURL": "https://titanlab.io"}, {"name": "Titan (TKX) Testnet", "chainId": 18889, "shortName": "titan_tkx-testnet", "networkId": 18889, "nativeCurrency": {"name": "Titan tkx", "symbol": "TKX", "decimals": 18}, "rpc": ["https://titan-testnet-json-rpc.titanlab.io", "https://titan-testnet-json-rpc-1.titanlab.io", "https://titan-testnet-json-rpc-2.titanlab.io"], "faucets": [], "infoURL": "https://titanlab.io"}, {"name": "HOME Verse Mainnet", "chainId": 19011, "shortName": "HMV", "networkId": 19011, "nativeCurrency": {"name": "OAS", "symbol": "OAS", "decimals": 18}, "rpc": ["https://rpc.mainnet.oasys.homeverse.games/"], "faucets": [], "infoURL": "https://www.homeverse.games/"}, {"name": "Decentraconnect Social", "chainId": 19224, "shortName": "DCSMs", "networkId": 19224, "nativeCurrency": {"name": "Decentraconnect Social", "symbol": "DCSM", "decimals": 18}, "rpc": ["https://rpc.decentraconnect.io"], "faucets": [], "infoURL": "https://docs.decentraconnect.io"}, {"name": "Magnet Network", "chainId": 19527, "shortName": "mgt", "networkId": 19527, "nativeCurrency": {"name": "Magnet Network", "symbol": "DOT", "decimals": 18}, "rpc": ["https://magnet-rpc.magport.io/"], "faucets": [], "infoURL": "https://magnet.magport.io/"}, {"name": "LBRY Mainnet", "chainId": 19600, "shortName": "LBRY", "networkId": 19600, "nativeCurrency": {"name": "LBRY Credits", "symbol": "LBC", "decimals": 8}, "rpc": ["https://lbry.nl/rpc"], "faucets": [], "infoURL": "https://lbry.com"}, {"name": "BTCIX Network", "chainId": 19845, "shortName": "btcix", "networkId": 19845, "nativeCurrency": {"name": "BTCIX Network", "symbol": "BTCIX", "decimals": 18}, "rpc": ["https://seed.btcix.org/rpc"], "faucets": [], "infoURL": "https://bitcolojix.org"}, {"name": "Camelark Mainnet", "chainId": 20001, "shortName": "<PERSON><PERSON><PERSON>", "networkId": 20001, "nativeCurrency": {"name": "EthereumPoW", "symbol": "ETHW", "decimals": 18}, "rpc": ["https://mainnet-http-rpc.camelark.com"], "faucets": [], "infoURL": "https://www.camelark.com"}, {"name": "Callisto Testnet", "chainId": 20729, "shortName": "CLOTestnet", "networkId": 79, "nativeCurrency": {"name": "Callisto", "symbol": "CLO", "decimals": 18}, "rpc": ["https://testnet-rpc.callisto.network/"], "faucets": ["https://faucet.callisto.network/"], "infoURL": "https://callisto.network"}, {"name": "P12 Chain", "chainId": 20736, "shortName": "p12", "networkId": 20736, "nativeCurrency": {"name": "Hooked P2", "symbol": "hP2", "decimals": 18}, "rpc": ["https://rpc-chain.p12.games"], "faucets": [], "infoURL": "https://p12.network"}, {"name": "Jono11 Subnet", "chainId": 20765, "shortName": "jono11", "networkId": 20765, "nativeCurrency": {"name": "Jono11 Token", "symbol": "JONO", "decimals": 18}, "rpc": ["https://subnets.avax.network/jono11/testnet/rpc"], "faucets": [], "infoURL": ""}, {"name": "C4EI", "chainId": 21004, "shortName": "c4ei", "networkId": 21004, "nativeCurrency": {"name": "C4EI", "symbol": "C4EI", "decimals": 18}, "rpc": ["https://rpc.c4ei.net"], "faucets": ["https://play.google.com/store/apps/details?id=net.c4ei.fps2"], "infoURL": "https://c4ei.net"}, {"name": "All About Healthy", "chainId": 21133, "shortName": "aah", "networkId": 21133, "nativeCurrency": {"name": "AAH", "symbol": "AAH", "decimals": 18}, "rpc": ["https://rpc.c4ex.net"], "faucets": ["https://t.me/c4eiAirdrop"], "infoURL": "https://c4ex.net"}, {"name": "CENNZnet Azalea", "chainId": 21337, "shortName": "cennz-a", "networkId": 21337, "nativeCurrency": {"name": "CPAY", "symbol": "CPAY", "decimals": 18}, "rpc": ["https://cennznet.unfrastructure.io/public"], "faucets": [], "infoURL": "https://cennz.net"}, {"name": "omChain <PERSON>", "chainId": 21816, "shortName": "omc", "networkId": 21816, "nativeCurrency": {"name": "<PERSON>m<PERSON><PERSON><PERSON>", "symbol": "OMC", "decimals": 18}, "rpc": ["https://seed.omchain.io"], "faucets": [], "infoURL": "https://omchain.io"}, {"name": "BSL Mainnet", "chainId": 21912, "shortName": "onf", "networkId": 21912, "nativeCurrency": {"name": "Origin NFT", "symbol": "ONF", "decimals": 18}, "rpc": ["http://rpc-mainnet.nftruth.io:8545", "ws://rpc-mainnet.nftruth.io:8645"], "faucets": [], "infoURL": "https://bsquarelab.com/"}, {"name": "Taycan", "chainId": 22023, "shortName": "SFL", "networkId": 22023, "nativeCurrency": {"name": "shuffle", "symbol": "SFL", "decimals": 18}, "rpc": ["https://taycan-rpc.hupayx.io:8545"], "faucets": [], "infoURL": "https://hupayx.io"}, {"name": "AirDAO Testnet", "chainId": 22040, "shortName": "airdao-test", "networkId": 22040, "nativeCurrency": {"name": "Amber", "symbol": "AMB", "decimals": 18}, "rpc": ["https://network.ambrosus-test.io"], "faucets": [], "infoURL": "https://testnet.airdao.io"}, {"name": "Nautilus Mainnet", "chainId": 22222, "shortName": "NAUTCHAIN", "networkId": 22222, "nativeCurrency": {"name": "Zebec", "symbol": "ZBC", "decimals": 18}, "rpc": ["https://api.nautilus.nautchain.xyz"], "faucets": [], "infoURL": "https://docs.nautchain.xyz"}, {"name": "GoldXChain Testnet", "chainId": 22324, "shortName": "goldx-testnet", "networkId": 22324, "nativeCurrency": {"name": "GoldX", "symbol": "GOLDX", "decimals": 18}, "rpc": ["https://testnet-rpc.goldxchain.io"], "faucets": ["https://faucet.goldxchain.io"], "infoURL": "https://goldxchain.io"}, {"name": "MAP Protocol", "chainId": 22776, "shortName": "mapo", "networkId": 22776, "nativeCurrency": {"name": "MAPO", "symbol": "MAPO", "decimals": 18}, "rpc": ["https://rpc.maplabs.io"], "faucets": [], "infoURL": "https://mapprotocol.io/"}, {"name": "Antofy Testnet", "chainId": 23006, "shortName": "ABNt", "networkId": 23006, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "ABN", "decimals": 18}, "rpc": ["https://testnet-rpc.antofy.io"], "faucets": ["https://faucet.antofy.io"], "infoURL": "https://antofy.io"}, {"name": "Opside Testnet", "chainId": 23118, "shortName": "opside", "networkId": 23118, "nativeCurrency": {"name": "IDE", "symbol": "IDE", "decimals": 18}, "rpc": ["https://testrpc.opside.network"], "faucets": ["https://faucet.opside.network"], "infoURL": "https://opside.network"}, {"name": "Oasis Sapphire", "chainId": 23294, "shortName": "sapphire", "networkId": 23294, "nativeCurrency": {"name": "Sapphire Rose", "symbol": "ROSE", "decimals": 18}, "rpc": ["https://sapphire.oasis.io", "wss://sapphire.oasis.io/ws"], "faucets": [], "infoURL": "https://docs.oasis.io/dapp/sapphire"}, {"name": "Oasis Sapphire Testnet", "chainId": 23295, "shortName": "sapphire-testnet", "networkId": 23295, "nativeCurrency": {"name": "Sapphire Test Rose", "symbol": "TEST", "decimals": 18}, "rpc": ["https://testnet.sapphire.oasis.io", "wss://testnet.sapphire.oasis.io/ws"], "faucets": [], "infoURL": "https://docs.oasis.io/dapp/sapphire"}, {"name": "DreyerX Mainnet", "chainId": 23451, "shortName": "d<PERSON><PERSON><PERSON>", "networkId": 23451, "nativeCurrency": {"name": "DreyerX", "symbol": "DRX", "decimals": 18}, "rpc": ["https://rpc.dreyerx.com"], "faucets": [], "infoURL": "https://dreyerx.com"}, {"name": "DreyerX Testnet", "chainId": 23452, "shortName": "dreyerx-testnet", "networkId": 23452, "nativeCurrency": {"name": "DreyerX", "symbol": "DRX", "decimals": 18}, "rpc": ["https://testnet-rpc.dreyerx.com"], "faucets": [], "infoURL": "https://dreyerx.com"}, {"name": "Blast Testnet", "chainId": 23888, "shortName": "blastT", "networkId": 23888, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["http://testnet-rpc.blastblockchain.com"], "faucets": [], "infoURL": "https://docs.blastblockchain.com"}, {"name": "Webchain", "chainId": 24484, "shortName": "web", "networkId": 37129, "nativeCurrency": {"name": "Webchain <PERSON>", "symbol": "WEB", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://webchain.network"}, {"name": "MintMe.com Coin", "chainId": 24734, "shortName": "mintme", "networkId": 37480, "nativeCurrency": {"name": "MintMe.com Coin", "symbol": "MINTME", "decimals": 18}, "rpc": ["https://node1.mintme.com"], "faucets": [], "infoURL": "https://www.mintme.com"}, {"name": "LiquidLayer Mainnet", "chainId": 25186, "shortName": "LILA", "networkId": 25186, "nativeCurrency": {"name": "LiquidLayer", "symbol": "LILA", "decimals": 18}, "rpc": ["https://mainnet.liquidlayer.network"], "faucets": [], "infoURL": "https://scan.liquidlayer.network"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 25839, "shortName": "talv", "networkId": 25839, "nativeCurrency": {"name": "AlveyCoin <PERSON>", "symbol": "tALV", "decimals": 18}, "rpc": ["https://testnet-rpc.alvey.io"], "faucets": ["https://faucet.alveytestnet.com"], "infoURL": "https://alveychain.com/"}, {"name": "Hammer Chain Mainnet", "chainId": 25888, "shortName": "GOLDT", "networkId": 25888, "nativeCurrency": {"name": "GOLDT", "symbol": "GOLDT", "decimals": 18}, "rpc": ["https://www.hammerchain.io/rpc"], "faucets": [], "infoURL": "https://www.hammerchain.io"}, {"name": "Bitkub Chain Testnet", "chainId": 25925, "shortName": "bkct", "networkId": 25925, "nativeCurrency": {"name": "Bitkub Coin", "symbol": "tKUB", "decimals": 18}, "rpc": ["https://rpc-testnet.bitkubchain.io", "wss://wss-testnet.bitkubchain.io"], "faucets": ["https://faucet.bitkubchain.com"], "infoURL": "https://www.bitkubchain.com/"}, {"name": "Ferrum Testnet", "chainId": 26026, "shortName": "frm", "networkId": 26026, "nativeCurrency": {"name": "<PERSON>rrum", "symbol": "tFRM", "decimals": 18}, "rpc": ["http://testnet.dev.svcs.ferrumnetwork.io:9933"], "faucets": ["https://testnet.faucet.ferrumnetwork.io"], "infoURL": "https://ferrum.network"}, {"name": "Hertz Network Mainnet", "chainId": 26600, "shortName": "HTZ", "networkId": 26600, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "HTZ", "decimals": 18}, "rpc": ["https://mainnet-rpc.hertzscan.com"], "faucets": [], "infoURL": "https://www.hertz-network.com"}, {"name": "OasisChain Mainnet", "chainId": 26863, "shortName": "OAC", "networkId": 26863, "nativeCurrency": {"name": "OAC", "symbol": "OAC", "decimals": 18}, "rpc": ["https://rpc1.oasischain.io", "https://rpc2.oasischain.io", "https://rpc3.oasischain.io"], "faucets": ["http://faucet.oasischain.io"], "infoURL": "https://scan.oasischain.io"}, {"name": "KLAOS Nova", "chainId": 27181, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "networkId": 27181, "nativeCurrency": {"name": "KLAOS", "symbol": "KLAOS", "decimals": 18}, "rpc": ["https://rpc.klaosnova.laosfoundation.io", "wss://rpc.klaosnova.laosfoundation.io"], "faucets": [], "infoURL": "https://www.laosfoundation.io/"}, {"name": "Nanon <PERSON>", "chainId": 27483, "shortName": "Nanon-Testnet", "networkId": 27483, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://sepolia-rpc.nanon.network"], "faucets": [], "infoURL": "https://www.nanon.network"}, {"name": "zeroone Mainnet Subnet", "chainId": 27827, "shortName": "zeroonemai", "networkId": 27827, "nativeCurrency": {"name": "ZERO", "symbol": "ZERO", "decimals": 18}, "rpc": ["https://subnets.avax.network/zeroonemai/mainnet/rpc"], "faucets": [], "infoURL": "https://zeroone.art/"}, {"name": "Vizing Testnet", "chainId": 28516, "shortName": "Vizing-Testnet", "networkId": 28516, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-sepolia.vizing.com"], "faucets": [], "infoURL": "https://vizing.com"}, {"name": "Vizing Mainnet", "chainId": 28518, "shortName": "Vizing", "networkId": 28518, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.vizing.com"], "faucets": [], "infoURL": "https://vizing.com"}, {"name": "Optimism Bedrock (Goerli Alpha Testnet)", "chainId": 28528, "shortName": "obgor", "networkId": 28528, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://alpha-1-replica-0.bedrock-goerli.optimism.io", "https://alpha-1-replica-1.bedrock-goerli.optimism.io", "https://alpha-1-replica-2.bedrock-goerli.optimism.io", "https://alpha-1-replica-2.bedrock-goerli.optimism.io"], "faucets": [], "infoURL": "https://community.optimism.io/docs/developers/bedrock"}, {"name": "<PERSON><PERSON>", "chainId": 28882, "shortName": "BobaSep<PERSON>", "networkId": 28882, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://sepolia.boba.network", "https://boba-sepolia.gateway.tenderly.co", "https://gateway.tenderly.co/public/boba-sepolia", "wss://boba-sepolia.gateway.tenderly.co/", "wss://gateway.tenderly.co/public/boba-sepolia"], "faucets": ["https://www.l2faucet.com/boba"], "infoURL": "https://boba.network"}, {"name": "HYCHAIN Testnet", "chainId": 29112, "shortName": "hychain-testnet", "networkId": 29112, "nativeCurrency": {"name": "TOPIA", "symbol": "TOPIA", "decimals": 18}, "rpc": ["https://testnet-rpc.hychain.com/http"], "faucets": [], "infoURL": "https://www.hychain.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 29536, "shortName": "tkec", "networkId": 29536, "nativeCurrency": {"name": "KaiChain Testnet Native Token", "symbol": "KEC", "decimals": 18}, "rpc": ["https://testnet-rpc.kaichain.net"], "faucets": ["https://faucet.kaichain.net"], "infoURL": "https://kaichain.net"}, {"name": "MCH Verse Mainnet", "chainId": 29548, "shortName": "MCHV", "networkId": 29548, "nativeCurrency": {"name": "OAS", "symbol": "OAS", "decimals": 18}, "rpc": ["https://rpc.oasys.mycryptoheroes.net"], "faucets": [], "infoURL": "https://www.mycryptoheroes.net/verse"}, {"name": "Piece testnet", "chainId": 30067, "shortName": "Piece", "networkId": 30067, "nativeCurrency": {"name": "ECE", "symbol": "ECE", "decimals": 18}, "rpc": ["https://testnet-rpc0.piecenetwork.com"], "faucets": ["https://piecenetwork.com/faucet"], "infoURL": "https://piecenetwork.com"}, {"name": "MiYou <PERSON>", "chainId": 30088, "shortName": "<PERSON><PERSON><PERSON>", "networkId": 30088, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "MY", "decimals": 18}, "rpc": ["https://blockchain.miyou.io", "https://blockchain.miyoulab.com"], "faucets": [], "infoURL": "https://www.miyou.io"}, {"name": "Cerium Testnet", "chainId": 30103, "shortName": "ceri", "networkId": 30103, "nativeCurrency": {"name": "Canxium", "symbol": "CAU", "decimals": 18}, "rpc": ["https://cerium-rpc.canxium.net"], "faucets": [], "infoURL": "https://canxium.org"}, {"name": "Movement EVM Legacy", "chainId": 30730, "shortName": "moveleg", "networkId": 30730, "nativeCurrency": {"name": "Move", "symbol": "MOVE", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://movementlabs.xyz"}, {"name": "Movement EVM Devnet", "chainId": 30731, "shortName": "movedev", "networkId": 30731, "nativeCurrency": {"name": "Move", "symbol": "MOVE", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://movementlabs.xyz"}, {"name": "Movement EVM Testnet", "chainId": 30732, "shortName": "movetest", "networkId": 30732, "nativeCurrency": {"name": "Move", "symbol": "MOVE", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://movementlabs.xyz"}, {"name": "Ethersocial Network", "chainId": 31102, "shortName": "esn", "networkId": 1, "nativeCurrency": {"name": "Ethersocial Network Ether", "symbol": "ESN", "decimals": 18}, "rpc": ["https://api.esn.gonspool.com"], "faucets": [], "infoURL": "https://ethersocial.org"}, {"name": "CloudTx Mainnet", "chainId": 31223, "shortName": "CLDTX", "networkId": 31223, "nativeCurrency": {"name": "CloudTx", "symbol": "CLD", "decimals": 18}, "rpc": ["https://mainnet-rpc.cloudtx.finance"], "faucets": [], "infoURL": "https://cloudtx.finance"}, {"name": "CloudTx Testnet", "chainId": 31224, "shortName": "CLD", "networkId": 31224, "nativeCurrency": {"name": "CloudTx", "symbol": "CLD", "decimals": 18}, "rpc": ["https://testnet-rpc.cloudtx.finance"], "faucets": ["https://faucet.cloudtx.finance"], "infoURL": "https://cloudtx.finance/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 31337, "shortName": "got", "networkId": 31337, "nativeCurrency": {"name": "Go<PERSON>hain <PERSON>in", "symbol": "GO", "decimals": 18}, "rpc": ["https://testnet-rpc.gochain.io"], "faucets": [], "infoURL": "https://gochain.io"}, {"name": "Evoke Testnet", "chainId": 31414, "shortName": "tmthn", "networkId": 31414, "nativeCurrency": {"name": "MTHN Testnet", "symbol": "MTHN", "decimals": 18}, "rpc": ["https://testnet-rpc.evokescan.org"], "faucets": ["https://faucet.evokescan.org"], "infoURL": "https://testnet-explorer.evokescan.org"}, {"name": "Filecoin - Wallaby testnet", "chainId": 31415, "shortName": "filecoin-wallaby", "networkId": 31415, "nativeCurrency": {"name": "testnet filecoin", "symbol": "tFIL", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://filecoin.io"}, {"name": "W3Gamez Holesky Testnet", "chainId": 32001, "shortName": "w3gamez", "networkId": 32001, "nativeCurrency": {"name": "W3Gamez Testnet Ether", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-holesky.w3gamez.network"], "faucets": [], "infoURL": "https://web3games.com/"}, {"name": "Bitgert Mainnet", "chainId": 32520, "shortName": "Brise", "networkId": 32520, "nativeCurrency": {"name": "Bitrise Token", "symbol": "Brise", "decimals": 18}, "rpc": ["https://rpc.icecreamswap.com", "https://mainnet-rpc.brisescan.com", "https://chainrpc.com", "https://serverrpc.com"], "faucets": [], "infoURL": "https://bitgert.com/"}, {"name": "Fusion Mainnet", "chainId": 32659, "shortName": "fsn", "networkId": 32659, "nativeCurrency": {"name": "Fusion", "symbol": "FSN", "decimals": 18}, "rpc": ["https://mainnet.fusionnetwork.io", "wss://mainnet.fusionnetwork.io"], "faucets": [], "infoURL": "https://fusion.org"}, {"name": "Zilliqa EVM", "chainId": 32769, "shortName": "zil", "networkId": 32769, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "ZIL", "decimals": 18}, "rpc": ["https://api.zilliqa.com"], "faucets": [], "infoURL": "https://www.zilliqa.com/"}, {"name": "Zilliqa EVM Isolated Server", "chainId": 32990, "shortName": "zil-isolated-server", "networkId": 32990, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "ZIL", "decimals": 18}, "rpc": ["https://zilliqa-isolated-server.zilliqa.com/"], "faucets": ["https://dev-wallet.zilliqa.com/faucet?network=isolated_server"], "infoURL": "https://www.zilliqa.com/"}, {"name": "Zilliqa EVM Testnet", "chainId": 33101, "shortName": "zil-testnet", "networkId": 33101, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "ZIL", "decimals": 18}, "rpc": ["https://dev-api.zilliqa.com"], "faucets": ["https://dev-wallet.zilliqa.com/faucet?network=testnet"], "infoURL": "https://www.zilliqa.com/"}, {"name": "Cloudverse Subnet", "chainId": 33210, "shortName": "cloudverse", "networkId": 33210, "nativeCurrency": {"name": "XCLOUD", "symbol": "XCLOUD", "decimals": 18}, "rpc": ["https://subnets.avax.network/cloudverse/mainnet/rpc"], "faucets": [], "infoURL": "https://muadao.build/"}, {"name": "Aves Mainnet", "chainId": 33333, "shortName": "avs", "networkId": 33333, "nativeCurrency": {"name": "Aves", "symbol": "AVS", "decimals": 18}, "rpc": ["https://rpc.avescoin.io"], "faucets": [], "infoURL": "https://avescoin.io"}, {"name": "Zilliqa EVM Devnet", "chainId": 33385, "shortName": "zil-devnet", "networkId": 33385, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "ZIL", "decimals": 18}, "rpc": ["https://api.devnet.zilliqa.com/"], "faucets": ["https://faucet.devnet.zilliqa.com/"], "infoURL": "https://www.zilliqa.com/"}, {"name": "Zilliqa-2 EVM Devnet", "chainId": 33469, "shortName": "zq2-devnet", "networkId": 33469, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "ZIL", "decimals": 18}, "rpc": ["https://api.zq2-devnet.zilliqa.com"], "faucets": ["https://faucet.zq2-devnet.zilliqa.com"], "infoURL": "https://www.zilliqa.com/"}, {"name": "Mode", "chainId": 34443, "shortName": "mode", "networkId": 34443, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://mainnet.mode.network", "https://mode.drpc.org", "wss://mode.drpc.org"], "faucets": [], "infoURL": "https://docs.mode.network/"}, {"name": "J2O Taro", "chainId": 35011, "shortName": "j2o", "networkId": 35011, "nativeCurrency": {"name": "TARO Coin", "symbol": "taro", "decimals": 18}, "rpc": ["https://rpc.j2o.io"], "faucets": [], "infoURL": "https://j2o.io"}, {"name": "Q Mainnet", "chainId": 35441, "shortName": "q", "networkId": 35441, "nativeCurrency": {"name": "QGOV", "symbol": "QGOV", "decimals": 18}, "rpc": ["https://rpc.q.org"], "faucets": [], "infoURL": "https://q.org"}, {"name": "Q Testnet", "chainId": 35443, "shortName": "q-testnet", "networkId": 35443, "nativeCurrency": {"name": "Q token", "symbol": "Q", "decimals": 18}, "rpc": ["https://rpc.qtestnet.org"], "faucets": [], "infoURL": "https://q.org/"}, {"name": "ConnectorManager", "chainId": 38400, "shortName": "cmrpg", "networkId": 38400, "nativeCurrency": {"name": "Rangers Protocol Gas", "symbol": "cmRPG", "decimals": 18}, "rpc": ["https://cm.rangersprotocol.com/api/jsonrpc"], "faucets": [], "infoURL": "https://rangersprotocol.com"}, {"name": "ConnectorManager Robin", "chainId": 38401, "shortName": "ttrpg", "networkId": 38401, "nativeCurrency": {"name": "Rangers Protocol Gas", "symbol": "ttRPG", "decimals": 18}, "rpc": ["https://robin-cm.rangersprotocol.com/api/jsonrpc"], "faucets": ["https://robin-faucet.rangersprotocol.com"], "infoURL": "https://rangersprotocol.com"}, {"name": "Energi Mainnet", "chainId": 39797, "shortName": "nrg", "networkId": 39797, "nativeCurrency": {"name": "Energi", "symbol": "NRG", "decimals": 18}, "rpc": ["https://nodeapi.energi.network"], "faucets": [], "infoURL": "https://www.energi.world/"}, {"name": "OHO Mainnet", "chainId": 39815, "shortName": "oho", "networkId": 39815, "nativeCurrency": {"name": "OHO", "symbol": "OHO", "decimals": 18}, "rpc": ["https://mainnet.oho.ai"], "faucets": [], "infoURL": "https://oho.ai"}, {"name": "Opulent-X BETA", "chainId": 41500, "shortName": "ox-beta", "networkId": 41500, "nativeCurrency": {"name": "Oxyn Gas", "symbol": "OXYN", "decimals": 18}, "rpc": ["https://connect.opulent-x.com"], "faucets": [], "infoURL": "https://beta.opulent-x.com"}, {"name": "pegglecoin", "chainId": 42069, "shortName": "PC", "networkId": 42069, "nativeCurrency": {"name": "pegglecoin", "symbol": "peggle", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://teampeggle.com"}, {"name": "Arbitrum One", "chainId": 42161, "shortName": "arb1", "networkId": 42161, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://arbitrum-mainnet.infura.io/v3/${INFURA_API_KEY}", "https://arb-mainnet.g.alchemy.com/v2/${ALCHEMY_API_KEY}", "https://arb1.arbitrum.io/rpc", "https://arbitrum-one.publicnode.com", "wss://arbitrum-one.publicnode.com"], "faucets": [], "infoURL": "https://arbitrum.io"}, {"name": "Arbitrum Nova", "chainId": 42170, "shortName": "arb-nova", "networkId": 42170, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://nova.arbitrum.io/rpc", "https://arbitrum-nova.publicnode.com", "wss://arbitrum-nova.publicnode.com"], "faucets": [], "infoURL": "https://arbitrum.io"}, {"name": "Celo <PERSON>net", "chainId": 42220, "shortName": "celo", "networkId": 42220, "nativeCurrency": {"name": "CELO", "symbol": "CELO", "decimals": 18}, "rpc": ["https://forno.celo.org", "wss://forno.celo.org/ws"], "faucets": [], "infoURL": "https://docs.celo.org/"}, {"name": "Oasis Emerald Testnet", "chainId": 42261, "shortName": "emerald-testnet", "networkId": 42261, "nativeCurrency": {"name": "Emerald Rose", "symbol": "ROSE", "decimals": 18}, "rpc": ["https://testnet.emerald.oasis.io/", "wss://testnet.emerald.oasis.io/ws"], "faucets": ["https://faucet.testnet.oasis.io/"], "infoURL": "https://docs.oasis.io/dapp/emerald"}, {"name": "Oasis Emerald", "chainId": 42262, "shortName": "emerald", "networkId": 42262, "nativeCurrency": {"name": "Emerald Rose", "symbol": "ROSE", "decimals": 18}, "rpc": ["https://emerald.oasis.io", "wss://emerald.oasis.io/ws"], "faucets": [], "infoURL": "https://docs.oasis.io/dapp/emerald"}, {"name": "GoldXChain Mainnet", "chainId": 42355, "shortName": "goldx", "networkId": 42355, "nativeCurrency": {"name": "GoldX", "symbol": "GOLDX", "decimals": 18}, "rpc": ["https://mainnet-rpc.goldxchain.io"], "faucets": [], "infoURL": "https://goldxchain.io"}, {"name": "ZKFair Mainnet", "chainId": 42766, "shortName": "ZKFair-Mainnet", "networkId": 42766, "nativeCurrency": {"name": "USDC Token", "symbol": "USDC", "decimals": 18}, "rpc": ["https://rpc.zkfair.io"], "faucets": [], "infoURL": "https://zkfair.io"}, {"name": "Gesoten Verse Testnet", "chainId": 42801, "shortName": "GST", "networkId": 42801, "nativeCurrency": {"name": "OAS", "symbol": "OAS", "decimals": 18}, "rpc": ["https://rpc.testnet.verse.gesoten.com/"], "faucets": [], "infoURL": "https://gesoten.com/"}, {"name": "<PERSON><PERSON>", "chainId": 42888, "shortName": "keth", "networkId": 42888, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["http://**************:8545"], "faucets": [], "infoURL": "https://ethereum.org"}, {"name": "Athereum", "chainId": 43110, "shortName": "avaeth", "networkId": 43110, "nativeCurrency": {"name": "Athereum Ether", "symbol": "ATH", "decimals": 18}, "rpc": ["https://ava.network:21015/ext/evm/rpc"], "faucets": ["http://athfaucet.ava.network//?address=${ADDRESS}"], "infoURL": "https://athereum.ava.network"}, {"name": "Hemi Network", "chainId": 43111, "shortName": "hemi", "networkId": 43111, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://hemi.xyz"}, {"name": "Avalanche Fuji Testnet", "chainId": 43113, "shortName": "Fuji", "networkId": 1, "nativeCurrency": {"name": "Avalanche", "symbol": "AVAX", "decimals": 18}, "rpc": ["https://api.avax-test.network/ext/bc/C/rpc", "https://avalanche-fuji-c-chain-rpc.publicnode.com", "wss://avalanche-fuji-c-chain-rpc.publicnode.com"], "faucets": ["https://faucet.avax-test.network/"], "infoURL": "https://cchain.explorer.avax-test.network"}, {"name": "Avalanche C-Chain", "chainId": 43114, "shortName": "avax", "networkId": 43114, "nativeCurrency": {"name": "Avalanche", "symbol": "AVAX", "decimals": 18}, "rpc": ["https://api.avax.network/ext/bc/C/rpc", "https://avalanche-c-chain-rpc.publicnode.com", "wss://avalanche-c-chain-rpc.publicnode.com"], "faucets": [], "infoURL": "https://www.avax.network/"}, {"name": "<PERSON><PERSON>", "chainId": 43288, "shortName": "bobaavax", "networkId": 43288, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "BOBA", "decimals": 18}, "rpc": ["https://avax.boba.network", "wss://wss.avax.boba.network", "https://replica.avax.boba.network", "wss://replica-wss.avax.boba.network"], "faucets": [], "infoURL": "https://docs.boba.network/for-developers/network-avalanche"}, {"name": "ZKFair Testnet", "chainId": 43851, "shortName": "ZKFair-Testnet", "networkId": 43851, "nativeCurrency": {"name": "USDC Token", "symbol": "USDC", "decimals": 18}, "rpc": ["https://testnet-rpc.zkfair.io"], "faucets": [], "infoURL": "https://zkfair.io"}, {"name": "<PERSON><PERSON>", "chainId": 44444, "shortName": "FREN", "networkId": 44444, "nativeCurrency": {"name": "FREN", "symbol": "FREN", "decimals": 18}, "rpc": ["https://rpc-02.frenscan.io"], "faucets": [], "infoURL": "https://frenchain.app"}, {"name": "Quantum Network", "chainId": 44445, "shortName": "QTM", "networkId": 44445, "nativeCurrency": {"name": "Quantum", "symbol": "QTM", "decimals": 18}, "rpc": ["https://rpcqtm.avescoin.io"], "faucets": [], "infoURL": "https://avescoin.io/"}, {"name": "<PERSON><PERSON>", "chainId": 44787, "shortName": "ALFA", "networkId": 44787, "nativeCurrency": {"name": "CELO", "symbol": "CELO", "decimals": 18}, "rpc": ["https://alfajores-forno.celo-testnet.org", "wss://alfajores-forno.celo-testnet.org/ws"], "faucets": ["https://celo.org/developers/faucet", "https://cauldron.pretoriaresearchlab.io/alfajores-faucet"], "infoURL": "https://docs.celo.org/"}, {"name": "Autobahn Network", "chainId": 45000, "shortName": "AutobahnNetwork", "networkId": 45000, "nativeCurrency": {"name": "TXL", "symbol": "TXL", "decimals": 18}, "rpc": ["https://rpc.autobahn.network"], "faucets": [], "infoURL": "https://autobahn.network"}, {"name": "Swamps L2", "chainId": 45454, "shortName": "SWP", "networkId": 45454, "nativeCurrency": {"name": "SWP", "symbol": "SWP", "decimals": 18}, "rpc": ["https://swamps.tc.l2aas.com"], "faucets": [], "infoURL": "https://www.swamps.fi"}, {"name": "Deelance Mainnet", "chainId": 45510, "shortName": "dee", "networkId": 45510, "nativeCurrency": {"name": "Deelance", "symbol": "DEE", "decimals": 18}, "rpc": ["https://rpc.deelance.com"], "faucets": ["https://faucet.deelance.com"], "infoURL": "https://deelance.com"}, {"name": "Fusion Testnet", "chainId": 46688, "shortName": "tfsn", "networkId": 46688, "nativeCurrency": {"name": "Testnet Fusion", "symbol": "T-FSN", "decimals": 18}, "rpc": ["https://testnet.fusionnetwork.io", "wss://testnet.fusionnetwork.io"], "faucets": [], "infoURL": "https://fusion.org"}, {"name": "REI Network", "chainId": 47805, "shortName": "REI", "networkId": 47805, "nativeCurrency": {"name": "REI", "symbol": "REI", "decimals": 18}, "rpc": ["https://rpc.rei.network", "wss://rpc.rei.network"], "faucets": [], "infoURL": "https://rei.network/"}, {"name": "Wireshape Floripa Testnet", "chainId": 49049, "shortName": "floripa", "networkId": 49049, "nativeCurrency": {"name": "WIRE", "symbol": "WIRE", "decimals": 18}, "rpc": ["https://rpc-floripa.wireshape.org", "https://wireshape-floripa-testnet.rpc.thirdweb.com"], "faucets": [], "infoURL": "https://wireshape.org"}, {"name": "Bifrost Testnet", "chainId": 49088, "shortName": "tbfc", "networkId": 49088, "nativeCurrency": {"name": "Bifrost", "symbol": "BFC", "decimals": 18}, "rpc": ["https://public-01.testnet.bifrostnetwork.com/rpc", "https://public-02.testnet.bifrostnetwork.com/rpc"], "faucets": [], "infoURL": "https://bifrostnetwork.com"}, {"name": "GUNZ Testnet", "chainId": 49321, "shortName": "Stork", "networkId": 49321, "nativeCurrency": {"name": "GUN", "symbol": "GUN", "decimals": 18}, "rpc": ["https://rpc.gunz.dev/ext/bc/ryk9vkvNuKtewME2PeCgybo9sdWXGmCkBrrx4VPuZPdVdAak8/rpc"], "faucets": [], "infoURL": "https://gunbygunz.com"}, {"name": "Energi Testnet", "chainId": 49797, "shortName": "tnrg", "networkId": 49797, "nativeCurrency": {"name": "Energi", "symbol": "NRG", "decimals": 18}, "rpc": ["https://nodeapi.test.energi.network"], "faucets": [], "infoURL": "https://www.energi.world/"}, {"name": "Liveplex OracleEVM", "chainId": 50001, "shortName": "LOE", "networkId": 50001, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.oracle.liveplex.io"], "faucets": [], "infoURL": ""}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 50005, "shortName": "YVM", "networkId": 50005, "nativeCurrency": {"name": "OAS", "symbol": "OAS", "decimals": 18}, "rpc": ["https://rpc.yooldo-verse.xyz/"], "faucets": [], "infoURL": "https://yooldo.gg/"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 50006, "shortName": "YVT", "networkId": 50006, "nativeCurrency": {"name": "OAS", "symbol": "OAS", "decimals": 18}, "rpc": ["https://rpc.testnet.yooldo-verse.xyz/"], "faucets": [], "infoURL": "https://yooldo.gg/"}, {"name": "GTON Testnet", "chainId": 50021, "shortName": "tgton", "networkId": 50021, "nativeCurrency": {"name": "GCD", "symbol": "GCD", "decimals": 18}, "rpc": ["https://testnet.gton.network/"], "faucets": [], "infoURL": "https://gton.capital"}, {"name": "Lumoz Testnet Alpha", "chainId": 51178, "shortName": "Lumoz-Testnet", "networkId": 51178, "nativeCurrency": {"name": "Lumoz Test Token", "symbol": "MOZ", "decimals": 18}, "rpc": ["https://alpha-us-http-geth.lumoz.org", "https://alpha-hk-http-geth.lumoz.org"], "faucets": [], "infoURL": "https://lumoz.org"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 51712, "shortName": "SRDXm", "networkId": 51712, "nativeCurrency": {"name": "<PERSON>rdis", "symbol": "SRDX", "decimals": 18}, "rpc": ["https://mainnet-rpc.sardisnetwork.com"], "faucets": ["https://faucet.sardisnetwork.com"], "infoURL": "https://mysardis.com"}, {"name": "Electroneum Mainnet", "chainId": 52014, "shortName": "etn-mainnet", "networkId": 52014, "nativeCurrency": {"name": "Electroneum", "symbol": "ETN", "decimals": 18}, "rpc": ["https://rpc.electroneum.com"], "faucets": [], "infoURL": "https://electroneum.com"}, {"name": "DOID", "chainId": 53277, "shortName": "DOID", "networkId": 53277, "nativeCurrency": {"name": "DOID", "symbol": "DOID", "decimals": 18}, "rpc": ["https://rpc.doid.tech"], "faucets": [], "infoURL": "https://doid.tech"}, {"name": "DODOchain testnet", "chainId": 53457, "shortName": "dodochain", "networkId": 53457, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "BERD", "decimals": 18}, "rpc": ["https://dodochain-testnet.alt.technology", "wss://dodochain-testnet.alt.technology/ws"], "faucets": [], "infoURL": "https://www.dodochain.com"}, {"name": "DFK Chain", "chainId": 53935, "shortName": "DFK", "networkId": 53935, "nativeCurrency": {"name": "Jewel", "symbol": "JEWEL", "decimals": 18}, "rpc": ["https://subnets.avax.network/defi-kingdoms/dfk-chain/rpc"], "faucets": [], "infoURL": "https://defikingdoms.com"}, {"name": "Haqq Chain Testnet", "chainId": 54211, "shortName": "ISLMT", "networkId": 54211, "nativeCurrency": {"name": "Islamic Coin", "symbol": "ISLMT", "decimals": 18}, "rpc": ["https://rpc.eth.testedge2.haqq.network"], "faucets": ["https://testedge2.haqq.network"], "infoURL": "https://islamiccoin.net"}, {"name": "Toronet Testnet", "chainId": 54321, "shortName": "ToronetTestnet", "networkId": 54321, "nativeCurrency": {"name": "Toro", "symbol": "TORO", "decimals": 18}, "rpc": ["http://testnet.toronet.org/rpc"], "faucets": [], "infoURL": "https://toronet.org"}, {"name": "Photon Testnet", "chainId": 54555, "shortName": "pton", "networkId": 54555, "nativeCurrency": {"name": "Photon", "symbol": "PTON", "decimals": 18}, "rpc": ["https://rpc-test.photonchain.io"], "faucets": ["https://photonchain.io/airdrop"], "infoURL": "https://photonchain.io"}, {"name": "Titan", "chainId": 55004, "shortName": "teth", "networkId": 55004, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.titan.tokamak.network", "wss://rpc.titan.tokamak.network"], "faucets": [], "infoURL": "https://tokamak.network"}, {"name": "REI Chain Mainnet", "chainId": 55555, "shortName": "<PERSON><PERSON><PERSON>", "networkId": 55555, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "REI", "decimals": 18}, "rpc": ["https://rei-rpc.moonrhythm.io"], "faucets": ["http://kururu.finance/faucet?chainId=55555"], "infoURL": "https://reichain.io"}, {"name": "REI Chain Testnet", "chainId": 55556, "shortName": "trei", "networkId": 55556, "nativeCurrency": {"name": "tRei", "symbol": "tREI", "decimals": 18}, "rpc": ["https://rei-testnet-rpc.moonrhythm.io"], "faucets": ["http://kururu.finance/faucet?chainId=55556"], "infoURL": "https://reichain.io"}, {"name": "Lambda Chain Mainnet", "chainId": 56026, "shortName": "lambda", "networkId": 56026, "nativeCurrency": {"name": "ETH", "symbol": "ETH", "decimals": 18}, "rpc": ["https://nrpc.lambda.im/"], "faucets": [], "infoURL": "https://lambda.im"}, {"name": "Boba BNB Mainnet", "chainId": 56288, "shortName": "BobaBnb", "networkId": 56288, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "BOBA", "decimals": 18}, "rpc": ["https://bnb.boba.network", "https://boba-bnb.gateway.tenderly.co/", "https://gateway.tenderly.co/public/boba-bnb", "https://replica.bnb.boba.network", "wss://boba-bnb.gateway.tenderly.co/", "wss://gateway.tenderly.co/public/boba-bnb"], "faucets": [], "infoURL": "https://boba.network"}, {"name": "Testnet Zeroone Subnet", "chainId": 56400, "shortName": "testnetzer", "networkId": 56400, "nativeCurrency": {"name": "ZERO", "symbol": "ZERO", "decimals": 18}, "rpc": ["https://subnets.avax.network/testnetzer/testnet/rpc"], "faucets": [], "infoURL": "https://zeroone.art/"}, {"name": "VELO Labs Mainnet", "chainId": 56789, "shortName": "VELO", "networkId": 56789, "nativeCurrency": {"name": "Nova", "symbol": "NOVA", "decimals": 18}, "rpc": ["https://nova.velo.org"], "faucets": ["https://nova-faucet.velo.org"], "infoURL": "https://velo.org"}, {"name": "DOID Testnet", "chainId": 56797, "shortName": "doidTestnet", "networkId": 56797, "nativeCurrency": {"name": "DOID", "symbol": "DOID", "decimals": 18}, "rpc": ["https://rpc.testnet.doid.tech"], "faucets": [], "infoURL": "https://doid.tech"}, {"name": "Rollux Testnet", "chainId": 57000, "shortName": "tsys-rollux", "networkId": 57000, "nativeCurrency": {"name": "Testnet Syscoin", "symbol": "TSYS", "decimals": 18}, "rpc": ["https://rpc-tanenbaum.rollux.com", "https://rpc.ankr.com/rollux_testnet/${ANKR_API_KEY}", "wss://rpc-tanenbaum.rollux.com/wss", "https://rollux.rpc.tanenbaum.io", "wss://rollux.rpc.tanenbaum.io/wss"], "faucets": ["https://rollux.id/faucetapp"], "infoURL": "https://rollux.com"}, {"name": "Sepolia PGN (Public Goods Network)", "chainId": 58008, "shortName": "sepPGN", "networkId": 58008, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://sepolia.publicgoods.network"], "faucets": [], "infoURL": "https://publicgoods.network/"}, {"name": "Linea Goerli", "chainId": 59140, "shortName": "linea-goerli", "networkId": 59140, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.goerli.linea.build", "wss://rpc.goerli.linea.build", "https://linea-goerli.infura.io/v3/${INFURA_API_KEY}", "wss://linea-goerli.infura.io/ws/v3/${INFURA_API_KEY}"], "faucets": ["https://faucetlink.to/goerli"], "infoURL": "https://linea.build"}, {"name": "Linea Sepolia", "chainId": 59141, "shortName": "linea-sepolia", "networkId": 59141, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.sepolia.linea.build", "wss://rpc.sepolia.linea.build", "https://linea-sepolia.infura.io/v3/${INFURA_API_KEY}", "wss://linea-sepolia.infura.io/ws/v3/${INFURA_API_KEY}"], "faucets": [], "infoURL": "https://linea.build"}, {"name": "Linea", "chainId": 59144, "shortName": "linea", "networkId": 59144, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.linea.build", "wss://rpc.linea.build", "https://linea-mainnet.infura.io/v3/${INFURA_API_KEY}", "wss://linea-mainnet.infura.io/ws/v3/${INFURA_API_KEY}"], "faucets": [], "infoURL": "https://linea.build"}, {"name": "Genesys Code Mainnet", "chainId": 59971, "shortName": "gcode", "networkId": 59971, "nativeCurrency": {"name": "GenesysCode", "symbol": "GCODE", "decimals": 18}, "rpc": ["https://mainnet.genesyscode.io/"], "faucets": [], "infoURL": "https://genesyscode.io"}, {"name": "Thinkium Testnet Chain 0", "chainId": 60000, "shortName": "TKM-test0", "networkId": 60000, "nativeCurrency": {"name": "TKM", "symbol": "TKM", "decimals": 18}, "rpc": ["https://test.thinkiumrpc.net/"], "faucets": ["https://www.thinkiumdev.net/faucet"], "infoURL": "https://thinkium.net/"}, {"name": "Thinkium Testnet Chain 1", "chainId": 60001, "shortName": "TKM-test1", "networkId": 60001, "nativeCurrency": {"name": "TKM", "symbol": "TKM", "decimals": 18}, "rpc": ["https://test1.thinkiumrpc.net/"], "faucets": ["https://www.thinkiumdev.net/faucet"], "infoURL": "https://thinkium.net/"}, {"name": "Thinkium Testnet Chain 2", "chainId": 60002, "shortName": "TKM-test2", "networkId": 60002, "nativeCurrency": {"name": "TKM", "symbol": "TKM", "decimals": 18}, "rpc": ["https://test2.thinkiumrpc.net/"], "faucets": ["https://www.thinkiumdev.net/faucet"], "infoURL": "https://thinkium.net/"}, {"name": "Thinkium Testnet Chain 103", "chainId": 60103, "shortName": "TKM-test103", "networkId": 60103, "nativeCurrency": {"name": "TKM", "symbol": "TKM", "decimals": 18}, "rpc": ["https://test103.thinkiumrpc.net/"], "faucets": ["https://www.thinkiumdev.net/faucet"], "infoURL": "https://thinkium.net/"}, {"name": "BOB", "chainId": 60808, "shortName": "bob", "networkId": 60808, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.gobob.xyz", "wss://rpc.gobob.xyz"], "faucets": [], "infoURL": "https://gobob.xyz"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 61406, "shortName": "kec", "networkId": 61406, "nativeCurrency": {"name": "KaiChain Native Token", "symbol": "KEC", "decimals": 18}, "rpc": ["https://mainnet-rpc.kaichain.net"], "faucets": [], "infoURL": "https://kaichain.net"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 61800, "shortName": "aium-dev", "networkId": 61800, "nativeCurrency": {"name": "Axelium", "symbol": "AIUM", "decimals": 18}, "rpc": ["https://aium-rpc-dev.viacube.com"], "faucets": [], "infoURL": "https://www.axel.org"}, {"name": "Etica Mainnet", "chainId": 61803, "shortName": "Etica", "networkId": 61803, "nativeCurrency": {"name": "EGAZ", "symbol": "EGAZ", "decimals": 18}, "rpc": ["https://eticamainnet.eticascan.org", "https://eticamainnet.eticaprotocol.org"], "faucets": ["http://faucet.etica-stats.org/"], "infoURL": "https://eticaprotocol.org"}, {"name": "DoKEN Super Chain Mainnet", "chainId": 61916, "shortName": "DoKEN", "networkId": 61916, "nativeCurrency": {"name": "DoKEN", "symbol": "DKN", "decimals": 18}, "rpc": ["https://sgrpc.doken.dev", "https://nyrpc.doken.dev", "https://ukrpc.doken.dev"], "faucets": [], "infoURL": "https://doken.dev/"}, {"name": "OPTOPIA Testnet", "chainId": 62049, "shortName": "OPTOPIA-Testnet", "networkId": 62049, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-testnet.optopia.ai"], "faucets": [], "infoURL": "https://optopia.ai"}, {"name": "Optopia Mainnet", "chainId": 62050, "shortName": "Optopia", "networkId": 62050, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://optopia.ai"}, {"name": "<PERSON><PERSON> Testnet", "chainId": 62320, "shortName": "BKLV", "networkId": 62320, "nativeCurrency": {"name": "CELO", "symbol": "CELO", "decimals": 18}, "rpc": ["https://baklava-forno.celo-testnet.org"], "faucets": ["https://docs.google.com/forms/d/e/1FAIpQLSdfr1BwUTYepVmmvfVUDRCwALejZ-TUva2YujNpvrEmPAX2pg/viewform", "https://cauldron.pretoriaresearchlab.io/baklava-faucet"], "infoURL": "https://docs.celo.org/"}, {"name": "MultiVAC Mainnet", "chainId": 62621, "shortName": "mtv", "networkId": 62621, "nativeCurrency": {"name": "MultiVAC", "symbol": "MTV", "decimals": 18}, "rpc": ["https://rpc.mtv.ac", "https://rpc-eu.mtv.ac"], "faucets": [], "infoURL": "https://mtv.ac"}, {"name": "eCredits Mainnet", "chainId": 63000, "shortName": "ecs", "networkId": 63000, "nativeCurrency": {"name": "eCredits", "symbol": "ECS", "decimals": 18}, "rpc": ["https://rpc.ecredits.com"], "faucets": [], "infoURL": "https://ecredits.com"}, {"name": "eCredits Testnet", "chainId": 63001, "shortName": "ecs-testnet", "networkId": 63001, "nativeCurrency": {"name": "eCredits", "symbol": "ECS", "decimals": 18}, "rpc": ["https://rpc.tst.ecredits.com"], "faucets": ["https://faucet.tst.ecredits.com"], "infoURL": "https://ecredits.com"}, {"name": "Scolcoin Mainnet", "chainId": 65450, "shortName": "SRC", "networkId": 65450, "nativeCurrency": {"name": "Scolcoin", "symbol": "SCOL", "decimals": 18}, "rpc": ["https://mainnet-rpc.scolcoin.com"], "faucets": [], "infoURL": "https://scolcoin.com"}, {"name": "<PERSON><PERSON>", "chainId": 66988, "shortName": "janusnetwork-testnet", "networkId": 66988, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "JNS", "decimals": 18}, "rpc": ["https://rpc.test.janusnetwork.io"], "faucets": [], "infoURL": "https://janus-network.gitbook.io/janus"}, {"name": "SiriusNet", "chainId": 67390, "shortName": "mcl", "networkId": 67390, "nativeCurrency": {"name": "MCD", "symbol": "MCD", "decimals": 18}, "rpc": ["https://u0tnafcv6j:<EMAIL>/"], "faucets": [], "infoURL": "https://macaucasinolisboa.xyz"}, {"name": "Cosmic Chain", "chainId": 67588, "shortName": "Cosmic", "networkId": 3344, "nativeCurrency": {"name": "Cosmic Chain", "symbol": "COSMIC", "decimals": 18}, "rpc": ["http://testnet.cosmicchain.site:3344"], "faucets": [], "infoURL": "https://cosmicchain.site"}, {"name": "DM2 Verse Mainnet", "chainId": 68770, "shortName": "dm2", "networkId": 68770, "nativeCurrency": {"name": "OAS", "symbol": "OAS", "decimals": 18}, "rpc": ["https://rpc.dm2verse.dmm.com"], "faucets": [], "infoURL": "https://seamoon.dmm.com"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 69420, "shortName": "cndr", "networkId": 69420, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "CTE", "decimals": 18}, "rpc": ["https://rpc.condrieu.ethdevops.io:8545"], "faucets": ["https://faucet.condrieu.ethdevops.io"], "infoURL": "https://condrieu.ethdevops.io"}, {"name": "Thinkium Mainnet Chain 0", "chainId": 70000, "shortName": "TKM0", "networkId": 70000, "nativeCurrency": {"name": "TKM", "symbol": "TKM", "decimals": 18}, "rpc": ["https://proxy.thinkiumrpc.net/"], "faucets": [], "infoURL": "https://thinkium.net/"}, {"name": "Thinkium Mainnet Chain 1", "chainId": 70001, "shortName": "TKM1", "networkId": 70001, "nativeCurrency": {"name": "TKM", "symbol": "TKM", "decimals": 18}, "rpc": ["https://proxy1.thinkiumrpc.net/"], "faucets": [], "infoURL": "https://thinkium.net/"}, {"name": "Thinkium Mainnet Chain 2", "chainId": 70002, "shortName": "TKM2", "networkId": 70002, "nativeCurrency": {"name": "TKM", "symbol": "TKM", "decimals": 18}, "rpc": ["https://proxy2.thinkiumrpc.net/"], "faucets": [], "infoURL": "https://thinkium.net/"}, {"name": "Thinkium Mainnet Chain 103", "chainId": 70103, "shortName": "TKM103", "networkId": 70103, "nativeCurrency": {"name": "TKM", "symbol": "TKM", "decimals": 18}, "rpc": ["https://proxy103.thinkiumrpc.net/"], "faucets": [], "infoURL": "https://thinkium.net/"}, {"name": "Proof of Play - Apex", "chainId": 70700, "shortName": "pop-apex", "networkId": 70700, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.apex.proofofplay.com"], "faucets": [], "infoURL": "https://proofofplay.com"}, {"name": "GuapcoinX", "chainId": 71111, "shortName": "GuapX", "networkId": 71111, "nativeCurrency": {"name": "GuapcoinX", "symbol": "GuapX", "decimals": 18}, "rpc": ["https://rpc-mainnet.guapcoinx.com/", "https://rpc-mainnet-1.guapcoinx.com/", "https://rpc-mainnet-2.guapcoinx.com/"], "faucets": [], "infoURL": "https://guapcoin.org/"}, {"name": "Polyjuice Testnet", "chainId": 71393, "shortName": "ckb", "networkId": 1, "nativeCurrency": {"name": "CKB", "symbol": "CKB", "decimals": 8}, "rpc": ["https://godwoken-testnet-web3-rpc.ckbapp.dev", "ws://godwoken-testnet-web3-rpc.ckbapp.dev/ws"], "faucets": ["https://faucet.nervos.org/"], "infoURL": "https://github.com/nervosnetwork/godwoken"}, {"name": "Godwoken Testnet v1", "chainId": 71401, "shortName": "gw-testnet-v1", "networkId": 71401, "nativeCurrency": {"name": "pCKB", "symbol": "pCKB", "decimals": 18}, "rpc": ["https://godwoken-testnet-v1.ckbapp.dev", "https://v1.testnet.godwoken.io/rpc"], "faucets": ["https://testnet.bridge.godwoken.io"], "infoURL": "https://www.nervos.org"}, {"name": "Godwoken Mainnet", "chainId": 71402, "shortName": "gw-mainnet-v1", "networkId": 71402, "nativeCurrency": {"name": "pCKB", "symbol": "pCKB", "decimals": 18}, "rpc": ["https://v1.mainnet.godwoken.io/rpc"], "faucets": [], "infoURL": "https://www.nervos.org"}, {"name": "CAGA crypto Ankara testnet", "chainId": 72778, "shortName": "caga", "networkId": 72778, "nativeCurrency": {"name": "Caga", "symbol": "CAGA", "decimals": 18}, "rpc": ["https://www.ankara-cagacrypto.com", "wss://wss.ankara-cagacrypto.com"], "faucets": [], "infoURL": "https://www.cagacrypto.com/"}, {"name": "Grok Chain Mainnet", "chainId": 72992, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "networkId": 72992, "nativeCurrency": {"name": "Groc", "symbol": "GROC", "decimals": 18}, "rpc": ["https://mainnet-rpc.grokchain.dev"], "faucets": [], "infoURL": "https://grokchain.dev"}, {"name": "ICB Testnet", "chainId": 73114, "shortName": "ICBT", "networkId": 73114, "nativeCurrency": {"name": "ICB Testnet Token", "symbol": "ICBT", "decimals": 18}, "rpc": ["https://rpc1-testnet.icbnetwork.info/", "https://rpc2-testnet.icbnetwork.info/"], "faucets": [], "infoURL": "https://icb.network"}, {"name": "ICB Network", "chainId": 73115, "shortName": "ICBX", "networkId": 73115, "nativeCurrency": {"name": "ICB Native Token", "symbol": "ICBX", "decimals": 18}, "rpc": ["https://rpc1-mainnet.icbnetwork.info/", "https://rpc2-mainnet.icbnetwork.info/"], "faucets": [], "infoURL": "https://icb.network"}, {"name": "Energy Web Volta Testnet", "chainId": 73799, "shortName": "vt", "networkId": 73799, "nativeCurrency": {"name": "Volta Token", "symbol": "VT", "decimals": 18}, "rpc": ["https://volta-rpc.energyweb.org", "wss://volta-rpc.energyweb.org/ws"], "faucets": ["https://voltafaucet.energyweb.org"], "infoURL": "https://energyweb.org"}, {"name": "Mixin Virtual Machine", "chainId": 73927, "shortName": "mvm", "networkId": 73927, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://geth.mvm.dev"], "faucets": [], "infoURL": "https://mvm.dev"}, {"name": "ResinCoin Mainnet", "chainId": 75000, "shortName": "resin", "networkId": 75000, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "RESIN", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://resincoin.dev"}, {"name": "BORAchain mainnet", "chainId": 77001, "shortName": "BORAchain", "networkId": 77001, "nativeCurrency": {"name": "BGAS", "symbol": "BGAS", "decimals": 18}, "rpc": ["https://public-node.api.boraportal.com/bora/mainnet"], "faucets": [], "infoURL": "https://www.boraportal.com"}, {"name": "Foundry Chain Testnet", "chainId": 77238, "shortName": "fnc", "networkId": 77238, "nativeCurrency": {"name": "Foundry Chain Testnet", "symbol": "tFNC", "decimals": 18}, "rpc": ["https://testnet-rpc.foundryscan.org/"], "faucets": ["https://faucet.foundryscan.org"], "infoURL": "https://foundrychain.org"}, {"name": "Vention Smart Chain Mainnet", "chainId": 77612, "shortName": "vscm", "networkId": 77612, "nativeCurrency": {"name": "VNT", "symbol": "VNT", "decimals": 18}, "rpc": ["https://mainnet-rpc.vention.network"], "faucets": ["https://faucet.vention.network"], "infoURL": "https://ventionscan.io"}, {"name": "Toronet Mainnet", "chainId": 77777, "shortName": "Toronet", "networkId": 77777, "nativeCurrency": {"name": "Toro", "symbol": "TORO", "decimals": 18}, "rpc": ["http://toronet.org/rpc"], "faucets": [], "infoURL": "https://toronet.org"}, {"name": "Firenze test network", "chainId": 78110, "shortName": "firenze", "networkId": 78110, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "FIN", "decimals": 18}, "rpc": ["https://ethnode.primusmoney.com/firenze"], "faucets": [], "infoURL": "https://primusmoney.com"}, {"name": "Dragonfly Mainnet (Hexapod)", "chainId": 78281, "shortName": "dfly", "networkId": 78281, "nativeCurrency": {"name": "Dragonfly", "symbol": "DFLY", "decimals": 18}, "rpc": ["https://dragonfly-rpc.switch.ch", "https://dragonfly-rpc.kore-technologies.ch", "https://dragonfly-rpc.phoenix-systems.io", "https://dragonfly-rpc.block-spirit.ch"], "faucets": [], "infoURL": "https://hexapod.network"}, {"name": "Amplify Subnet", "chainId": 78430, "shortName": "amplify", "networkId": 78430, "nativeCurrency": {"name": "AMP", "symbol": "AMP", "decimals": 18}, "rpc": ["https://subnets.avax.network/amplify/testnet/rpc"], "faucets": [], "infoURL": "https://www.avax.network"}, {"name": "Bulletin Subnet", "chainId": 78431, "shortName": "bulletin", "networkId": 78431, "nativeCurrency": {"name": "BLT", "symbol": "BLT", "decimals": 18}, "rpc": ["https://subnets.avax.network/bulletin/testnet/rpc"], "faucets": [], "infoURL": "https://www.avax.network"}, {"name": "Conduit Subnet", "chainId": 78432, "shortName": "conduit", "networkId": 78432, "nativeCurrency": {"name": "CON", "symbol": "CON", "decimals": 18}, "rpc": ["https://subnets.avax.network/conduit/testnet/rpc"], "faucets": [], "infoURL": "https://www.avax.network"}, {"name": "Vanguard", "chainId": 78600, "shortName": "vanguard", "networkId": 78600, "nativeCurrency": {"name": "Vanguard Vanry", "symbol": "VANRY", "decimals": 18}, "rpc": ["https://rpc-vanguard.vanarchain.com", "wss://ws-vanguard.vanarchain.com"], "faucets": ["https://faucet.vanarchain.com"], "infoURL": "https://vanarchain.com"}, {"name": "Gold Smart Chain Testnet", "chainId": 79879, "shortName": "STANDt", "networkId": 79879, "nativeCurrency": {"name": "Standard in Gold", "symbol": "STAND", "decimals": 18}, "rpc": ["https://rpc-testnet.goldsmartchain.com"], "faucets": ["https://faucet.goldsmartchain.com"], "infoURL": "https://goldsmartchain.com"}, {"name": "Mumbai", "chainId": 80001, "shortName": "maticmum", "networkId": 80001, "nativeCurrency": {"name": "MATIC", "symbol": "MATIC", "decimals": 18}, "rpc": ["https://rpc-mumbai.maticvigil.com", "https://polygon-mumbai-bor-rpc.publicnode.com", "wss://polygon-mumbai-bor-rpc.publicnode.com", "https://polygon-mumbai.gateway.tenderly.co", "wss://polygon-mumbai.gateway.tenderly.co"], "faucets": ["https://faucet.polygon.technology/"], "infoURL": "https://polygon.technology/"}, {"name": "<PERSON><PERSON>", "chainId": 80002, "shortName": "polygonamoy", "networkId": 80002, "nativeCurrency": {"name": "MATIC", "symbol": "MATIC", "decimals": 18}, "rpc": ["https://rpc-amoy.polygon.technology", "https://polygon-amoy-bor-rpc.publicnode.com", "wss://polygon-amoy-bor-rpc.publicnode.com"], "faucets": ["https://faucet.polygon.technology/"], "infoURL": "https://polygon.technology/"}, {"name": "<PERSON><PERSON><PERSON>n Artio", "chainId": 80085, "shortName": "be<PERSON>hain<PERSON>rtio", "networkId": 80085, "nativeCurrency": {"name": "BERA Token", "symbol": "BERA", "decimals": 18}, "rpc": ["https://artio.rpc.berachain.com", "https://rpc.ankr.com/berachain_testnet"], "faucets": ["https://artio.faucet.berachain.com"], "infoURL": "https://www.berachain.com"}, {"name": "Hizoco mainnet", "chainId": 80096, "shortName": "hzc", "networkId": 80096, "nativeCurrency": {"name": "Hizoco", "symbol": "HZC", "decimals": 18}, "rpc": ["https://hizoco.net/rpc"], "faucets": [], "infoURL": "http://hizoco.net"}, {"name": "Nordek Mainnet", "chainId": 81041, "shortName": "nordek", "networkId": 81041, "nativeCurrency": {"name": "NRK", "symbol": "NRK", "decimals": 18}, "rpc": ["https://mainnet-rpc.nordekscan.com"], "faucets": [], "infoURL": "https://nordekscan.com"}, {"name": "<PERSON><PERSON>", "chainId": 81341, "shortName": "amanatest", "networkId": 81341, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "MEER-T", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://github.com/Qitmeer"}, {"name": "<PERSON>ana <PERSON>", "chainId": 81342, "shortName": "amanamix", "networkId": 81342, "nativeCurrency": {"name": "<PERSON>ana <PERSON>", "symbol": "MEER-M", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://github.com/Qitmeer"}, {"name": "<PERSON><PERSON>", "chainId": 81343, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "networkId": 81343, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "MEER-P", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://github.com/Qitmeer"}, {"name": "<PERSON><PERSON>", "chainId": 81351, "shortName": "flanatest", "networkId": 81351, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "MEER-T", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://github.com/Qitmeer"}, {"name": "<PERSON><PERSON>", "chainId": 81352, "shortName": "flanamix", "networkId": 81352, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "MEER-M", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://github.com/Qitmeer"}, {"name": "<PERSON><PERSON>", "chainId": 81353, "shortName": "flanapriv", "networkId": 81353, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "MEER-P", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://github.com/Qitmeer"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 81361, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "networkId": 81361, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "MEER-T", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://github.com/Qitmeer"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 81362, "shortName": "mizanamix", "networkId": 81362, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "MEER-M", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://github.com/Qitmeer"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 81363, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "networkId": 81363, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "MEER-P", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://github.com/Qitmeer"}, {"name": "Blast", "chainId": 81457, "shortName": "blastmainnet", "networkId": 81457, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.blast.io", "https://rpc.ankr.com/blast", "https://blast.din.dev/rpc", "https://blastl2-mainnet.public.blastapi.io", "https://blast.blockpi.network/v1/rpc/public"], "faucets": [], "infoURL": "https://blast.io/"}, {"name": "Quantum Chain Mainnet", "chainId": 81720, "shortName": "qnet", "networkId": 81720, "nativeCurrency": {"name": "Quantum Chain", "symbol": "QNET", "decimals": 18}, "rpc": ["https://rpc.quantumscan.org"], "faucets": [], "infoURL": "https://quantumnetwork.gg"}, {"name": "Smart Layer Network Testnet", "chainId": 82459, "shortName": "tSLN", "networkId": 82459, "nativeCurrency": {"name": "Service Unit Token", "symbol": "SU", "decimals": 18}, "rpc": ["https://rpc.test.smartlayer.network"], "faucets": [], "infoURL": "https://www.smartlayer.network/"}, {"name": "ZEDXION", "chainId": 83872, "shortName": "ZEDX", "networkId": 83872, "nativeCurrency": {"name": "Zedxion", "symbol": "zedx", "decimals": 9}, "rpc": ["https://mainnet-rpc.zedscan.net"], "faucets": [], "infoURL": "https://docs.zedscan.net"}, {"name": "Base Goerli Testnet", "chainId": 84531, "shortName": "basegor", "networkId": 84531, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://goerli.base.org", "https://base-goerli.gateway.tenderly.co", "wss://base-goerli.gateway.tenderly.co", "https://base-goerli-rpc.publicnode.com", "wss://base-goerli-rpc.publicnode.com"], "faucets": ["https://www.coinbase.com/faucets/base-ethereum-goerli-faucet"], "infoURL": "https://base.org"}, {"name": "Base Sepolia Testnet", "chainId": 84532, "shortName": "basesep", "networkId": 84532, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://sepolia.base.org", "https://base-sepolia-rpc.publicnode.com", "wss://base-sepolia-rpc.publicnode.com"], "faucets": [], "infoURL": "https://base.org"}, {"name": "Aerie Network", "chainId": 84886, "shortName": "Aerie", "networkId": 84886, "nativeCurrency": {"name": "Aerie", "symbol": "AER", "decimals": 18}, "rpc": ["https://mainnet.aerielab.io"], "faucets": [], "infoURL": "https://aerielab.io/"}, {"name": "CYBERTRUST", "chainId": 85449, "shortName": "<PERSON>ber", "networkId": 48501, "nativeCurrency": {"name": "Cyber Trust", "symbol": "CYBER", "decimals": 18}, "rpc": ["http://testnet.cybertrust.space:48501"], "faucets": [], "infoURL": "https://cybertrust.space"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 88002, "shortName": "NAUTTest", "networkId": 88002, "nativeCurrency": {"name": "Zebec Test Token", "symbol": "tZBC", "decimals": 18}, "rpc": ["https://api.proteus.nautchain.xyz/solana"], "faucets": ["https://proteusfaucet.nautchain.xyz"], "infoURL": "https://docs.nautchain.xyz"}, {"name": "Unit Zero Testnet", "chainId": 88817, "shortName": "unit0-testnet", "networkId": 88817, "nativeCurrency": {"name": "UNIT0", "symbol": "UNIT0", "decimals": 18}, "rpc": ["https://rpc-testnet.unit0.dev"], "faucets": [], "infoURL": "https://units.network"}, {"name": "Unit Zero Stagenet", "chainId": 88819, "shortName": "unit0-stagenet", "networkId": 88819, "nativeCurrency": {"name": "UNIT0", "symbol": "UNIT0", "decimals": 18}, "rpc": ["https://rpc-stagenet.unit0.dev"], "faucets": [], "infoURL": "https://units.network"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 88880, "shortName": "chz", "networkId": 88880, "nativeCurrency": {"name": "Chiliz", "symbol": "CHZ", "decimals": 18}, "rpc": ["https://scoville-rpc.chiliz.com"], "faucets": ["https://scoville-faucet.chiliz.com"], "infoURL": "https://www.chiliz.com/en/chain"}, {"name": "IVAR Chain Mainnet", "chainId": 88888, "shortName": "ivar", "networkId": 88888, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "IVAR", "decimals": 18}, "rpc": ["https://mainnet-rpc.ivarex.com"], "faucets": ["https://faucet.ivarex.com/"], "infoURL": "https://ivarex.com"}, {"name": "F(x)Core Testnet Network", "chainId": 90001, "shortName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "networkId": 90001, "nativeCurrency": {"name": "Function X", "symbol": "FX", "decimals": 18}, "rpc": ["https://testnet-fx-json-web3.functionx.io:8545"], "faucets": [], "infoURL": "https://functionx.io/"}, {"name": "Beverly Hills", "chainId": 90210, "shortName": "bvhl", "networkId": 90210, "nativeCurrency": {"name": "Beverly Hills Testnet Ether", "symbol": "BVE", "decimals": 18}, "rpc": ["https://rpc.beverlyhills.ethdevops.io:8545"], "faucets": ["https://faucet.beverlyhills.ethdevops.io"], "infoURL": "https://beverlyhills.ethdevops.io"}, {"name": "Camp Testnet", "chainId": 90354, "shortName": "camp", "networkId": 90354, "nativeCurrency": {"name": "Ethereum", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-camp-network-4xje7wy105.t.conduit.xyz"], "faucets": ["https://www.campnetwork.xyz/faucet"], "infoURL": "https://campaign-1.gitbook.io/camp-technical-docså"}, {"name": "Nautilus Trition Chain", "chainId": 91002, "shortName": "NAUT", "networkId": 91002, "nativeCurrency": {"name": "Nautilus Zebec Testnet Tokens", "symbol": "tZBC", "decimals": 18}, "rpc": ["https://triton.api.nautchain.xyz"], "faucets": ["https://faucet.eclipse.builders"], "infoURL": "https://docs.nautchain.xyz"}, {"name": "MetaDAP Enterprise Mainnet", "chainId": 91120, "shortName": "MetaDAP", "networkId": 91120, "nativeCurrency": {"name": "DAP", "symbol": "DAP", "decimals": 18}, "rpc": ["https://rpc.chain.metadap.io", "wss://rpc-ws.chain.metadap.io"], "faucets": [], "infoURL": "https://metadap.io/"}, {"name": "Combo Testnet", "chainId": 91715, "shortName": "combo-testnet", "networkId": 91715, "nativeCurrency": {"name": "BNB Chain Native Token", "symbol": "tcBNB", "decimals": 18}, "rpc": ["https://test-rpc.combonetwork.io"], "faucets": [], "infoURL": "https://combonetwork.io"}, {"name": "Lambda Testnet", "chainId": 92001, "shortName": "lambda-testnet", "networkId": 92001, "nativeCurrency": {"name": "test-Lamb", "symbol": "LAMB", "decimals": 18}, "rpc": ["https://evm.lambda.top/"], "faucets": ["https://faucet.lambda.top"], "infoURL": "https://lambda.im"}, {"name": "LiquidLayer Testnet", "chainId": 93572, "shortName": "tLILA", "networkId": 93572, "nativeCurrency": {"name": "LiquidLayer Testnet", "symbol": "LILA", "decimals": 18}, "rpc": ["https://testnet.liquidlayer.network"], "faucets": ["https://claim.liquidlayer.network"], "infoURL": "https://testnet-scan.liquidlayer.network"}, {"name": "<PERSON><PERSON> Testnet (Hexapod)", "chainId": 96970, "shortName": "mantis", "networkId": 96970, "nativeCurrency": {"name": "Mantis", "symbol": "MANTIS", "decimals": 18}, "rpc": ["https://mantis-rpc.switch.ch", "https://mantis-rpc.kore-technologies.ch", "https://mantis-rpc.phoenix-systems.io"], "faucets": ["https://mantis.switch.ch/faucet", "https://mantis.kore-technologies.ch/faucet", "https://mantis.phoenix-systems.io/faucet", "https://mantis.block-spirit.ch/faucet"], "infoURL": "https://hexapod.network"}, {"name": "Boba BNB Mainnet Old", "chainId": 97288, "shortName": "BobaBnbOld", "networkId": 97288, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "BOBA", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://boba.network"}, {"name": "eLiberty Testnet", "chainId": 99099, "shortName": "ELt", "networkId": 99099, "nativeCurrency": {"name": "eLiberty", "symbol": "$EL", "decimals": 18}, "rpc": ["https://testnet-rpc.eliberty.ngo"], "faucets": ["https://faucet.eliberty.ngo"], "infoURL": "https://eliberty.ngo"}, {"name": "UB Smart Chain(testnet)", "chainId": 99998, "shortName": "usctest", "networkId": 99998, "nativeCurrency": {"name": "UBC", "symbol": "UBC", "decimals": 18}, "rpc": ["https://testnet.rpc.uschain.network"], "faucets": [], "infoURL": "https://www.ubchain.site"}, {"name": "UB Smart Chain", "chainId": 99999, "shortName": "usc", "networkId": 99999, "nativeCurrency": {"name": "UBC", "symbol": "UBC", "decimals": 18}, "rpc": ["https://rpc.uschain.network"], "faucets": [], "infoURL": "https://www.ubchain.site/"}, {"name": "QuarkChain Mainnet Root", "chainId": 100000, "shortName": "qkc-r", "networkId": 100000, "nativeCurrency": {"name": "QKC", "symbol": "QKC", "decimals": 18}, "rpc": ["http://jrpc.mainnet.quarkchain.io:38391"], "faucets": [], "infoURL": "https://www.quarkchain.io"}, {"name": "QuarkChain Mainnet Shard 0", "chainId": 100001, "shortName": "qkc-s0", "networkId": 100001, "nativeCurrency": {"name": "QKC", "symbol": "QKC", "decimals": 18}, "rpc": ["https://mainnet-s0-ethapi.quarkchain.io", "http://eth-jrpc.mainnet.quarkchain.io:39000"], "faucets": [], "infoURL": "https://www.quarkchain.io"}, {"name": "QuarkChain Mainnet Shard 1", "chainId": 100002, "shortName": "qkc-s1", "networkId": 100002, "nativeCurrency": {"name": "QKC", "symbol": "QKC", "decimals": 18}, "rpc": ["https://mainnet-s1-ethapi.quarkchain.io", "http://eth-jrpc.mainnet.quarkchain.io:39001"], "faucets": [], "infoURL": "https://www.quarkchain.io"}, {"name": "QuarkChain Mainnet Shard 2", "chainId": 100003, "shortName": "qkc-s2", "networkId": 100003, "nativeCurrency": {"name": "QKC", "symbol": "QKC", "decimals": 18}, "rpc": ["https://mainnet-s2-ethapi.quarkchain.io", "http://eth-jrpc.mainnet.quarkchain.io:39002"], "faucets": [], "infoURL": "https://www.quarkchain.io"}, {"name": "QuarkChain Mainnet Shard 3", "chainId": 100004, "shortName": "qkc-s3", "networkId": 100004, "nativeCurrency": {"name": "QKC", "symbol": "QKC", "decimals": 18}, "rpc": ["https://mainnet-s3-ethapi.quarkchain.io", "http://eth-jrpc.mainnet.quarkchain.io:39003"], "faucets": [], "infoURL": "https://www.quarkchain.io"}, {"name": "QuarkChain Mainnet Shard 4", "chainId": 100005, "shortName": "qkc-s4", "networkId": 100005, "nativeCurrency": {"name": "QKC", "symbol": "QKC", "decimals": 18}, "rpc": ["https://mainnet-s4-ethapi.quarkchain.io", "http://eth-jrpc.mainnet.quarkchain.io:39004"], "faucets": [], "infoURL": "https://www.quarkchain.io"}, {"name": "QuarkChain Mainnet Shard 5", "chainId": 100006, "shortName": "qkc-s5", "networkId": 100006, "nativeCurrency": {"name": "QKC", "symbol": "QKC", "decimals": 18}, "rpc": ["https://mainnet-s5-ethapi.quarkchain.io", "http://eth-jrpc.mainnet.quarkchain.io:39005"], "faucets": [], "infoURL": "https://www.quarkchain.io"}, {"name": "QuarkChain Mainnet Shard 6", "chainId": 100007, "shortName": "qkc-s6", "networkId": 100007, "nativeCurrency": {"name": "QKC", "symbol": "QKC", "decimals": 18}, "rpc": ["https://mainnet-s6-ethapi.quarkchain.io", "http://eth-jrpc.mainnet.quarkchain.io:39006"], "faucets": [], "infoURL": "https://www.quarkchain.io"}, {"name": "QuarkChain Mainnet Shard 7", "chainId": 100008, "shortName": "qkc-s7", "networkId": 100008, "nativeCurrency": {"name": "QKC", "symbol": "QKC", "decimals": 18}, "rpc": ["https://mainnet-s7-ethapi.quarkchain.io", "http://eth-jrpc.mainnet.quarkchain.io:39007"], "faucets": [], "infoURL": "https://www.quarkchain.io"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 100009, "shortName": "vechain", "networkId": 100009, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "VET", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://vechain.org"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 100010, "shortName": "vechain-testnet", "networkId": 100010, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "VET", "decimals": 18}, "rpc": [], "faucets": ["https://faucet.vecha.in"], "infoURL": "https://vechain.org"}, {"name": "Deprecated CHI", "chainId": 100100, "shortName": "chi1", "networkId": 100100, "nativeCurrency": {"name": "Chiado xDAI", "symbol": "xDAI", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://docs.gnosischain.com"}, {"name": "Global Trust Network", "chainId": 101010, "shortName": "stabilityprotocol", "networkId": 101010, "nativeCurrency": {"name": "FREE", "symbol": "FREE", "decimals": 18}, "rpc": ["https://gtn.stabilityprotocol.com"], "faucets": [], "infoURL": "https://stabilityprotocol.com"}, {"name": "Creditcoin Testnet", "chainId": 102031, "shortName": "ctctest", "networkId": 102031, "nativeCurrency": {"name": "Testnet CTC", "symbol": "tCTC", "decimals": 18}, "rpc": ["https://rpc.cc3-testnet.creditcoin.network"], "faucets": [], "infoURL": "https://creditcoin.org"}, {"name": "Crystaleum", "chainId": 103090, "shortName": "CRFI", "networkId": 1, "nativeCurrency": {"name": "CRFI", "symbol": "◈", "decimals": 18}, "rpc": ["https://evm.cryptocurrencydevs.org", "https://rpc.crystaleum.org"], "faucets": [], "infoURL": "https://crystaleum.org"}, {"name": "<PERSON><PERSON>", "chainId": 103454, "shortName": "masatest", "networkId": 103454, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "MASA", "decimals": 18}, "rpc": ["https://subnets.avax.network/masatestne/testnet/rpc"], "faucets": [], "infoURL": "https://masa.finance"}, {"name": "KaspaClassic Mainnet", "chainId": 104566, "shortName": "cas", "networkId": 104566, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "CAS", "decimals": 18}, "rpc": ["https://api.kaspaclassic.world/", "http://80.178.101.118:8000/"], "faucets": [], "infoURL": "https://kaspaclassic.com/"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 105105, "shortName": "stratis", "networkId": 105105, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "STRAX", "decimals": 18}, "rpc": ["https://rpc.stratisevm.com"], "faucets": [], "infoURL": "https://www.stratisplatform.com"}, {"name": "BROChain Mainnet", "chainId": 108801, "shortName": "bro", "networkId": 108801, "nativeCurrency": {"name": "Brother", "symbol": "BRO", "decimals": 18}, "rpc": ["https://rpc.brochain.org", "http://rpc.brochain.org", "https://rpc.brochain.org/mainnet", "http://rpc.brochain.org/mainnet"], "faucets": [], "infoURL": "https://brochain.org"}, {"name": "QuarkChain Devnet Root", "chainId": 110000, "shortName": "qkc-d-r", "networkId": 110000, "nativeCurrency": {"name": "QKC", "symbol": "QKC", "decimals": 18}, "rpc": ["http://jrpc.devnet.quarkchain.io:38391"], "faucets": [], "infoURL": "https://www.quarkchain.io"}, {"name": "QuarkChain Devnet Shard 0", "chainId": 110001, "shortName": "qkc-d-s0", "networkId": 110001, "nativeCurrency": {"name": "QKC", "symbol": "QKC", "decimals": 18}, "rpc": ["https://devnet-s0-ethapi.quarkchain.io", "http://eth-jrpc.devnet.quarkchain.io:39900"], "faucets": [], "infoURL": "https://www.quarkchain.io"}, {"name": "QuarkChain Devnet Shard 1", "chainId": 110002, "shortName": "qkc-d-s1", "networkId": 110002, "nativeCurrency": {"name": "QKC", "symbol": "QKC", "decimals": 18}, "rpc": ["https://devnet-s1-ethapi.quarkchain.io", "http://eth-jrpc.devnet.quarkchain.io:39901"], "faucets": [], "infoURL": "https://www.quarkchain.io"}, {"name": "QuarkChain Devnet Shard 2", "chainId": 110003, "shortName": "qkc-d-s2", "networkId": 110003, "nativeCurrency": {"name": "QKC", "symbol": "QKC", "decimals": 18}, "rpc": ["https://devnet-s2-ethapi.quarkchain.io", "http://eth-jrpc.devnet.quarkchain.io:39902"], "faucets": [], "infoURL": "https://www.quarkchain.io"}, {"name": "QuarkChain Devnet Shard 3", "chainId": 110004, "shortName": "qkc-d-s3", "networkId": 110004, "nativeCurrency": {"name": "QKC", "symbol": "QKC", "decimals": 18}, "rpc": ["https://devnet-s3-ethapi.quarkchain.io", "http://eth-jrpc.devnet.quarkchain.io:39903"], "faucets": [], "infoURL": "https://www.quarkchain.io"}, {"name": "QuarkChain Devnet Shard 4", "chainId": 110005, "shortName": "qkc-d-s4", "networkId": 110005, "nativeCurrency": {"name": "QKC", "symbol": "QKC", "decimals": 18}, "rpc": ["https://devnet-s4-ethapi.quarkchain.io", "http://eth-jrpc.devnet.quarkchain.io:39904"], "faucets": [], "infoURL": "https://www.quarkchain.io"}, {"name": "QuarkChain Dev<PERSON> Shard 5", "chainId": 110006, "shortName": "qkc-d-s5", "networkId": 110006, "nativeCurrency": {"name": "QKC", "symbol": "QKC", "decimals": 18}, "rpc": ["https://devnet-s5-ethapi.quarkchain.io", "http://eth-jrpc.devnet.quarkchain.io:39905"], "faucets": [], "infoURL": "https://www.quarkchain.io"}, {"name": "QuarkChain <PERSON> 6", "chainId": 110007, "shortName": "qkc-d-s6", "networkId": 110007, "nativeCurrency": {"name": "QKC", "symbol": "QKC", "decimals": 18}, "rpc": ["https://devnet-s6-ethapi.quarkchain.io", "http://eth-jrpc.devnet.quarkchain.io:39906"], "faucets": [], "infoURL": "https://www.quarkchain.io"}, {"name": "QuarkChain Devnet Shard 7", "chainId": 110008, "shortName": "qkc-d-s7", "networkId": 110008, "nativeCurrency": {"name": "QKC", "symbol": "QKC", "decimals": 18}, "rpc": ["https://devnet-s7-ethapi.quarkchain.io", "http://eth-jrpc.devnet.quarkchain.io:39907"], "faucets": [], "infoURL": "https://www.quarkchain.io"}, {"name": "Siberium Test Network", "chainId": 111000, "shortName": "testsbr", "networkId": 111000, "nativeCurrency": {"name": "TestSIBR", "symbol": "SIBR", "decimals": 18}, "rpc": ["https://rpc.test.siberium.net"], "faucets": [], "infoURL": "https://siberium.net"}, {"name": "Siberium Network", "chainId": 111111, "shortName": "sbr", "networkId": 111111, "nativeCurrency": {"name": "Siberium", "symbol": "SIBR", "decimals": 18}, "rpc": ["https://rpc.main.siberium.net", "https://rpc.main.siberium.net.ru"], "faucets": [], "infoURL": "https://siberium.net"}, {"name": "re.al", "chainId": 111188, "shortName": "re-al", "networkId": 111188, "nativeCurrency": {"name": "re.al <PERSON>", "symbol": "reETH", "decimals": 18}, "rpc": ["https://real.drpc.org", "wss://real.drpc.org"], "faucets": [], "infoURL": "https://re.al"}, {"name": "Metachain One Mainnet", "chainId": 112358, "shortName": "metao", "networkId": 112358, "nativeCurrency": {"name": "Metao", "symbol": "METAO", "decimals": 18}, "rpc": ["https://rpc.metachain.one", "https://rpc2.metachain.one"], "faucets": [], "infoURL": "https://metachain.one"}, {"name": "MetaDAP Enterprise Testnet", "chainId": 119139, "shortName": "MetaDAP-T", "networkId": 119139, "nativeCurrency": {"name": "DAP", "symbol": "DAP", "decimals": 18}, "rpc": ["https://rpc.testnet.chain.metadap.io", "wss://rpc-ws.testnet.chain.metadap.io"], "faucets": [], "infoURL": "https://metadap.io/"}, {"name": "ADIL Devnet", "chainId": 123456, "shortName": "dadil", "networkId": 123456, "nativeCurrency": {"name": "Devnet ADIL", "symbol": "ADIL", "decimals": 18}, "rpc": ["https://devnet.adilchain-rpc.io"], "faucets": [], "infoURL": "https://adilchain.io"}, {"name": "Etherlink Testnet", "chainId": 128123, "shortName": "etlt", "networkId": 128123, "nativeCurrency": {"name": "tez", "symbol": "XTZ", "decimals": 18}, "rpc": ["https://node.ghostnet.etherlink.com"], "faucets": ["https://faucet.etherlink.com"], "infoURL": "https://etherlink.com"}, {"name": "Odyssey Chain (Testnet)", "chainId": 131313, "shortName": "DIONE", "networkId": 131313, "nativeCurrency": {"name": "DIONE", "symbol": "DIONE", "decimals": 18}, "rpc": ["https://testnode.dioneprotocol.com/ext/bc/D/rpc"], "faucets": ["https://faucet.dioneprotocol.com/"], "infoURL": "https://www.dioneprotocol.com/"}, {"name": "ETND Chain Mainnets", "chainId": 131419, "shortName": "ETND", "networkId": 131419, "nativeCurrency": {"name": "ETND", "symbol": "ETND", "decimals": 18}, "rpc": ["https://rpc.node1.etnd.pro/"], "faucets": [], "infoURL": "https://www.etnd.pro"}, {"name": "MagApe Testnet", "chainId": 141319, "shortName": "mag", "networkId": 141319, "nativeCurrency": {"name": "MagApe", "symbol": "MAG", "decimals": 18}, "rpc": ["https://testnet-api.magape.io/chain/"], "faucets": [], "infoURL": "https://magape.io"}, {"name": "ICPlaza Mainnet", "chainId": 142857, "shortName": "ICPlaza", "networkId": 142857, "nativeCurrency": {"name": "ict", "symbol": "ict", "decimals": 18}, "rpc": ["https://rpcmainnet.ic-plaza.org/"], "faucets": [], "infoURL": "https://docs.ic-plaza.org/"}, {"name": "PlayFi Mainnet", "chainId": 161212, "shortName": "playfi", "networkId": 161212, "nativeCurrency": {"name": "Play", "symbol": "PLAY", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://www.playfi.ai/"}, {"name": "<PERSON><PERSON>", "chainId": 167000, "shortName": "tko-mainnet", "networkId": 167000, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.taiko.xyz", "wss://ws.taiko.xyz"], "faucets": [], "infoURL": "https://taiko.xyz"}, {"name": "<PERSON><PERSON> (Alpha-2 Testnet)", "chainId": 167004, "shortName": "taiko-a2", "networkId": 167004, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.a2.taiko.xyz"], "faucets": [], "infoURL": "https://taiko.xyz"}, {"name": "Taiko Grimsvotn L2", "chainId": 167005, "shortName": "taiko-l2", "networkId": 167005, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.test.taiko.xyz"], "faucets": [], "infoURL": "https://taiko.xyz"}, {"name": "Taiko Eldfell L3", "chainId": 167006, "shortName": "taiko-l3", "networkId": 167006, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.l3test.taiko.xyz"], "faucets": [], "infoURL": "https://taiko.xyz"}, {"name": "Taiko Jolnir L2", "chainId": 167007, "shortName": "tko-jol<PERSON>", "networkId": 167007, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.jolnir.taiko.xyz"], "faucets": [], "infoURL": "https://taiko.xyz"}, {"name": "Taiko Katla L2", "chainId": 167008, "shortName": "tko-katla", "networkId": 167008, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.katla.taiko.xyz", "wss://ws.katla.taiko.xyz", "https://taiko-katla.drpc.org", "wss://taiko-katla.drpc.org"], "faucets": [], "infoURL": "https://taiko.xyz"}, {"name": "Taiko Hekla L2", "chainId": 167009, "shortName": "tko-hekla", "networkId": 167009, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.hekla.taiko.xyz", "wss://ws.hekla.taiko.xyz"], "faucets": [], "infoURL": "https://taiko.xyz"}, {"name": "Bitica Chain Mainnet", "chainId": 188710, "shortName": "bdcc", "networkId": 188710, "nativeCurrency": {"name": "Bitica Coin", "symbol": "BDCC", "decimals": 18}, "rpc": ["https://mainnet-rpc.biticablockchain.com/"], "faucets": [], "infoURL": "https://biticablockchain.com/"}, {"name": "Condor Test Network", "chainId": 188881, "shortName": "condor", "networkId": 188881, "nativeCurrency": {"name": "Condor Native Token", "symbol": "CONDOR", "decimals": 18}, "rpc": ["https://testnet.condor.systems/rpc"], "faucets": ["https://faucet.condor.systems"], "infoURL": "https://condor.systems"}, {"name": "Mind Network Testnet", "chainId": 192940, "shortName": "fhet", "networkId": 192940, "nativeCurrency": {"name": "FHE", "symbol": "FHE", "decimals": 18}, "rpc": ["https://rpc-testnet.mindnetwork.xyz", "wss://rpc-testnet.mindnetwork.xyz"], "faucets": [], "infoURL": "https://mindnetwork.xyz"}, {"name": "xFair.AI Testnet", "chainId": 200000, "shortName": "fait", "networkId": 200000, "nativeCurrency": {"name": "FAI", "symbol": "FAI", "decimals": 18}, "rpc": ["https://rpc_testnet.xfair.ai", "wss://rpc_testnet.xfair.ai"], "faucets": [], "infoURL": "https://xfair.ai"}, {"name": "Milkomeda C1 Testnet", "chainId": 200101, "shortName": "milkTAda", "networkId": 200101, "nativeCurrency": {"name": "milkTAda", "symbol": "mTAda", "decimals": 18}, "rpc": ["https://rpc-devnet-cardano-evm.c1.milkomeda.com", "wss://rpc-devnet-cardano-evm.c1.milkomeda.com"], "faucets": [], "infoURL": "https://milkomeda.com"}, {"name": "Milkomeda A1 Testnet", "chainId": 200202, "shortName": "milkTAlgo", "networkId": 200202, "nativeCurrency": {"name": "milkTAlgo", "symbol": "mTAlgo", "decimals": 18}, "rpc": ["https://rpc-devnet-algorand-rollup.a1.milkomeda.com"], "faucets": [], "infoURL": "https://milkomeda.com"}, {"name": "Akroma", "chainId": 200625, "shortName": "aka", "networkId": 200625, "nativeCurrency": {"name": "Akroma Ether", "symbol": "AKA", "decimals": 18}, "rpc": ["https://remote.akroma.io"], "faucets": [], "infoURL": "https://akroma.io"}, {"name": "Bitlayer Testnet", "chainId": 200810, "shortName": "btrt", "networkId": 200810, "nativeCurrency": {"name": "BTC", "symbol": "BTC", "decimals": 18}, "rpc": ["https://testnet-rpc.bitlayer.org", "wss://testnet-ws.bitlayer.org", "https://testnet-rpc.bitlayer-rpc.com", "wss://testnet-ws.bitlayer-rpc.com", "https://rpc.ankr.com/bitlayer_testnet"], "faucets": ["https://www.bitlayer.org/faucet"], "infoURL": "https://docs.bitlayer.org/"}, {"name": "Bitlayer Mainnet", "chainId": 200901, "shortName": "btr", "networkId": 200901, "nativeCurrency": {"name": "BTC", "symbol": "BTC", "decimals": 18}, "rpc": ["https://rpc.bitlayer.org", "https://rpc.bitlayer-rpc.com", "https://rpc.ankr.com/bitlayer", "wss://ws.bitlayer.org", "wss://ws.bitlayer-rpc.com"], "faucets": [], "infoURL": "https://docs.bitlayer.org/"}, {"name": "Alaya Mainnet", "chainId": 201018, "shortName": "alaya", "networkId": 1, "nativeCurrency": {"name": "ATP", "symbol": "atp", "decimals": 18}, "rpc": ["https://openapi.alaya.network/rpc", "wss://openapi.alaya.network/ws"], "faucets": [], "infoURL": "https://www.alaya.network/"}, {"name": "<PERSON><PERSON>", "chainId": 201030, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "networkId": 1, "nativeCurrency": {"name": "ATP", "symbol": "atp", "decimals": 18}, "rpc": ["https://devnetopenapi.alaya.network/rpc", "wss://devnetopenapi.alaya.network/ws"], "faucets": ["https://faucet.alaya.network/faucet/?id=f93426c0887f11eb83b900163e06151c"], "infoURL": "https://www.alaya.network/"}, {"name": "Mythical Chain", "chainId": 201804, "shortName": "myth", "networkId": 201804, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "MYTH", "decimals": 18}, "rpc": ["https://chain-rpc.mythicalgames.com"], "faucets": [], "infoURL": "https://mythicalgames.com/"}, {"name": "Decimal Smart Chain Testnet", "chainId": 202020, "shortName": "tDSC", "networkId": 202020, "nativeCurrency": {"name": "Decimal", "symbol": "tDEL", "decimals": 18}, "rpc": ["https://testnet-val.decimalchain.com/web3/"], "faucets": [], "infoURL": "https://decimalchain.com"}, {"name": "X1 Devnet", "chainId": 202212, "shortName": "x1-devnet", "networkId": 202212, "nativeCurrency": {"name": "XN", "symbol": "XN", "decimals": 18}, "rpc": ["https://x1-devnet.xen.network"], "faucets": [], "infoURL": "https://docs.xen.network/x1/"}, {"name": "YMTECH-BESU Testnet", "chainId": 202401, "shortName": "YMTECH-BESU", "networkId": 202401, "nativeCurrency": {"name": "ETH", "symbol": "ETH", "decimals": 18}, "rpc": ["http://39.119.118.216:8545"], "faucets": [], "infoURL": "https://www.ymtech.co.kr"}, {"name": "<PERSON><PERSON>", "chainId": 202624, "shortName": "twl-jellie", "networkId": 202624, "nativeCurrency": {"name": "Twala Coin", "symbol": "TWL", "decimals": 18}, "rpc": ["https://jellie-rpc.twala.io/", "wss://jellie-rpc-wss.twala.io/"], "faucets": [], "infoURL": "https://twala.io/"}, {"name": "X1 Network", "chainId": 204005, "shortName": "x1-testnet", "networkId": 204005, "nativeCurrency": {"name": "XN", "symbol": "XN", "decimals": 18}, "rpc": ["https://x1-testnet.xen.network"], "faucets": [], "infoURL": "https://docs.xen.network/go-x1/"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 205205, "shortName": "auroria", "networkId": 205205, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "tSTRAX", "decimals": 18}, "rpc": ["https://auroria.rpc.stratisevm.com"], "faucets": ["https://auroria.faucet.stratisevm.com"], "infoURL": "https://www.stratisplatform.com"}, {"name": "GitAGI Atlas Testnet", "chainId": 210049, "shortName": "atlas", "networkId": 210049, "nativeCurrency": {"name": "GitAGI", "symbol": "tGAGI", "decimals": 18}, "rpc": ["https://rpc.gitagi.org"], "faucets": [], "infoURL": "https://gitagi.org/"}, {"name": "PlatON Mainnet", "chainId": 210425, "shortName": "platon", "networkId": 1, "nativeCurrency": {"name": "LAT", "symbol": "lat", "decimals": 18}, "rpc": ["https://openapi2.platon.network/rpc", "wss://openapi2.platon.network/ws"], "faucets": [], "infoURL": "https://www.platon.network"}, {"name": "<PERSON><PERSON>", "chainId": 220315, "shortName": "mas", "networkId": 220315, "nativeCurrency": {"name": "Master Bank", "symbol": "MAS", "decimals": 18}, "rpc": ["http://node.masnet.ai:8545"], "faucets": [], "infoURL": "https://masterbank.org"}, {"name": "Reapchain Mainnet", "chainId": 221230, "shortName": "reap", "networkId": 221230, "nativeCurrency": {"name": "Reap", "symbol": "REAP", "decimals": 18}, "rpc": ["https://eth.reapchain.org"], "faucets": [], "infoURL": "https://reapchain.com"}, {"name": "Reapchain Testnet", "chainId": 221231, "shortName": "reap-testnet", "networkId": 221231, "nativeCurrency": {"name": "test-Reap", "symbol": "tREAP", "decimals": 18}, "rpc": ["https://test-eth.reapchain.org"], "faucets": ["http://faucet.reapchain.com"], "infoURL": "https://reapchain.com"}, {"name": "HydraDX", "chainId": 222222, "shortName": "hdx", "networkId": 222222, "nativeCurrency": {"name": "Wrapped ETH", "symbol": "WETH", "decimals": 18}, "rpc": ["https://rpc.hydradx.cloud", "wss://rpc.hydradx.cloud"], "faucets": [], "infoURL": "https://hydradx.io"}, {"name": "DeepL Mainnet", "chainId": 222555, "shortName": "deepl", "networkId": 222555, "nativeCurrency": {"name": "DeepL", "symbol": "DEEPL", "decimals": 18}, "rpc": ["https://rpc.deeplnetwork.org"], "faucets": [], "infoURL": "https://deeplnetwork.org"}, {"name": "DeepL Testnet", "chainId": 222666, "shortName": "tdeepl", "networkId": 222666, "nativeCurrency": {"name": "DeepL", "symbol": "DEEPL", "decimals": 18}, "rpc": ["https://testnet.deeplnetwork.org"], "faucets": ["https://faucet.deeplnetwork.org"], "infoURL": "https://deeplnetwork.org"}, {"name": "Taf ECO Chain Mainnet", "chainId": 224168, "shortName": "TAFECO", "networkId": 224168, "nativeCurrency": {"name": "Taf ECO Chain Mainnet", "symbol": "TAFECO", "decimals": 18}, "rpc": ["https://mainnet.tafchain.com/v1"], "faucets": [], "infoURL": "https://www.tafchain.com"}, {"name": "CONET Sebolia Testnet", "chainId": 224422, "shortName": "conet-sebolia", "networkId": 224422, "nativeCurrency": {"name": "CONET Sebolia", "symbol": "CONET", "decimals": 18}, "rpc": ["https://rpc1.conet.network"], "faucets": [], "infoURL": "https://conet.network"}, {"name": "CONET <PERSON>", "chainId": 224433, "shortName": "conet-holesky", "networkId": 224433, "nativeCurrency": {"name": "CONET <PERSON>", "symbol": "CONET", "decimals": 18}, "rpc": ["https://rpc.conet.network"], "faucets": [], "infoURL": "https://conet.network"}, {"name": "HashKey Chain Testnet(discard)", "chainId": 230315, "shortName": "hsktest", "networkId": 230315, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "tHSK", "decimals": 18}, "rpc": ["https://testnet.hashkeychain/rpc"], "faucets": ["https://testnet.hashkeychain/faucet"], "infoURL": "https://www.hashkey.com"}, {"name": "Haymo Testnet", "chainId": 234666, "shortName": "hym", "networkId": 234666, "nativeCurrency": {"name": "HAYMO", "symbol": "HYM", "decimals": 18}, "rpc": ["https://testnet1.haymo.network"], "faucets": [], "infoURL": "https://haymoswap.web.app/"}, {"name": "ARTIS sigma1", "chainId": 246529, "shortName": "ats", "networkId": 246529, "nativeCurrency": {"name": "ARTIS sigma1 Ether", "symbol": "ATS", "decimals": 18}, "rpc": ["https://rpc.sigma1.artis.network"], "faucets": [], "infoURL": "https://artis.eco"}, {"name": "ARTIS Testnet tau1", "chainId": 246785, "shortName": "atstau", "networkId": 246785, "nativeCurrency": {"name": "ARTIS tau1 Ether", "symbol": "tATS", "decimals": 18}, "rpc": ["https://rpc.tau1.artis.network"], "faucets": [], "infoURL": "https://artis.network"}, {"name": "Saakuru Testnet", "chainId": 247253, "shortName": "saakuru-testnet", "networkId": 247253, "nativeCurrency": {"name": "OAS", "symbol": "OAS", "decimals": 18}, "rpc": ["https://rpc-testnet.saakuru.network"], "faucets": [], "infoURL": "https://saakuru.network"}, {"name": "CMP-Mainnet", "chainId": 256256, "shortName": "cmp-mainnet", "networkId": 256256, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "CMP", "decimals": 18}, "rpc": ["https://mainnet.block.caduceus.foundation", "wss://mainnet.block.caduceus.foundation"], "faucets": [], "infoURL": "https://caduceus.foundation/"}, {"name": "Gear Zero Network Testnet", "chainId": 266256, "shortName": "gz-testnet", "networkId": 266256, "nativeCurrency": {"name": "Gear Zero Network Native Token", "symbol": "GZN", "decimals": 18}, "rpc": ["https://gzn-test.linksme.info"], "faucets": [], "infoURL": "https://token.gearzero.ca/testnet"}, {"name": "EgonCoin Testnet", "chainId": 271271, "shortName": "EGONt", "networkId": 271271, "nativeCurrency": {"name": "EgonCoin", "symbol": "EGON", "decimals": 18}, "rpc": ["https://rpctest.egonscan.com"], "faucets": ["https://faucet.egonscan.com"], "infoURL": "https://egonscan.com"}, {"name": "Social Smart Chain Mainnet", "chainId": 281121, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "networkId": 281121, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "$OC", "decimals": 18}, "rpc": ["https://socialsmartchain.digitalnext.business"], "faucets": [], "infoURL": "https://digitalnext.business/SocialSmartChain"}, {"name": "Zillion Sepolia Testnet", "chainId": 282828, "shortName": "zillsep", "networkId": 282828, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://sepolia.zillnet.io/rpc"], "faucets": [], "infoURL": "https://zillnet.io"}, {"name": "SaharaAI Testnet", "chainId": 313313, "shortName": "saharatest", "networkId": 313313, "nativeCurrency": {"name": "SAHARA", "symbol": "SAH", "decimals": 18}, "rpc": ["https://testnet.saharalabs.ai"], "faucets": [], "infoURL": "https://saharalabs.ai"}, {"name": "Filecoin - Calibration testnet", "chainId": 314159, "shortName": "filecoin-calibration", "networkId": 314159, "nativeCurrency": {"name": "testnet filecoin", "symbol": "tFIL", "decimals": 18}, "rpc": ["https://api.calibration.node.glif.io/rpc/v1", "https://rpc.ankr.com/filecoin_testnet", "https://filecoin-calibration.chainstacklabs.com/rpc/v1", "https://filecoin-calibration.chainup.net/rpc/v1", "https://calibration.filfox.info/rpc/v1", "https://filecoin-calibration.drpc.org", "wss://filecoin-calibration.drpc.org"], "faucets": ["https://faucet.calibration.fildev.network/"], "infoURL": "https://filecoin.io"}, {"name": "Parex Mainnet", "chainId": 322202, "shortName": "parex", "networkId": 322202, "nativeCurrency": {"name": "PAREX", "symbol": "PRX", "decimals": 18}, "rpc": ["https://mainnet-rpc.parex.network"], "faucets": [], "infoURL": "https://parex.network"}, {"name": "Bloom Genesis Testnet", "chainId": 323213, "shortName": "BGBC-Testnet", "networkId": 323213, "nativeCurrency": {"name": "Bloom", "symbol": "BGBC", "decimals": 18}, "rpc": ["https://testnet-rpc.bloomgenesis.com"], "faucets": ["https://faucet.bloomgenesis.com"], "infoURL": "https://www.bloomgenesis.com"}, {"name": "TTcoin Smart Chain Mainnet", "chainId": 330844, "shortName": "tc", "networkId": 330844, "nativeCurrency": {"name": "TTcoin", "symbol": "TC", "decimals": 18}, "rpc": ["https://mainnet-rpc.tscscan.com"], "faucets": ["https://faucet.tscscan.com"], "infoURL": "https://ttcoin.info/"}, {"name": "Bloom Genesis Mainnet", "chainId": 333313, "shortName": "BGBC", "networkId": 333313, "nativeCurrency": {"name": "Bloom", "symbol": "BGBC", "decimals": 18}, "rpc": ["https://mainnet-rpc.bloomgenesis.com"], "faucets": [], "infoURL": "https://www.bloomgenesis.com"}, {"name": "Aves Testnet", "chainId": 333331, "shortName": "avst", "networkId": 333331, "nativeCurrency": {"name": "AvesT", "symbol": "AVST", "decimals": 18}, "rpc": ["https://test.rpc.avescoin.io"], "faucets": [], "infoURL": "https://ethereum.org"}, {"name": "Nativ3 Testnet", "chainId": 333333, "shortName": "N3-Test", "networkId": 333333, "nativeCurrency": {"name": "USNT", "symbol": "USNT", "decimals": 18}, "rpc": ["https://rpctest.nativ3.network", "wss://wstest.nativ3.network"], "faucets": [], "infoURL": "https://nativ3.network"}, {"name": "Oone Chain Testnet", "chainId": 333666, "shortName": "oonetest", "networkId": 333666, "nativeCurrency": {"name": "tOONE", "symbol": "tOONE", "decimals": 18}, "rpc": ["https://rpc.testnet.oonechain.com"], "faucets": ["https://apps-test.adigium.com/faucet"], "infoURL": "https://oonechain.com"}, {"name": "Oone Chain Devnet", "chainId": 333777, "shortName": "<PERSON><PERSON><PERSON>", "networkId": 333777, "nativeCurrency": {"name": "tOONE", "symbol": "tOONE", "decimals": 18}, "rpc": ["https://rpc.dev.oonechain.com"], "faucets": ["https://apps-test.adigium.com/faucet"], "infoURL": "https://oonechain.com"}, {"name": "Polis Testnet", "chainId": 333888, "shortName": "sparta", "networkId": 333888, "nativeCurrency": {"name": "tPolis", "symbol": "tPOLIS", "decimals": 18}, "rpc": ["https://sparta-rpc.polis.tech"], "faucets": ["https://faucet.polis.tech"], "infoURL": "https://polis.tech"}, {"name": "Polis Mainnet", "chainId": 333999, "shortName": "olympus", "networkId": 333999, "nativeCurrency": {"name": "Polis", "symbol": "POLIS", "decimals": 18}, "rpc": ["https://rpc.polis.tech"], "faucets": ["https://faucet.polis.tech"], "infoURL": "https://polis.tech"}, {"name": "UPchain Testnet", "chainId": 336655, "shortName": "UPchain-testnet", "networkId": 336655, "nativeCurrency": {"name": "UBTC", "symbol": "UBTC", "decimals": 18}, "rpc": ["https://rpc-testnet.uniport.network"], "faucets": ["https://faucet-testnet.uniport.network"], "infoURL": "https://uniport.network"}, {"name": "UPchain Mainnet", "chainId": 336666, "shortName": "UPchain-mainnet", "networkId": 336666, "nativeCurrency": {"name": "UBTC", "symbol": "UBTC", "decimals": 18}, "rpc": ["https://rpc.uniport.network"], "faucets": [], "infoURL": "https://uniport.network"}, {"name": "Bitfinity Network Testnet", "chainId": 355113, "shortName": "Bitfinity", "networkId": 355113, "nativeCurrency": {"name": "BITFINITY", "symbol": "BFT", "decimals": 18}, "rpc": ["https://testnet.bitfinity.network"], "faucets": ["https://bitfinity.network/faucet"], "infoURL": "https://bitfinity.network"}, {"name": "Digit Soul Smart Chain 2", "chainId": 363636, "shortName": "DS2", "networkId": 363636, "nativeCurrency": {"name": "Digit Coin", "symbol": "DGC", "decimals": 18}, "rpc": ["https://dgs-rpc.digitsoul.co.th"], "faucets": [], "infoURL": ""}, {"name": "HAPchain Testnet", "chainId": 373737, "shortName": "hap-testnet", "networkId": 373737, "nativeCurrency": {"name": "HAP", "symbol": "HAP", "decimals": 18}, "rpc": ["https://jsonrpc-test.hap.land"], "faucets": [], "infoURL": "https://hap.land"}, {"name": "Metal C-Chain", "chainId": 381931, "shortName": "metal", "networkId": 381931, "nativeCurrency": {"name": "Metal", "symbol": "METAL", "decimals": 18}, "rpc": ["https://api.metalblockchain.org/ext/bc/C/rpc"], "faucets": [], "infoURL": "https://www.metalblockchain.org/"}, {"name": "Metal Tahoe C-Chain", "chainId": 381932, "shortName": "Tahoe", "networkId": 381932, "nativeCurrency": {"name": "Metal", "symbol": "METAL", "decimals": 18}, "rpc": ["https://tahoe.metalblockchain.org/ext/bc/C/rpc"], "faucets": [], "infoURL": "https://www.metalblockchain.org/"}, {"name": "Tipboxcoin Mainnet", "chainId": 404040, "shortName": "TPBXm", "networkId": 404040, "nativeCurrency": {"name": "Tipboxcoin", "symbol": "TPBX", "decimals": 18}, "rpc": ["https://mainnet-rpc.tipboxcoin.net"], "faucets": ["https://faucet.tipboxcoin.net"], "infoURL": "https://tipboxcoin.net"}, {"name": "AIE Testnet", "chainId": 413413, "shortName": "aiet", "networkId": 413413, "nativeCurrency": {"name": "AIE", "symbol": "tAIE", "decimals": 18}, "rpc": ["https://rpc1-testnet.aiechain.io"], "faucets": [], "infoURL": "https://testnet.aiescan.io"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 420420, "shortName": "KEK", "networkId": 103090, "nativeCurrency": {"name": "KEK", "symbol": "KEK", "decimals": 18}, "rpc": ["https://mainnet.kekchain.com"], "faucets": [], "infoURL": "https://kekchain.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON> (kektest)", "chainId": 420666, "shortName": "tKEK", "networkId": 1, "nativeCurrency": {"name": "tKEK", "symbol": "tKEK", "decimals": 18}, "rpc": ["https://testnet.kekchain.com"], "faucets": [], "infoURL": "https://kekchain.com"}, {"name": "Alterium L2 Testnet", "chainId": 420692, "shortName": "alterium", "networkId": 420692, "nativeCurrency": {"name": "Alterium ETH", "symbol": "AltETH", "decimals": 18}, "rpc": ["https://l2-testnet-rpc.altscan.org"], "faucets": [], "infoURL": "https://alteriumprotocol.org"}, {"name": "Arbitrum Rinkeby", "chainId": 421611, "shortName": "arb-rink<PERSON>y", "networkId": 421611, "nativeCurrency": {"name": "Arbitrum Rinkeby Ether", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rinkeby.arbitrum.io/rpc"], "faucets": ["http://fauceth.komputing.org?chain=421611&address=${ADDRESS}"], "infoURL": "https://arbitrum.io"}, {"name": "Arbitrum Goerli", "chainId": 421613, "shortName": "arb-goerli", "networkId": 421613, "nativeCurrency": {"name": "Arbitrum Goerli Ether", "symbol": "AGOR", "decimals": 18}, "rpc": ["https://goerli-rollup.arbitrum.io/rpc", "https://arbitrum-goerli.publicnode.com", "wss://arbitrum-goerli.publicnode.com"], "faucets": [], "infoURL": "https://arbitrum.io/"}, {"name": "Arbitrum Sepolia", "chainId": 421614, "shortName": "arb-sep", "networkId": 421614, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://sepolia-rollup.arbitrum.io/rpc", "https://arbitrum-sepolia.infura.io/v3/${INFURA_API_KEY}"], "faucets": [], "infoURL": "https://arbitrum.io"}, {"name": "Fastex Chain testnet", "chainId": 424242, "shortName": "fastexTestnet", "networkId": 424242, "nativeCurrency": {"name": "FTN", "symbol": "FTN", "decimals": 18}, "rpc": ["https://rpc.testnet.fastexchain.com"], "faucets": [], "infoURL": "https://fastex.com"}, {"name": "<PERSON><PERSON>", "chainId": 431140, "shortName": "markr-go", "networkId": 431140, "nativeCurrency": {"name": "Avalanche", "symbol": "AVAX", "decimals": 18}, "rpc": ["https://rpc.markr.io/ext/"], "faucets": [], "infoURL": "https://www.markr.io/"}, {"name": "Dexalot Subnet Testnet", "chainId": 432201, "shortName": "dexalot-testnet", "networkId": 432201, "nativeCurrency": {"name": "Dexalot", "symbol": "ALOT", "decimals": 18}, "rpc": ["https://subnets.avax.network/dexalot/testnet/rpc"], "faucets": ["https://faucet.avax.network/?subnet=dexalot"], "infoURL": "https://dexalot.com"}, {"name": "Dexalot Subnet", "chainId": 432204, "shortName": "dexalot", "networkId": 432204, "nativeCurrency": {"name": "Dexalot", "symbol": "ALOT", "decimals": 18}, "rpc": ["https://subnets.avax.network/dexalot/mainnet/rpc"], "faucets": [], "infoURL": "https://dexalot.com"}, {"name": "Syndr L3 Sepolia", "chainId": 444444, "shortName": "syndr", "networkId": 444444, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://sepolia.syndr.com/http", "wss://sepolia.syndr.com/ws"], "faucets": [], "infoURL": "https://syndr.com"}, {"name": "Weelink Testnet", "chainId": 444900, "shortName": "wlkt", "networkId": 444900, "nativeCurrency": {"name": "Weelink Chain Token", "symbol": "tWLK", "decimals": 18}, "rpc": ["https://weelinknode1c.gw002.oneitfarm.com"], "faucets": ["https://faucet.weelink.gw002.oneitfarm.com"], "infoURL": "https://weelink.cloud"}, {"name": "Patex Sepolia Testnet", "chainId": 471100, "shortName": "psep", "networkId": 471100, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://test-rpc.patex.io/"], "faucets": [], "infoURL": "https://patex.io/"}, {"name": "Ultra Pro Mainnet", "chainId": 473861, "shortName": "ultrapro", "networkId": 473861, "nativeCurrency": {"name": "Ultra Pro", "symbol": "UPRO", "decimals": 18}, "rpc": ["https://mainnet-rpc.ultraproscan.io"], "faucets": [], "infoURL": "https://ultrapro.info"}, {"name": "OpenChain Mainnet", "chainId": 474142, "shortName": "oc", "networkId": 474142, "nativeCurrency": {"name": "OpenCoin", "symbol": "OPC", "decimals": 10}, "rpc": ["https://baas-rpc.luniverse.io:18545?lChainId=1641349324562974539"], "faucets": [], "infoURL": "https://www.openchain.live"}, {"name": "CMP-Testnet", "chainId": 512512, "shortName": "cmp", "networkId": 512512, "nativeCurrency": {"name": "C<PERSON>uceus <PERSON>net Token", "symbol": "CMP", "decimals": 18}, "rpc": ["https://galaxy.block.caduceus.foundation", "wss://galaxy.block.caduceus.foundation"], "faucets": ["https://dev.caduceus.foundation/testNetwork"], "infoURL": "https://caduceus.foundation/"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 513100, "shortName": "dis", "networkId": 513100, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "DIS", "decimals": 18}, "rpc": ["https://rpc.dischain.xyz"], "faucets": [], "infoURL": "https://dischain.xyz"}, {"name": "DoCoin Community Chain", "chainId": 526916, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "networkId": 526916, "nativeCurrency": {"name": "DO", "symbol": "DCT", "decimals": 18}, "rpc": ["https://rpc.docoin.shop"], "faucets": [], "infoURL": "https://docoin.network"}, {"name": "<PERSON><PERSON> Sep<PERSON>net", "chainId": 534351, "shortName": "scr-sepolia", "networkId": 534351, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://sepolia-rpc.scroll.io", "https://rpc.ankr.com/scroll_sepolia_testnet", "https://scroll-sepolia.chainstacklabs.com", "https://scroll-testnet-public.unifra.io"], "faucets": [], "infoURL": "https://scroll.io"}, {"name": "<PERSON><PERSON>", "chainId": 534352, "shortName": "scr", "networkId": 534352, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.scroll.io", "https://rpc.ankr.com/scroll", "https://scroll-mainnet.chainstacklabs.com"], "faucets": [], "infoURL": "https://scroll.io"}, {"name": "Scroll Alpha Testnet", "chainId": 534353, "shortName": "scr-alpha", "networkId": 534353, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://alpha-rpc.scroll.io/l2"], "faucets": [], "infoURL": "https://scroll.io"}, {"name": "Scroll Pre-Alpha Testnet", "chainId": 534354, "shortName": "scr-prealpha", "networkId": 534354, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "TSETH", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://scroll.io"}, {"name": "Shinarium Beta", "chainId": 534849, "shortName": "shi", "networkId": 534849, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "SHI", "decimals": 18}, "rpc": ["https://rpc.shinarium.org"], "faucets": ["https://faucet.shinarium.org"], "infoURL": "https://shinarium.org"}, {"name": "BeanEco SmartChain", "chainId": 535037, "shortName": "BESC", "networkId": 535037, "nativeCurrency": {"name": "BeanEco SmartChain", "symbol": "BESC", "decimals": 18}, "rpc": ["https://mainnet-rpc.bescscan.io"], "faucets": [], "infoURL": "besceco.finance"}, {"name": "Eclipse Testnet", "chainId": 555666, "shortName": "eclipset", "networkId": 555666, "nativeCurrency": {"name": "Eclipse", "symbol": "ECLPS", "decimals": 18}, "rpc": ["https://subnets.avax.network/eclipsecha/testnet/rpc"], "faucets": [], "infoURL": "http://eclipsenet.io"}, {"name": "Hypra Mainnet", "chainId": 622277, "shortName": "hyp", "networkId": 622277, "nativeCurrency": {"name": "<PERSON>y<PERSON>", "symbol": "HYP", "decimals": 18}, "rpc": ["https://rpc.hypra.network", "https://rpc.rethereum.org", "https://rethereum.rpc.restratagem.com", "https://rpc.rthcentral.org", "https://hypra.rpc.thirdweb.com"], "faucets": [], "infoURL": "https://www.hypra.network"}, {"name": "Bear Network Chain Mainnet", "chainId": 641230, "shortName": "BRNKC", "networkId": 641230, "nativeCurrency": {"name": "Bear Network Chain Native Token", "symbol": "BRNKC", "decimals": 18}, "rpc": ["https://brnkc-mainnet.bearnetwork.net", "https://brnkc-mainnet1.bearnetwork.net"], "faucets": [], "infoURL": "https://bearnetwork.net"}, {"name": "ALL Mainnet", "chainId": 651940, "shortName": "ALL", "networkId": 651940, "nativeCurrency": {"name": "ALL", "symbol": "ALL", "decimals": 18}, "rpc": ["https://mainnet-rpc.alltra.global"], "faucets": [], "infoURL": "https://alltra.world"}, {"name": "Xai Mainnet", "chainId": 660279, "shortName": "xai", "networkId": 660279, "nativeCurrency": {"name": "Xai", "symbol": "XAI", "decimals": 18}, "rpc": ["https://xai-chain.net/rpc"], "faucets": [], "infoURL": "https://xai.games"}, {"name": "Vision - Vpioneer Test Chain", "chainId": 666666, "shortName": "vpioneer", "networkId": 666666, "nativeCurrency": {"name": "VS", "symbol": "VS", "decimals": 18}, "rpc": ["https://vpioneer.infragrid.v.network/ethereum/compatible"], "faucets": ["https://vpioneerfaucet.visionscan.org"], "infoURL": "https://visionscan.org"}, {"name": "Hela Official Runtime Testnet", "chainId": 666888, "shortName": "hela-testnet", "networkId": 666888, "nativeCurrency": {"name": "<PERSON>la HLUS<PERSON>", "symbol": "HLUSD", "decimals": 18}, "rpc": ["https://testnet-rpc.helachain.com"], "faucets": ["https://testnet-faucet.helachain.com"], "infoURL": "https://helalabs.com"}, {"name": "Won Network", "chainId": 686868, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "networkId": 686868, "nativeCurrency": {"name": "Won", "symbol": "WON", "decimals": 18}, "rpc": ["https://rpc.wonnetwork.org"], "faucets": ["https://faucet.wondollars.org"], "infoURL": "https://wonnetwork.org"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 696969, "shortName": "<PERSON><PERSON><PERSON>-de<PERSON><PERSON>", "networkId": 696969, "nativeCurrency": {"name": "Galadriel Devnet token", "symbol": "GAL", "decimals": 18}, "rpc": ["https://devnet.galadriel.com"], "faucets": ["https://docs.galadriel.com/faucet"], "infoURL": "https://galadriel.com"}, {"name": "Tiltyard Mainnet Subnet", "chainId": 710420, "shortName": "tiltyardmainnet", "networkId": 710420, "nativeCurrency": {"name": "TILT", "symbol": "TILT", "decimals": 18}, "rpc": ["https://subnets.avax.network/tiltyard/mainnet/rpc"], "faucets": [], "infoURL": "https://play.tiltyard.gg/"}, {"name": "<PERSON><PERSON>", "chainId": 713715, "shortName": "sei-devnet", "networkId": 713715, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "SEI", "decimals": 18}, "rpc": ["https://evm-rpc-arctic-1.sei-apis.com", "https://evm-rpc.arctic-1.seinetwork.io"], "faucets": ["https://sei-faucet.nima.enterprises", "https://sei-evm.faucetme.pro"], "infoURL": "https://www.sei.io"}, {"name": "<PERSON><PERSON>", "chainId": 743111, "shortName": "hemi-sep", "networkId": 743111, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://testnet.rpc.hemi.network/rpc"], "faucets": [], "infoURL": "https://hemi.xyz"}, {"name": "Bear Network Chain Testnet", "chainId": 751230, "shortName": "BRNKCTEST", "networkId": 751230, "nativeCurrency": {"name": "Bear Network Chain Testnet Token", "symbol": "tBRNKC", "decimals": 18}, "rpc": ["https://brnkc-test.bearnetwork.net"], "faucets": ["https://faucet.bearnetwork.net"], "infoURL": "https://bearnetwork.net"}, {"name": "Miexs Smartchain", "chainId": 761412, "shortName": "Miexs", "networkId": 761412, "nativeCurrency": {"name": "Miexs Coin", "symbol": "MIX", "decimals": 18}, "rpc": ["https://mainnet-rpc.miexs.com"], "faucets": [], "infoURL": "https://miexs.com"}, {"name": "Modularium", "chainId": 776877, "shortName": "mdlrm", "networkId": 776877, "nativeCurrency": {"name": "Modularium", "symbol": "MDM", "decimals": 18}, "rpc": ["https://fraa-dancebox-3035-rpc.a.dancebox.tanssi.network"], "faucets": [], "infoURL": "https://www.rmrk.app/"}, {"name": "OctaSpace", "chainId": 800001, "shortName": "octa", "networkId": 800001, "nativeCurrency": {"name": "OctaSpace", "symbol": "OCTA", "decimals": 18}, "rpc": ["https://rpc.octa.space", "wss://rpc.octa.space"], "faucets": [], "infoURL": "https://octa.space"}, {"name": "BIZ Smart Chain Testnet", "chainId": 808080, "shortName": "bizt-testnet", "networkId": 808080, "nativeCurrency": {"name": "tBIZT", "symbol": "tBIZT", "decimals": 18}, "rpc": ["https://rpc-testnet.bizex.io/"], "faucets": [], "infoURL": "https://www.biztoken.io/"}, {"name": "zkLink Nova Mainnet", "chainId": 810180, "shortName": "zklink-nova", "networkId": 810180, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.zklink.io", "wss://rpc.zklink.io"], "faucets": [], "infoURL": "https://zklink.io"}, {"name": "zkLink Nova Sepolia Testnet", "chainId": 810181, "shortName": "zklink-nova-sepolia", "networkId": 810181, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://sepolia.rpc.zklink.io", "wss://sepolia.rpc.zklink.io"], "faucets": [], "infoURL": "https://zklink.io"}, {"name": "zkLink Nova Goerli Testnet", "chainId": 810182, "shortName": "zklink-nova-goerli", "networkId": 810182, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://goerli.rpc.zklink.io", "wss://goerli.rpc.zklink.io"], "faucets": [], "infoURL": "https://zklink.io"}, {"name": "CURVE Mainnet", "chainId": 827431, "shortName": "CURVEm", "networkId": 827431, "nativeCurrency": {"name": "Curve", "symbol": "CURVE", "decimals": 18}, "rpc": ["https://mainnet-rpc.curvescan.io"], "faucets": [], "infoURL": "https://curvescan.io"}, {"name": "4GoodNetwork", "chainId": 846000, "shortName": "bloqs4good", "networkId": 846000, "nativeCurrency": {"name": "APTA", "symbol": "APTA", "decimals": 18}, "rpc": ["https://chain.deptofgood.com"], "faucets": [], "infoURL": "https://bloqs4good.com"}, {"name": "Dodao", "chainId": 855456, "shortName": "dodao", "networkId": 855456, "nativeCurrency": {"name": "Dodao", "symbol": "DODAO", "decimals": 18}, "rpc": ["https://fraa-dancebox-3041-rpc.a.dancebox.tanssi.network", "wss://fraa-dancebox-3041-rpc.a.dancebox.tanssi.network"], "faucets": [], "infoURL": "https://dodao.dev/"}, {"name": "BlocX Mainnet", "chainId": 879151, "shortName": "blx", "networkId": 879151, "nativeCurrency": {"name": "BlocX", "symbol": "BLX", "decimals": 18}, "rpc": ["https://mainnet-rpc.blxscan.com/"], "faucets": [], "infoURL": "https://www.blocxchain.org/"}, {"name": "REXX Mainnet", "chainId": 888882, "shortName": "REXX", "networkId": 888882, "nativeCurrency": {"name": "REXX", "symbol": "REXX", "decimals": 18}, "rpc": ["https://rpc.rexxnetwork.com"], "faucets": [], "infoURL": "https://rexxnetwork.com"}, {"name": "Vision - Mainnet", "chainId": 888888, "shortName": "vision", "networkId": 888888, "nativeCurrency": {"name": "VS", "symbol": "VS", "decimals": 18}, "rpc": ["https://infragrid.v.network/ethereum/compatible"], "faucets": [], "infoURL": "https://www.v.network"}, {"name": "Posichain Mainnet Shard 0", "chainId": 900000, "shortName": "psc-s0", "networkId": 900000, "nativeCurrency": {"name": "Posichain Native Token", "symbol": "POSI", "decimals": 18}, "rpc": ["https://api.posichain.org", "https://api.s0.posichain.org"], "faucets": [], "infoURL": "https://posichain.org"}, {"name": "Posichain Testnet Shard 0", "chainId": 910000, "shortName": "psc-t-s0", "networkId": 910000, "nativeCurrency": {"name": "Posichain Native Token", "symbol": "POSI", "decimals": 18}, "rpc": ["https://api.s0.t.posichain.org"], "faucets": ["https://faucet.posichain.org/"], "infoURL": "https://posichain.org"}, {"name": "Astria EVM Dusknet", "chainId": 912559, "shortName": "ria-dev", "networkId": 912559, "nativeCurrency": {"name": "RIA", "symbol": "RIA", "decimals": 18}, "rpc": ["https://rpc.evm.dusk-3.devnet.astria.org"], "faucets": ["https://faucet.evm.dusk-3.devnet.astria.org/"], "infoURL": "https://docs.astria.org"}, {"name": "Posichain Devnet Shard 0", "chainId": 920000, "shortName": "psc-d-s0", "networkId": 920000, "nativeCurrency": {"name": "Posichain Native Token", "symbol": "POSI", "decimals": 18}, "rpc": ["https://api.s0.d.posichain.org"], "faucets": ["https://faucet.posichain.org/"], "infoURL": "https://posichain.org"}, {"name": "Posichain Devnet Shard 1", "chainId": 920001, "shortName": "psc-d-s1", "networkId": 920001, "nativeCurrency": {"name": "Posichain Native Token", "symbol": "POSI", "decimals": 18}, "rpc": ["https://api.s1.d.posichain.org"], "faucets": ["https://faucet.posichain.org/"], "infoURL": "https://posichain.org"}, {"name": "FNCY Testnet", "chainId": 923018, "shortName": "tFNCY", "networkId": 923018, "nativeCurrency": {"name": "FNCY", "symbol": "FNCY", "decimals": 18}, "rpc": ["https://fncy-testnet-seed.fncy.world"], "faucets": ["https://faucet-testnet.fncy.world"], "infoURL": "https://fncyscan-testnet.fncy.world"}, {"name": "Jono12 Subnet", "chainId": 955081, "shortName": "jono12", "networkId": 955081, "nativeCurrency": {"name": "Jono12 Token", "symbol": "JONO", "decimals": 18}, "rpc": ["https://subnets.avax.network/jono12/testnet/rpc"], "faucets": [], "infoURL": ""}, {"name": "Eluvio Content Fabric", "chainId": 955305, "shortName": "elv", "networkId": 955305, "nativeCurrency": {"name": "ELV", "symbol": "ELV", "decimals": 18}, "rpc": ["https://host-76-74-28-226.contentfabric.io/eth/", "https://host-76-74-28-232.contentfabric.io/eth/", "https://host-76-74-29-2.contentfabric.io/eth/", "https://host-76-74-29-8.contentfabric.io/eth/", "https://host-76-74-29-34.contentfabric.io/eth/", "https://host-76-74-29-35.contentfabric.io/eth/", "https://host-154-14-211-98.contentfabric.io/eth/", "https://host-154-14-192-66.contentfabric.io/eth/", "https://host-60-240-133-202.contentfabric.io/eth/", "https://host-64-235-250-98.contentfabric.io/eth/"], "faucets": [], "infoURL": "https://eluv.io"}, {"name": "Ecrox Chain Mainnet", "chainId": 988207, "shortName": "ecrox", "networkId": 988207, "nativeCurrency": {"name": "ECROX COIN", "symbol": "ECROX", "decimals": 18}, "rpc": ["https://mainnet-rpc.ecroxscan.com/"], "faucets": [], "infoURL": "https://ecroxcoin.io/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "chainId": 999999, "shortName": "AMC", "networkId": 999999, "nativeCurrency": {"name": "AMC", "symbol": "AMC", "decimals": 18}, "rpc": ["https://node1.amchain.net"], "faucets": [], "infoURL": "https://hewe.io/"}, {"name": "Netmind Chain Testnet", "chainId": 1100789, "shortName": "nmtTest", "networkId": 1100789, "nativeCurrency": {"name": "NMT", "symbol": "NMT", "decimals": 18}, "rpc": ["https://testblock.protago-dev.com"], "faucets": [], "infoURL": "https://netmind.ai"}, {"name": "Tiltyard Subnet", "chainId": 1127469, "shortName": "tiltyard", "networkId": 1127469, "nativeCurrency": {"name": "Tiltyard Token", "symbol": "TILTG", "decimals": 18}, "rpc": ["https://subnets.avax.network/tiltyard/testnet/rpc"], "faucets": [], "infoURL": ""}, {"name": "zKatana", "chainId": 1261120, "shortName": "azktn", "networkId": 1261120, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.zkatana.gelato.digital", "https://rpc.startale.com/zkatana", "https://astar-zkatana.drpc.org", "wss://astar-zkatana.drpc.org"], "faucets": [], "infoURL": "https://astar.network"}, {"name": "Etho Protocol", "chainId": 1313114, "shortName": "etho", "networkId": 1313114, "nativeCurrency": {"name": "Etho Protocol", "symbol": "ETHO", "decimals": 18}, "rpc": ["https://rpc.ethoprotocol.com"], "faucets": [], "infoURL": "https://ethoprotocol.com"}, {"name": "Xerom", "chainId": 1313500, "shortName": "xero", "networkId": 1313500, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "XERO", "decimals": 18}, "rpc": ["https://rpc.xerom.org"], "faucets": [], "infoURL": "https://xerom.org"}, {"name": "Kintsugi", "chainId": 1337702, "shortName": "<PERSON><PERSON><PERSON>", "networkId": 1337702, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "kiETH", "decimals": 18}, "rpc": ["https://rpc.kintsugi.themerge.dev"], "faucets": ["http://fauceth.komputing.org?chain=1337702&address=${ADDRESS}", "https://faucet.kintsugi.themerge.dev"], "infoURL": "https://kintsugi.themerge.dev/"}, {"name": "Kiln", "chainId": 1337802, "shortName": "kiln", "networkId": 1337802, "nativeCurrency": {"name": "Testnet ETH", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.kiln.themerge.dev"], "faucets": ["https://faucet.kiln.themerge.dev", "https://kiln-faucet.pk910.de", "https://kilnfaucet.com"], "infoURL": "https://kiln.themerge.dev/"}, {"name": "Zhejiang", "chainId": 1337803, "shortName": "zhejiang", "networkId": 1337803, "nativeCurrency": {"name": "Testnet ETH", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.zhejiang.ethpandaops.io"], "faucets": ["https://faucet.zhejiang.ethpandaops.io", "https://zhejiang-faucet.pk910.de"], "infoURL": "https://zhejiang.ethpandaops.io"}, {"name": "PlayFi Albireo Testnet", "chainId": 1612127, "shortName": "alberio", "networkId": 1612127, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://albireo-rpc.playfi.ai"], "faucets": [], "infoURL": "https://www.playfi.ai/"}, {"name": "Turkey Demo Dev", "chainId": 1731313, "shortName": "TDD", "networkId": 1731313, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://devchain-poa.huabeizhenxuan.com"], "faucets": [], "infoURL": ""}, {"name": "DeBank Testnet", "chainId": 2021398, "shortName": "dbk", "networkId": 2021398, "nativeCurrency": {"name": "DeBank USD", "symbol": "USD", "decimals": 18}, "rpc": ["http://rpc.testnet.debank.com"], "faucets": [], "infoURL": "https://debank.com"}, {"name": "Plian Mainnet Main", "chainId": 2099156, "shortName": "plian-mainnet", "networkId": 2099156, "nativeCurrency": {"name": "Plian Token", "symbol": "PI", "decimals": 18}, "rpc": ["https://mainnet.plian.io/pchain"], "faucets": [], "infoURL": "https://plian.org/"}, {"name": "PlatON Dev Testnet Deprecated", "chainId": 2203181, "shortName": "<PERSON><PERSON><PERSON><PERSON>", "networkId": 1, "nativeCurrency": {"name": "LAT", "symbol": "lat", "decimals": 18}, "rpc": ["https://devnetopenapi2.platon.network/rpc", "wss://devnetopenapi2.platon.network/ws"], "faucets": ["https://devnet2faucet.platon.network/faucet"], "infoURL": "https://www.platon.network"}, {"name": "PlatON Dev Testnet2", "chainId": 2206132, "shortName": "platondev2", "networkId": 1, "nativeCurrency": {"name": "LAT", "symbol": "lat", "decimals": 18}, "rpc": ["https://devnet2openapi.platon.network/rpc", "wss://devnet2openapi.platon.network/ws"], "faucets": ["https://devnet2faucet.platon.network/faucet"], "infoURL": "https://www.platon.network"}, {"name": "DPU Chain", "chainId": 2611555, "shortName": "DPU", "networkId": 2611555, "nativeCurrency": {"name": "DGC", "symbol": "DGC", "decimals": 18}, "rpc": ["https://sc-rpc.dpu.ac.th"], "faucets": [], "infoURL": ""}, {"name": "SaharaAI Network", "chainId": 3132023, "shortName": "sahara", "networkId": 3132023, "nativeCurrency": {"name": "SAHARA", "symbol": "SAH", "decimals": 18}, "rpc": ["https://mainnet.saharalabs.ai"], "faucets": [], "infoURL": "https://saharalabs.ai"}, {"name": "Filecoin - Butterfly testnet", "chainId": 3141592, "shortName": "filecoin-butterfly", "networkId": 3141592, "nativeCurrency": {"name": "testnet filecoin", "symbol": "tFIL", "decimals": 18}, "rpc": [], "faucets": ["https://faucet.butterfly.fildev.network"], "infoURL": "https://filecoin.io"}, {"name": "Manta Pacific Testnet", "chainId": 3441005, "shortName": "mantaTestnet", "networkId": 3441005, "nativeCurrency": {"name": "Manta", "symbol": "MANTA", "decimals": 18}, "rpc": ["https://manta-testnet.calderachain.xyz/http", "https://manta-pacific-testnet.drpc.org", "wss://manta-pacific-testnet.drpc.org"], "faucets": [], "infoURL": "https://manta-testnet.caldera.dev/"}, {"name": "Manta Pacific Sepolia Testnet", "chainId": 3441006, "shortName": "mantaSepoliaTestnet", "networkId": 3441006, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://pacific-rpc.sepolia-testnet.manta.network/http"], "faucets": [], "infoURL": "https://manta-testnet.caldera.dev/"}, {"name": "AltLayer Zero Gas Network", "chainId": 4000003, "shortName": "alt-zerogas", "networkId": 4000003, "nativeCurrency": {"name": "ZERO", "symbol": "ZERO", "decimals": 18}, "rpc": ["https://zero.alt.technology"], "faucets": [], "infoURL": "https://altlayer.io"}, {"name": "Worlds Caldera", "chainId": 4281033, "shortName": "worldscal", "networkId": 4281033, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://worlds-test.calderachain.xyz/http"], "faucets": [], "infoURL": "https://caldera.xyz/"}, {"name": "MXC Wannsee zkEVM Testnet", "chainId": 5167003, "shortName": "MXCdiscontinued", "networkId": 5167003, "nativeCurrency": {"name": "MXC Wannsee zkEVM Testnet", "symbol": "MXC", "decimals": 18}, "rpc": ["https://wannsee-rpc.mxc.com"], "faucets": [], "infoURL": "https://wannsee.mxc.com/docs/intro"}, {"name": "Moonchain Geneva Testnet", "chainId": 5167004, "shortName": "MXC", "networkId": 5167004, "nativeCurrency": {"name": "Moonchain Geneva Testnet", "symbol": "MXC", "decimals": 18}, "rpc": ["https://geneva-rpc.moonchain.com"], "faucets": [], "infoURL": "https://doc.mxc.com/docs/intro"}, {"name": "Electroneum Testnet", "chainId": 5201420, "shortName": "etn-testnet", "networkId": 5201420, "nativeCurrency": {"name": "Electroneum", "symbol": "ETN", "decimals": 18}, "rpc": ["https://testnet-rpc.electroneum.com"], "faucets": [], "infoURL": "https://electroneum.com"}, {"name": "Reactive <PERSON><PERSON><PERSON>", "chainId": 5318008, "shortName": "kreact", "networkId": 5318008, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "REACT", "decimals": 18}, "rpc": ["https://kopli-rpc.reactive.network", "http://kopli-rpc.rkt.ink"], "faucets": ["https://dev.reactive.network/docs/kopli-testnet#faucet"], "infoURL": "https://reactive.network"}, {"name": "Imversed Mainnet", "chainId": 5555555, "shortName": "imversed", "networkId": 5555555, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>d <PERSON>", "symbol": "IMV", "decimals": 18}, "rpc": ["https://jsonrpc.imversed.network", "https://ws-jsonrpc.imversed.network"], "faucets": [], "infoURL": "https://imversed.com"}, {"name": "Imversed Testnet", "chainId": 5555558, "shortName": "imversed-testnet", "networkId": 5555558, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>d <PERSON>", "symbol": "IMV", "decimals": 18}, "rpc": ["https://jsonrpc-test.imversed.network", "https://ws-jsonrpc-test.imversed.network"], "faucets": [], "infoURL": "https://imversed.com"}, {"name": "Astar zKyoto", "chainId": 6038361, "shortName": "azkyt", "networkId": 6038361, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.startale.com/zkyoto", "https://rpc.zkyoto.gelato.digital"], "faucets": [], "infoURL": "https://astar.network"}, {"name": "Safe(AnWang) Mainnet", "chainId": 6666665, "shortName": "SafeMainnet", "networkId": 6666665, "nativeCurrency": {"name": "SAFE(AnWang)", "symbol": "SAFE", "decimals": 18}, "rpc": ["https://rpc.anwang.com"], "faucets": [], "infoURL": "https://www.anwang.com"}, {"name": "Safe(AnWang) Testnet", "chainId": 6666666, "shortName": "SafeTestnet", "networkId": 6666666, "nativeCurrency": {"name": "SAFE(AnWang)", "symbol": "SAFE", "decimals": 18}, "rpc": ["https://rpc-testnet.anwang.com"], "faucets": [], "infoURL": "https://www.anwang.com"}, {"name": "Saakuru Mainnet", "chainId": 7225878, "shortName": "saakuru", "networkId": 7225878, "nativeCurrency": {"name": "OAS", "symbol": "OAS", "decimals": 18}, "rpc": ["https://rpc.saakuru.network"], "faucets": [], "infoURL": "https://saakuru.network"}, {"name": "OpenVessel", "chainId": 7355310, "shortName": "vsl", "networkId": 7355310, "nativeCurrency": {"name": "Vessel ETH", "symbol": "VETH", "decimals": 18}, "rpc": ["https://mainnet-external.openvessel.io"], "faucets": [], "infoURL": "https://www.openvessel.io"}, {"name": "QL1 Testnet", "chainId": 7668378, "shortName": "tqom", "networkId": 7668378, "nativeCurrency": {"name": "Shiba Predator", "symbol": "QOM", "decimals": 18}, "rpc": ["https://rpc.testnet.qom.one"], "faucets": ["https://faucet.qom.one"], "infoURL": "https://qom.one"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 7762959, "shortName": "music", "networkId": 7762959, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "MUSIC", "decimals": 18}, "rpc": ["https://mewapi.musicoin.tw"], "faucets": [], "infoURL": "https://musicoin.tw"}, {"name": "<PERSON><PERSON>", "chainId": 7777777, "shortName": "zora", "networkId": 7777777, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.zora.energy/"], "faucets": [], "infoURL": "https://zora.energy"}, {"name": "Plian Mainnet Subchain 1", "chainId": 8007736, "shortName": "plian-mainnet-l2", "networkId": 8007736, "nativeCurrency": {"name": "Plian Token", "symbol": "PI", "decimals": 18}, "rpc": ["https://mainnet.plian.io/child_0"], "faucets": [], "infoURL": "https://plian.org"}, {"name": "Hokum", "chainId": 8080808, "shortName": "hokum", "networkId": 8080808, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://mainnet.hokum.gg"], "faucets": [], "infoURL": "https://hokum.gg"}, {"name": "HAPchain", "chainId": 8794598, "shortName": "hap", "networkId": 8794598, "nativeCurrency": {"name": "HAP", "symbol": "HAP", "decimals": 18}, "rpc": ["https://jsonrpc.hap.land"], "faucets": [], "infoURL": "https://hap.land"}, {"name": "Quarix Testnet", "chainId": 8888881, "shortName": "quarix-testnet", "networkId": 8888881, "nativeCurrency": {"name": "QARE", "symbol": "QARE", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": ""}, {"name": "Quarix", "chainId": 8888888, "shortName": "quarix", "networkId": 8888888, "nativeCurrency": {"name": "QARE", "symbol": "QARE", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": ""}, {"name": "XCAP", "chainId": 9322252, "shortName": "xcap", "networkId": 9322252, "nativeCurrency": {"name": "Gas", "symbol": "GAS", "decimals": 18}, "rpc": ["https://xcap-mainnet.relay.xcap.network/znzvh2ueyvm2yts5fv5gnul395jbkfb2/rpc1"], "faucets": [], "infoURL": "https://xcap.network/"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 9322253, "shortName": "milv", "networkId": 9322253, "nativeCurrency": {"name": "Gas", "symbol": "GAS", "decimals": 18}, "rpc": ["https://xcap-milvine.relay.xcap.network/zj5l55ftsgi027kz4nf14vs8d89inego/rpc1"], "faucets": [], "infoURL": "https://xcap.network/"}, {"name": "Plian Testnet Subchain 1", "chainId": 10067275, "shortName": "plian-testnet-l2", "networkId": 10067275, "nativeCurrency": {"name": "Plian Token", "symbol": "TPI", "decimals": 18}, "rpc": ["https://testnet.plian.io/child_test"], "faucets": [], "infoURL": "https://plian.org/"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 10101010, "shortName": "SVRNm", "networkId": 10101010, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "SVRN", "decimals": 18}, "rpc": ["https://mainnet-rpc.soverun.com"], "faucets": ["https://faucet.soverun.com"], "infoURL": "https://soverun.com"}, {"name": "Sepolia", "chainId": 11155111, "shortName": "sep", "networkId": 11155111, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.sepolia.org", "https://rpc2.sepolia.org", "https://rpc-sepolia.rockx.com", "https://rpc.sepolia.ethpandaops.io", "https://sepolia.infura.io/v3/${INFURA_API_KEY}", "wss://sepolia.infura.io/v3/${INFURA_API_KEY}", "https://sepolia.gateway.tenderly.co", "wss://sepolia.gateway.tenderly.co", "https://ethereum-sepolia-rpc.publicnode.com", "wss://ethereum-sepolia-rpc.publicnode.com", "https://sepolia.drpc.org", "wss://sepolia.drpc.org"], "faucets": ["http://fauceth.komputing.org?chain=11155111&address=${ADDRESS}"], "infoURL": "https://sepolia.otterscan.io"}, {"name": "OP Sepolia Testnet", "chainId": 11155420, "shortName": "opsep", "networkId": 11155420, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://sepolia.optimism.io", "https://optimism-sepolia.drpc.org", "wss://optimism-sepolia.drpc.org"], "faucets": ["https://app.optimism.io/faucet"], "infoURL": "https://optimism.io"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 13371337, "shortName": "tpep", "networkId": 13371337, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "TPEP", "decimals": 18}, "rpc": ["https://churchill-rpc.pepchain.io"], "faucets": [], "infoURL": "https://pepchain.io"}, {"name": "Andus<PERSON>in Mainnet", "chainId": 14288640, "shortName": "anduschain-mainnet", "networkId": 14288640, "nativeCurrency": {"name": "DAON", "symbol": "DEB", "decimals": 18}, "rpc": ["https://rpc.anduschain.io/rpc", "wss://rpc.anduschain.io/ws"], "faucets": [], "infoURL": "https://anduschain.io/"}, {"name": "Plian Testnet Main", "chainId": 16658437, "shortName": "plian-testnet", "networkId": 16658437, "nativeCurrency": {"name": "Plian Testnet Token", "symbol": "TPI", "decimals": 18}, "rpc": ["https://testnet.plian.io/testnet"], "faucets": [], "infoURL": "https://plian.org"}, {"name": "Lambda Chain Testnet", "chainId": 17000920, "shortName": "tlambda", "networkId": 17000920, "nativeCurrency": {"name": "ETH", "symbol": "ETH", "decimals": 18}, "rpc": ["https://testnrpc.lambda.im/"], "faucets": [], "infoURL": "https://lambda.im"}, {"name": "IOLite", "chainId": 18289463, "shortName": "ilt", "networkId": 18289463, "nativeCurrency": {"name": "IOLite Ether", "symbol": "ILT", "decimals": 18}, "rpc": ["https://net.iolite.io"], "faucets": [], "infoURL": "https://iolite.io"}, {"name": "Stability Testnet", "chainId": 20180427, "shortName": "stabilitytestnet", "networkId": 20180427, "nativeCurrency": {"name": "FREE", "symbol": "FREE", "decimals": 18}, "rpc": ["https://free.testnet.stabilityprotocol.com"], "faucets": [], "infoURL": "https://stabilityprotocol.com"}, {"name": "SmartMesh Mainnet", "chainId": 20180430, "shortName": "spectrum", "networkId": 1, "nativeCurrency": {"name": "SmartMesh Native Token", "symbol": "SMT", "decimals": 18}, "rpc": ["https://jsonapi1.smartmesh.cn"], "faucets": [], "infoURL": "https://smartmesh.io"}, {"name": "quarkblockchain", "chainId": 20181205, "shortName": "qki", "networkId": 20181205, "nativeCurrency": {"name": "quarkblockchain Native Token", "symbol": "QKI", "decimals": 18}, "rpc": ["https://hz.rpc.qkiscan.cn", "https://jp.rpc.qkiscan.io", "https://rpc1.qkiscan.io", "https://rpc2.qkiscan.io", "https://rpc3.qkiscan.io"], "faucets": [], "infoURL": "https://quarkblockchain.org/"}, {"name": "Pego Network", "chainId": ********, "shortName": "pg", "networkId": ********, "nativeCurrency": {"name": "Pego Native Token", "symbol": "PG", "decimals": 18}, "rpc": ["https://pegorpc.com", "https://node1.pegorpc.com", "https://node2.pegorpc.com", "https://node3.pegorpc.com"], "faucets": [], "infoURL": "https://pego.network"}, {"name": "DeBank Sepolia Testnet", "chainId": ********, "shortName": "dbkse", "networkId": ********, "nativeCurrency": {"name": "DeBank USD", "symbol": "USD", "decimals": 18}, "rpc": ["https://sepolia-rpc.testnet.debank.com"], "faucets": [], "infoURL": "https://debank.com"}, {"name": "Hokum Testnet", "chainId": ********, "shortName": "hokum-testnet", "networkId": ********, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://testnet.hokum.gg"], "faucets": [], "infoURL": "https://hokum.gg"}, {"name": "Excelon Mainnet", "chainId": ********, "shortName": "xlon", "networkId": ********, "nativeCurrency": {"name": "Excelon", "symbol": "xlon", "decimals": 18}, "rpc": ["https://edgewallet1.xlon.org/"], "faucets": [], "infoURL": "https://xlon.org"}, {"name": "Excoincial Chain Volta-Testnet", "chainId": ********, "shortName": "exlvolta", "networkId": ********, "nativeCurrency": {"name": "TExlcoin", "symbol": "TEXL", "decimals": 18}, "rpc": ["https://testnet-rpc.exlscan.com"], "faucets": ["https://faucet.exlscan.com"], "infoURL": ""}, {"name": "Excoincial Chain Mainnet", "chainId": ********, "shortName": "exl", "networkId": ********, "nativeCurrency": {"name": "Exlcoin", "symbol": "EXL", "decimals": 18}, "rpc": ["https://rpc.exlscan.com"], "faucets": [], "infoURL": ""}, {"name": "Ancient8 Testnet", "chainId": 28122024, "shortName": "a8", "networkId": 28122024, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpcv2-testnet.ancient8.gg"], "faucets": [], "infoURL": "https://ancient8.gg/"}, {"name": "Auxilium Network Mainnet", "chainId": 28945486, "shortName": "auxi", "networkId": 28945486, "nativeCurrency": {"name": "Auxilium coin", "symbol": "AUX", "decimals": 18}, "rpc": ["https://rpc.auxilium.global"], "faucets": [], "infoURL": "https://auxilium.global"}, {"name": "Flachain Mainnet", "chainId": 29032022, "shortName": "fla", "networkId": 29032022, "nativeCurrency": {"name": "Flacoin", "symbol": "FLA", "decimals": 18}, "rpc": ["https://flachain.flaexchange.top/"], "faucets": [], "infoURL": "https://www.flaexchange.top"}, {"name": "Filecoin - Local testnet", "chainId": 31415926, "shortName": "filecoin-local", "networkId": 31415926, "nativeCurrency": {"name": "testnet filecoin", "symbol": "tFIL", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://filecoin.io"}, {"name": "Joys Digital Mainnet", "chainId": 35855456, "shortName": "JOYS", "networkId": 35855456, "nativeCurrency": {"name": "JOYS", "symbol": "JOYS", "decimals": 18}, "rpc": ["https://node.joys.digital"], "faucets": [], "infoURL": "https://joys.digital"}, {"name": "SKALE Nebula Hub Testnet", "chainId": 37084624, "shortName": "nebula-testnet", "networkId": 37084624, "nativeCurrency": {"name": "sFUEL", "symbol": "sFUEL", "decimals": 18}, "rpc": ["https://testnet.skalenodes.com/v1/lanky-ill-funny-testnet", "wss://testnet.skalenodes.com/v1/ws/lanky-ill-funny-testnet"], "faucets": ["https://www.sfuelstation.com/"], "infoURL": "https://nebulachain.io/"}, {"name": "Kingdom Chain", "chainId": 39916801, "shortName": "kchain", "networkId": 39916801, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "KOZI", "decimals": 18}, "rpc": ["https://kingdomchain.observer/rpc"], "faucets": [], "infoURL": "https://www.beastkingdom.io/"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 43214913, "shortName": "mais", "networkId": 43214913, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "MAI", "decimals": 18}, "rpc": ["http://174.138.9.169:9650/ext/bc/VUKSzFZKckx4PoZF9gX5QAqLPxbLzvu1vcssPG5QuodaJtdHT/rpc"], "faucets": [], "infoURL": ""}, {"name": "Aquachain", "chainId": 61717561, "shortName": "aqua", "networkId": 61717561, "nativeCurrency": {"name": "Aquachain <PERSON>", "symbol": "AQUA", "decimals": 18}, "rpc": ["https://c.onical.org", "https://tx.aquacha.in/api"], "faucets": ["https://aquacha.in/faucet"], "infoURL": "https://aquachain.github.io"}, {"name": "Autonity <PERSON> (Thames) Testnet", "chainId": 65010000, "shortName": "bakerloo-0", "networkId": 65010000, "nativeCurrency": {"name": "Bakerloo Auton", "symbol": "ATN", "decimals": 18}, "rpc": [], "faucets": ["https://faucet.autonity.org/"], "infoURL": "https://autonity.org/"}, {"name": "Autonity Bakerloo (Barada) Testnet", "chainId": 65010001, "shortName": "bakerloo-01", "networkId": 65010001, "nativeCurrency": {"name": "Bakerloo Auton", "symbol": "ATN", "decimals": 18}, "rpc": [], "faucets": ["https://faucet.autonity.org/"], "infoURL": "https://autonity.org/"}, {"name": "Autonity <PERSON>o (Sumida) Testnet", "chainId": 65010002, "shortName": "bakerloo-02", "networkId": 65010002, "nativeCurrency": {"name": "Bakerloo Auton", "symbol": "ATN", "decimals": 18}, "rpc": ["https://rpc1.bakerloo.autonity.org/", "wss://rpc1.bakerloo.autonity.org/ws/"], "faucets": ["https://faucet.autonity.org/"], "infoURL": "https://autonity.org/"}, {"name": "Autonity <PERSON>lly (Thames) Testnet", "chainId": 65100000, "shortName": "piccadilly-0", "networkId": 65100000, "nativeCurrency": {"name": "Piccadilly Auton", "symbol": "ATN", "decimals": 18}, "rpc": [], "faucets": ["https://faucet.autonity.org/"], "infoURL": "https://autonity.org/"}, {"name": "Autonity Piccadilly (Barada) Testnet", "chainId": 65100001, "shortName": "piccadilly-01", "networkId": 65100001, "nativeCurrency": {"name": "Piccadilly Auton", "symbol": "ATN", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://autonity.org/"}, {"name": "Autonity Piccadilly (Sumida) Testnet", "chainId": 65100002, "shortName": "piccadilly-02", "networkId": 65100002, "nativeCurrency": {"name": "Piccadilly Auton", "symbol": "ATN", "decimals": 18}, "rpc": ["https://rpc1.piccadilly.autonity.org/", "wss://rpc1.piccadilly.autonity.org/ws/"], "faucets": [], "infoURL": "https://autonity.org/"}, {"name": "Frame <PERSON>", "chainId": 68840142, "shortName": "frametest", "networkId": 68840142, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.testnet.frame.xyz/http"], "faucets": ["https://faucet.triangleplatform.com/frame/testnet"], "infoURL": "https://www.frame.xyz/"}, {"name": "0xHash Testnet", "chainId": 77787778, "shortName": "HETH", "networkId": 77787778, "nativeCurrency": {"name": "0xHash", "symbol": "HETH", "decimals": 18}, "rpc": ["https://rpc-test.0xhash.io"], "faucets": [], "infoURL": "https://0xhash.io"}, {"name": "T.E.A.M Blockchain", "chainId": 88888888, "shortName": "team", "networkId": 88888888, "nativeCurrency": {"name": "TEAM", "symbol": "$TEAM", "decimals": 18}, "rpc": ["https://rpc.teamblockchain.team"], "faucets": [], "infoURL": "https://teamblockchain.team"}, {"name": "Polygon Blackberry", "chainId": 94204209, "shortName": "polygon-blackberry", "networkId": 94204209, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.polygon-blackberry.gelato.digital", "wss://ws.polygon-blackberry.gelato.digital"], "faucets": [], "infoURL": "https://raas.gelato.network/rollups/details/public/polygon-blackberry"}, {"name": "Joys Digital TestNet", "chainId": 99415706, "shortName": "TOYS", "networkId": 99415706, "nativeCurrency": {"name": "TOYS", "symbol": "TOYS", "decimals": 18}, "rpc": ["https://toys.joys.cash/"], "faucets": ["https://faucet.joys.digital/"], "infoURL": "https://joys.digital"}, {"name": "Cyber Testnet", "chainId": 111557560, "shortName": "cysep", "networkId": 111557560, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://cyber-testnet.alt.technology/", "wss://cyber-testnet.alt.technology/ws", "https://rpc.testnet.cyber.co/", "wss://rpc.testnet.cyber.co/"], "faucets": [], "infoURL": "https://cyber.co/"}, {"name": "OP Celestia <PERSON>", "chainId": 123420111, "shortName": "opcelestia-raspberry", "networkId": 123420111, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.opcelestia-raspberry.gelato.digital", "wss://ws.opcelestia-raspberry.gelato.digital"], "faucets": [], "infoURL": "https://raas.gelato.network/rollups/details/public/opcelestia-raspberry"}, {"name": "Plume Testnet", "chainId": 161221135, "shortName": "plume-testnet", "networkId": 161221135, "nativeCurrency": {"name": "Plume Sep<PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://testnet-rpc.plumenetwork.xyz/http", "wss://testnet-rpc.plumenetwork.xyz/ws"], "faucets": [], "infoURL": "https://www.plumenetwork.xyz/"}, {"name": "Blast Sepolia Testnet", "chainId": 168587773, "shortName": "blastsepolia", "networkId": 168587773, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://sepolia.blast.io", "https://blast-sepolia.drpc.org", "wss://blast-sepolia.drpc.org"], "faucets": ["https://faucet.quicknode.com/blast/sepolia"], "infoURL": "https://blast.io/"}, {"name": "Gather Mainnet Network", "chainId": 192837465, "shortName": "GTH", "networkId": 192837465, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "GTH", "decimals": 18}, "rpc": ["https://mainnet.gather.network"], "faucets": [], "infoURL": "https://gather.network"}, {"name": "Kanazawa", "chainId": 222000222, "shortName": "kanazawa", "networkId": 222000222, "nativeCurrency": {"name": "gMeld", "symbol": "gMELD", "decimals": 18}, "rpc": ["https://testnet-rpc.meld.com"], "faucets": [], "infoURL": "https://meld.com"}, {"name": "Neon EVM Devnet", "chainId": 245022926, "shortName": "neonevm-devnet", "networkId": 245022926, "nativeCurrency": {"name": "Neon", "symbol": "NEON", "decimals": 18}, "rpc": ["https://devnet.neonevm.org", "https://neon-evm-devnet.drpc.org", "wss://neon-evm-devnet.drpc.org"], "faucets": ["https://neonfaucet.org"], "infoURL": "https://neon-labs.org"}, {"name": "Neon EVM Mainnet", "chainId": 245022934, "shortName": "neonevm-mainnet", "networkId": 245022934, "nativeCurrency": {"name": "Neon", "symbol": "NEON", "decimals": 18}, "rpc": ["https://neon-proxy-mainnet.solana.p2p.org", "https://neon-evm.drpc.org", "wss://neon-evm.drpc.org"], "faucets": [], "infoURL": "https://neonevm.org"}, {"name": "Neon EVM TestNet", "chainId": 245022940, "shortName": "neonevm-testnet", "networkId": 245022940, "nativeCurrency": {"name": "Neon", "symbol": "NEON", "decimals": 18}, "rpc": ["https://testnet.neonevm.org"], "faucets": [], "infoURL": "https://neon-labs.org"}, {"name": "Razor <PERSON> Chain", "chainId": 278611351, "shortName": "razor", "networkId": 278611351, "nativeCurrency": {"name": "sFuel", "symbol": "SFUEL", "decimals": 18}, "rpc": ["https://mainnet.skalenodes.com/v1/turbulent-unique-scheat"], "faucets": ["https://faucet.razorscan.io/"], "infoURL": "https://razor.network"}, {"name": "OneLedger Mainnet", "chainId": 311752642, "shortName": "oneledger", "networkId": 311752642, "nativeCurrency": {"name": "OLT", "symbol": "OLT", "decimals": 18}, "rpc": ["https://mainnet-rpc.oneledger.network"], "faucets": [], "infoURL": "https://oneledger.io"}, {"name": "<PERSON>d", "chainId": 333000333, "shortName": "meld", "networkId": 333000333, "nativeCurrency": {"name": "gMeld", "symbol": "gMELD", "decimals": 18}, "rpc": ["https://rpc-1.meld.com"], "faucets": [], "infoURL": "https://meld.com"}, {"name": "Deprecated SKALE Calypso Hub Testnet", "chainId": 344106930, "shortName": "deprected-calypso-testnet", "networkId": 344106930, "nativeCurrency": {"name": "sFUEL", "symbol": "sFUEL", "decimals": 18}, "rpc": ["https://staging-v3.skalenodes.com/v1/staging-utter-unripe-menkar"], "faucets": ["https://sfuel.dirtroad.dev/staging"], "infoURL": "https://calypsohub.network/"}, {"name": "Gather Testnet Network", "chainId": 356256156, "shortName": "tGTH", "networkId": 356256156, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "GTH", "decimals": 18}, "rpc": ["https://testnet.gather.network"], "faucets": [], "infoURL": "https://gather.network"}, {"name": "Deprecated SKALE Europa Hub Testnet", "chainId": 476158412, "shortName": "deprecated-europa-testnet", "networkId": 476158412, "nativeCurrency": {"name": "sFUEL", "symbol": "sFUEL", "decimals": 18}, "rpc": ["https://staging-v3.skalenodes.com/v1/staging-legal-crazy-castor"], "faucets": ["https://sfuel.dirtroad.dev/staging"], "infoURL": "https://europahub.network/"}, {"name": "Gather Devnet Network", "chainId": 486217935, "shortName": "dGTH", "networkId": 486217935, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "GTH", "decimals": 18}, "rpc": ["https://devnet.gather.network"], "faucets": [], "infoURL": "https://gather.network"}, {"name": "Deprecated SKALE Nebula Hub Testnet", "chainId": 503129905, "shortName": "deprecated-nebula-testnet", "networkId": 503129905, "nativeCurrency": {"name": "sFUEL", "symbol": "sFUEL", "decimals": 18}, "rpc": ["https://staging-v3.skalenodes.com/v1/staging-faint-slimy-achird", "wss://staging-v3.skalenodes.com/v1/ws/staging-faint-slimy-achird"], "faucets": ["https://sfuel.dirtroad.dev/staging"], "infoURL": "https://nebulachain.io/"}, {"name": "Degen Chain", "chainId": 666666666, "shortName": "degen-chain", "networkId": 666666666, "nativeCurrency": {"name": "DEGEN", "symbol": "DEGEN", "decimals": 18}, "rpc": ["https://rpc.degen.tips"], "faucets": [], "infoURL": "https://degen.tips"}, {"name": "Ancient8", "chainId": 888888888, "shortName": "ancient8", "networkId": 888888888, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc.ancient8.gg"], "faucets": [], "infoURL": "https://ancient8.gg/"}, {"name": "PTCESCAN Testnet", "chainId": 889910245, "shortName": "PTCE", "networkId": 889910245, "nativeCurrency": {"name": "PTCE", "symbol": "PTCE", "decimals": 18}, "rpc": ["https://rpc-testnet.ptcscan.io"], "faucets": ["https://faucet.ptcscan.io/"], "infoURL": "https://ptcscan.io"}, {"name": "PTCESCAN Mainnet", "chainId": 889910246, "shortName": "POLYTECH", "networkId": 889910246, "nativeCurrency": {"name": "PTCE", "symbol": "PTCE", "decimals": 18}, "rpc": ["https://rpc.ptcscan.io"], "faucets": [], "infoURL": "https://ptcscan.io"}, {"name": "SKALE Calypso Hub Testnet", "chainId": 974399131, "shortName": "calypso-testnet", "networkId": 974399131, "nativeCurrency": {"name": "sFUEL", "symbol": "sFUEL", "decimals": 18}, "rpc": ["https://testnet.skalenodes.com/v1/giant-half-dual-testnet"], "faucets": ["https://www.sfuelstation.com/"], "infoURL": "https://calypsohub.network/"}, {"name": "Zora <PERSON>net", "chainId": 999999999, "shortName": "zsep", "networkId": 999999999, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://sepolia.rpc.zora.energy"], "faucets": [], "infoURL": "https://zora.energy"}, {"name": "SKALE Titan Hub Testnet", "chainId": 1020352220, "shortName": "titan-testnet", "networkId": 1020352220, "nativeCurrency": {"name": "sFUEL", "symbol": "sFUEL", "decimals": 18}, "rpc": ["https://testnet.skalenodes.com/v1/aware-fake-trim-testnet", "wss://testnet.skalenodes.com/v1/ws/aware-fake-trim-testnet"], "faucets": ["https://www.sfuelstation.com/"], "infoURL": ""}, {"name": "IPOS Network", "chainId": 1122334455, "shortName": "ipos", "networkId": 1122334455, "nativeCurrency": {"name": "IPOS Network Ether", "symbol": "IPOS", "decimals": 18}, "rpc": ["https://rpc.iposlab.com", "https://rpc2.iposlab.com"], "faucets": [], "infoURL": "https://iposlab.com"}, {"name": "CyberdeckNet", "chainId": 1146703430, "shortName": "cyb", "networkId": 1146703430, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "CYB", "decimals": 18}, "rpc": ["http://cybeth1.cyberdeck.eu:8545"], "faucets": [], "infoURL": "https://cyberdeck.eu"}, {"name": "HUMAN Protocol", "chainId": 1273227453, "shortName": "human-mainnet", "networkId": 1273227453, "nativeCurrency": {"name": "sFUEL", "symbol": "sFUEL", "decimals": 18}, "rpc": ["https://mainnet.skalenodes.com/v1/wan-red-ain"], "faucets": ["https://dashboard.humanprotocol.org/faucet"], "infoURL": "https://www.humanprotocol.org"}, {"name": "Aurora Mainnet", "chainId": 1313161554, "shortName": "aurora", "networkId": 1313161554, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://mainnet.aurora.dev", "https://aurora.drpc.org", "wss://aurora.drpc.org"], "faucets": [], "infoURL": "https://aurora.dev"}, {"name": "Aurora Testnet", "chainId": 1313161555, "shortName": "aurora-testnet", "networkId": 1313161555, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://testnet.aurora.dev/", "https://aurora-testnet.drpc.org", "wss://aurora-testnet.drpc.org"], "faucets": [], "infoURL": "https://aurora.dev"}, {"name": "Aurora Betanet", "chainId": 1313161556, "shortName": "aurora-betanet", "networkId": 1313161556, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://aurora.dev"}, {"name": "PowerGold", "chainId": 1313161560, "shortName": "powergold", "networkId": 1313161560, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://powergold.aurora.dev"], "faucets": [], "infoURL": "https://www.powergold.tech"}, {"name": "SKALE Titan Hub", "chainId": 1350216234, "shortName": "titan-mainnet", "networkId": 1350216234, "nativeCurrency": {"name": "sFUEL", "symbol": "sFUEL", "decimals": 18}, "rpc": ["https://mainnet.skalenodes.com/v1/parallel-stormy-spica", "wss://mainnet.skalenodes.com/v1/ws/parallel-stormy-spica"], "faucets": ["https://sfuel.skale.network/"], "infoURL": ""}, {"name": "Chaos (SKALE Testnet)", "chainId": 1351057110, "shortName": "chaos-tenet", "networkId": 1351057110, "nativeCurrency": {"name": "sFUEL", "symbol": "sFUEL", "decimals": 18}, "rpc": ["https://staging-v3.skalenodes.com/v1/staging-fast-active-bellatrix"], "faucets": ["https://sfuel.skale.network/staging/chaos"], "infoURL": "https://docs.skale.network/develop/"}, {"name": "RARI Chain Mainnet", "chainId": 1380012617, "shortName": "rari-mainnet", "networkId": 1380012617, "nativeCurrency": {"name": "Ethereum", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rari.calderachain.xyz/http"], "faucets": [], "infoURL": "https://rarichain.org/"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": 1380996178, "shortName": "rptr", "networkId": 1380996178, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "RPTR", "decimals": 18}, "rpc": ["https://rpc.raptorchain.io/web3"], "faucets": [], "infoURL": "https://raptorchain.io"}, {"name": "SKALE Europa Hub Testnet", "chainId": 1444673419, "shortName": "europa-testnet", "networkId": 1444673419, "nativeCurrency": {"name": "sFUEL", "symbol": "sFUEL", "decimals": 18}, "rpc": ["https://testnet.skalenodes.com/v1/juicy-low-small-testnet"], "faucets": ["https://www.sfuelstation.com/"], "infoURL": "https://europahub.network/"}, {"name": "SKALE Nebula Hub", "chainId": 1482601649, "shortName": "nebula-mainnet", "networkId": 1482601649, "nativeCurrency": {"name": "sFUEL", "symbol": "sFUEL", "decimals": 18}, "rpc": ["https://mainnet.skalenodes.com/v1/green-giddy-denebola", "wss://mainnet-proxy.skalenodes.com/v1/ws/green-giddy-denebola"], "faucets": ["https://sfuel.skale.network/"], "infoURL": "https://nebulachain.io/"}, {"name": "Deprecated SKALE Titan Hub Testnet", "chainId": 1517929550, "shortName": "deprecated-titan-testnet", "networkId": 1517929550, "nativeCurrency": {"name": "sFUEL", "symbol": "sFUEL", "decimals": 18}, "rpc": ["https://staging-v3.skalenodes.com/v1/staging-aware-chief-gianfar", "wss://staging-v3.skalenodes.com/v1/ws/staging-aware-chief-gianfar"], "faucets": ["https://sfuel.dirtroad.dev/staging"], "infoURL": ""}, {"name": "SKALE Calypso Hub", "chainId": 1564830818, "shortName": "calypso-mainnet", "networkId": 1564830818, "nativeCurrency": {"name": "sFUEL", "symbol": "sFUEL", "decimals": 18}, "rpc": ["https://mainnet.skalenodes.com/v1/honorable-steel-rasalhague"], "faucets": ["https://sfuel.dirtroad.dev"], "infoURL": "https://calypsohub.network/"}, {"name": "Harmony Mainnet Shard 0", "chainId": 1666600000, "shortName": "hmy-s0", "networkId": 1666600000, "nativeCurrency": {"name": "ONE", "symbol": "ONE", "decimals": 18}, "rpc": ["https://api.harmony.one", "https://a.api.s0.t.hmny.io", "https://api.s0.t.hmny.io", "https://rpc.ankr.com/harmony", "https://harmony.api.onfinality.io/public", "https://1rpc.io/one", "https://harmony-0.drpc.org", "wss://harmony-0.drpc.org"], "faucets": [], "infoURL": "https://www.harmony.one/"}, {"name": "Harmony Mainnet Shard 1", "chainId": 1666600001, "shortName": "hmy-s1", "networkId": 1666600001, "nativeCurrency": {"name": "ONE", "symbol": "ONE", "decimals": 18}, "rpc": ["https://api.s1.t.hmny.io", "https://harmony-1.drpc.org", "wss://harmony-1.drpc.org"], "faucets": [], "infoURL": "https://www.harmony.one/"}, {"name": "Harmony Mainnet Shard 2", "chainId": 1666600002, "shortName": "hmy-s2", "networkId": 1666600002, "nativeCurrency": {"name": "ONE", "symbol": "ONE", "decimals": 18}, "rpc": ["https://api.s2.t.hmny.io"], "faucets": [], "infoURL": "https://www.harmony.one/"}, {"name": "Harmony Mainnet Shard 3", "chainId": 1666600003, "shortName": "hmy-s3", "networkId": 1666600003, "nativeCurrency": {"name": "ONE", "symbol": "ONE", "decimals": 18}, "rpc": ["https://api.s3.t.hmny.io"], "faucets": [], "infoURL": "https://www.harmony.one/"}, {"name": "Harmony Testnet Shard 0", "chainId": 1666700000, "shortName": "hmy-b-s0", "networkId": 1666700000, "nativeCurrency": {"name": "ONE", "symbol": "ONE", "decimals": 18}, "rpc": ["https://api.s0.b.hmny.io"], "faucets": ["https://faucet.pops.one"], "infoURL": "https://www.harmony.one/"}, {"name": "Harmony Testnet Shard 1", "chainId": 1666700001, "shortName": "hmy-b-s1", "networkId": 1666700001, "nativeCurrency": {"name": "ONE", "symbol": "ONE", "decimals": 18}, "rpc": ["https://api.s1.b.hmny.io"], "faucets": ["https://faucet.pops.one"], "infoURL": "https://www.harmony.one/"}, {"name": "Harmony Devnet Shard 0", "chainId": 1666900000, "shortName": "hmy-ps-s0", "networkId": 1666900000, "nativeCurrency": {"name": "ONE", "symbol": "ONE", "decimals": 18}, "rpc": ["https://api.s0.ps.hmny.io"], "faucets": [], "infoURL": "https://www.harmony.one/"}, {"name": "Harmony Devnet Shard 1", "chainId": 1666900001, "shortName": "hmy-ps-s1", "networkId": 1666900001, "nativeCurrency": {"name": "ONE", "symbol": "ONE", "decimals": 18}, "rpc": ["https://api.s1.ps.hmny.io"], "faucets": [], "infoURL": "https://www.harmony.one/"}, {"name": "<PERSON><PERSON><PERSON>", "chainId": 1802203764, "shortName": "kkrt-sepolia", "networkId": 1802203764, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://sepolia-rpc.kakarot.org"], "faucets": [], "infoURL": "https://kakarot.org"}, {"name": "RARI Chain Testnet", "chainId": 1918988905, "shortName": "rari-testnet", "networkId": 1918988905, "nativeCurrency": {"name": "Ethereum", "symbol": "ETH", "decimals": 18}, "rpc": ["https://testnet.rpc.rarichain.org/http"], "faucets": [], "infoURL": "https://rarichain.org/"}, {"name": "DataHopper", "chainId": 2021121117, "shortName": "hop", "networkId": 2021121117, "nativeCurrency": {"name": "DataHoppers", "symbol": "HOP", "decimals": 18}, "rpc": ["https://23.92.21.121:8545"], "faucets": [], "infoURL": "https://www.DataHopper.com"}, {"name": "SKALE Europa Hub", "chainId": 2046399126, "shortName": "europa", "networkId": 2046399126, "nativeCurrency": {"name": "sFUEL", "symbol": "sFUEL", "decimals": 18}, "rpc": ["https://mainnet.skalenodes.com/v1/elated-tan-skat", "wss://mainnet.skalenodes.com/v1/elated-tan-skat"], "faucets": ["https://ruby.exchange/faucet.html", "https://sfuel.mylilius.com/"], "infoURL": "https://europahub.network/"}, {"name": "Ancient8 Testnet (deprecated)", "chainId": 2863311531, "shortName": "a8old", "networkId": 2863311531, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://rpc-testnet.ancient8.gg"], "faucets": [], "infoURL": "https://ancient8.gg/"}, {"name": "Pirl", "chainId": 3125659152, "shortName": "pirl", "networkId": 3125659152, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "PIRL", "decimals": 18}, "rpc": ["https://wallrpc.pirl.io"], "faucets": [], "infoURL": "https://pirl.io"}, {"name": "OneLedger Testnet Frankenstein", "chainId": 4216137055, "shortName": "frankenstein", "networkId": 4216137055, "nativeCurrency": {"name": "OLT", "symbol": "OLT", "decimals": 18}, "rpc": ["https://frankenstein-rpc.oneledger.network"], "faucets": ["https://frankenstein-faucet.oneledger.network"], "infoURL": "https://oneledger.io"}, {"name": "Palm Testnet", "chainId": 11297108099, "shortName": "tpalm", "networkId": 11297108099, "nativeCurrency": {"name": "PALM", "symbol": "PALM", "decimals": 18}, "rpc": ["https://palm-testnet.infura.io/v3/${INFURA_API_KEY}", "https://palm-testnet.public.blastapi.io"], "faucets": [], "infoURL": "https://palm.network"}, {"name": "Palm", "chainId": 11297108109, "shortName": "palm", "networkId": 11297108109, "nativeCurrency": {"name": "PALM", "symbol": "PALM", "decimals": 18}, "rpc": ["https://palm-mainnet.infura.io/v3/${INFURA_API_KEY}", "https://palm-mainnet.public.blastapi.io"], "faucets": [], "infoURL": "https://palm.network"}, {"name": "GitSwarm Test Network", "chainId": 28872323069, "shortName": "GS-ETH", "networkId": 28872323069, "nativeCurrency": {"name": "GitSwarm Ether", "symbol": "GS-ETH", "decimals": 18}, "rpc": ["https://gitswarm.com:2096"], "faucets": [], "infoURL": "https://gitswarm.com/"}, {"name": "Xai Testnet v2", "chainId": 37714555429, "shortName": "xaitestnet", "networkId": 37714555429, "nativeCurrency": {"name": "sXai", "symbol": "sXAI", "decimals": 18}, "rpc": ["https://testnet-v2.xai-chain.net/rpc"], "faucets": [], "infoURL": "https://xai.games"}, {"name": "Arbitrum Blueberry", "chainId": 88153591557, "shortName": "arb-blueberry", "networkId": 88153591557, "nativeCurrency": {"name": "GelatoCGT", "symbol": "CGT", "decimals": 18}, "rpc": ["https://rpc.arb-blueberry.gelato.digital", "wss://ws.arb-blueberry.gelato.digital"], "faucets": [], "infoURL": "https://raas.gelato.network/rollups/details/public/arb-blueberry"}, {"name": "Ka<PERSON>ot Sepolia Deprecated", "chainId": 107107114116, "shortName": "kkrt-sepolia-deprecated", "networkId": 107107114116, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": [], "faucets": [], "infoURL": "https://kakarot.org"}, {"name": "Alphabet Mainnet", "chainId": 111222333444, "shortName": "alphabet", "networkId": 111222333444, "nativeCurrency": {"name": "ALT", "symbol": "ALT", "decimals": 18}, "rpc": ["https://londonpublic.alphabetnetwork.org", "wss://londonpublic.alphabetnetwork.org/ws/", "https://main-rpc.com", "wss://main-rpc.com/ws/"], "faucets": [], "infoURL": "https://alphabetnetwork.org"}, {"name": "Ntity Mainnet", "chainId": 197710212030, "shortName": "ntt", "networkId": 197710212030, "nativeCurrency": {"name": "Ntity", "symbol": "NTT", "decimals": 18}, "rpc": ["https://rpc.ntity.io"], "faucets": [], "infoURL": "https://ntity.io"}, {"name": "Haradev Testnet", "chainId": 197710212031, "shortName": "ntt-haradev", "networkId": 197710212031, "nativeCurrency": {"name": "<PERSON><PERSON><PERSON>", "symbol": "NTTH", "decimals": 18}, "rpc": ["https://blockchain.haradev.com"], "faucets": [], "infoURL": "https://ntity.io"}, {"name": "Zeniq", "chainId": 383414847825, "shortName": "zeniq", "networkId": 383414847825, "nativeCurrency": {"name": "Zeniq", "symbol": "ZENIQ", "decimals": 18}, "rpc": ["https://smart.zeniq.network:9545"], "faucets": ["https://faucet.zeniq.net/"], "infoURL": "https://www.zeniq.dev/"}, {"name": "PDC Mainnet", "chainId": 666301171999, "shortName": "ipdc", "networkId": 666301171999, "nativeCurrency": {"name": "PDC", "symbol": "PDC", "decimals": 18}, "rpc": ["https://mainnet.ipdc.io/"], "faucets": [], "infoURL": "https://ipdc.io"}, {"name": "Molereum Network", "chainId": 6022140761023, "shortName": "mole", "networkId": 6022140761023, "nativeCurrency": {"name": "Molereum Ether", "symbol": "MOLE", "decimals": 18}, "rpc": ["https://molereum.jdubedition.com"], "faucets": [], "infoURL": "https://github.com/Jdubedition/molereum"}, {"name": "Godwoken Testnet (V1)", "chainId": 868455272153094, "shortName": "gw-testnet-v1-deprecated", "networkId": 868455272153094, "nativeCurrency": {"name": "CKB", "symbol": "CKB", "decimals": 8}, "rpc": ["https://godwoken-testnet-web3-v1-rpc.ckbapp.dev"], "faucets": ["https://homura.github.io/light-godwoken"], "infoURL": "https://www.nervos.org"}, {"name": "DCHAIN Testnet", "chainId": 2713017997578000, "shortName": "dchaint", "networkId": 2713017997578000, "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpc": ["https://dchaintestnet-2713017997578000-1.jsonrpc.testnet.sagarpc.io"], "faucets": [], "infoURL": "https://www.dchain.foundation/"}]