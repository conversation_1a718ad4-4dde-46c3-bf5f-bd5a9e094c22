import React from 'react';
import { Box, Typography, Card, CardContent } from '@mui/material';
import { Helmet } from 'react-helmet-async';

const Trading: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>交易管理 - AI Crypto Trading Agent</title>
      </Helmet>
      
      <Box>
        <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
          交易管理
        </Typography>
        
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              交易功能开发中...
            </Typography>
            <Typography color="textSecondary">
              此页面将包含订单管理、交易历史、实时交易等功能。
            </Typography>
          </CardContent>
        </Card>
      </Box>
    </>
  );
};

export default Trading;
