import React from 'react';
import { Box, Typography, Card, CardContent } from '@mui/material';
import { Helmet } from 'react-helmet-async';

const Strategies: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>策略配置 - AI Crypto Trading Agent</title>
      </Helmet>
      
      <Box>
        <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
          策略配置
        </Typography>
        
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              策略配置功能开发中...
            </Typography>
            <Typography color="textSecondary">
              此页面将提供交易策略的配置、回测、性能分析等功能。
            </Typography>
          </CardContent>
        </Card>
      </Box>
    </>
  );
};

export default Strategies;
