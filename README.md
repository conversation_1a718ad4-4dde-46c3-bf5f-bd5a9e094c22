# AI Crypto Trading Agent

一个智能的加密货币链上交易监控和自动化交易系统，支持以太坊、Binance Smart Chain和Solana网络。

## 功能特性

### 核心功能
- 🔍 **链上数据监控**: 实时监控多链交易数据
- 🎯 **智能选币**: 可定制的潜力币种筛选算法
- 🤖 **自动交易**: 可配置的交易执行策略
- 💼 **多链钱包**: 支持ETH、BSC、SOL钱包管理
- 🌐 **Web界面**: 浏览器端信息获取和操作
- ⚡ **风险管理**: 智能资金管理和风险控制

### 支持的区块链
- Ethereum (ETH)
- Binance Smart Chain (BSC)
- Solana (SOL)

## 项目结构

```
AI_Bot/
├── backend/                 # 后端服务
│   ├── app/
│   │   ├── core/           # 核心模块
│   │   ├── api/            # API接口
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务服务
│   │   └── utils/          # 工具函数
│   ├── requirements.txt
│   └── main.py
├── frontend/               # 前端界面
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── services/
│   │   └── utils/
│   ├── package.json
│   └── public/
├── config/                 # 配置文件
├── data/                   # 数据存储
├── logs/                   # 日志文件
└── scripts/               # 脚本工具
```

## 快速开始

### 环境要求
- Python 3.9+
- Node.js 16+
- Chrome/Chromium (用于Web自动化)

### 一键启动 (推荐)

使用开发环境启动脚本：
```bash
python start_dev.py
```

这个脚本会自动：
- 创建Python虚拟环境
- 安装所有依赖
- 创建配置文件
- 启动前后端服务

### 手动安装

#### 1. 运行安装脚本
```bash
python scripts/setup.py
```

#### 2. 配置设置
编辑配置文件：
```bash
# 复制并编辑配置文件
cp config/config.example.yaml config/config.yaml
cp .env.example .env
```

设置以下重要配置：
- RPC节点地址
- 私钥/助记词 (⚠️ 请妥善保管)
- API密钥
- 风险管理参数

#### 3. 启动服务

**后端服务：**
```bash
cd backend
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate

python main.py
```

**前端服务：**
```bash
cd frontend
npm start
```

## 配置说明

### 选币策略配置
- 交易量筛选
- 价格变动幅度
- 流动性指标
- 技术指标分析
- 社交媒体情绪

### 交易策略配置
- 网格交易
- 趋势跟踪
- 均值回归
- 动量交易
- 套利交易

### 风险管理配置
- 最大仓位限制
- 止损止盈设置
- 资金分配比例
- 最大回撤控制

## 使用指南

### 1. 首次使用

1. **启动系统**：运行 `python start_dev.py`
2. **访问界面**：打开浏览器访问 http://localhost:3000
3. **添加代币**：在"代币监控"页面添加要监控的代币
4. **配置策略**：在"策略配置"页面设置交易策略
5. **启动交易**：系统会自动根据策略生成交易信号

### 2. 主要功能

#### 代币监控
- 实时监控代币价格和交易量
- 支持多链代币 (ETH/BSC/SOL)
- 自动风险评估和筛选

#### 智能交易
- 多种交易策略 (动量/均值回归/机器学习)
- 自动执行买卖订单
- 实时风险管理

#### 投资组合管理
- 实时资产价值计算
- 收益分析和统计
- 风险控制和报警

### 3. 策略说明

#### 动量策略
基于RSI指标和交易量变化，捕捉价格趋势：
- RSI < 30 且交易量放大 → 买入信号
- RSI > 70 → 卖出信号

#### 均值回归策略
基于布林带指标，寻找价格回归机会：
- 价格触及下轨 → 买入信号
- 价格触及上轨 → 卖出信号

#### 机器学习策略
使用随机森林模型，综合多个指标预测价格走势：
- 模型置信度 > 60% → 生成交易信号
- 自动学习历史数据优化策略

### 4. 风险管理

- **仓位控制**：单个代币最大仓位限制
- **止损止盈**：自动设置止损和止盈点
- **日损失限制**：超过日损失阈值自动停止交易
- **流动性检查**：确保代币有足够流动性

## 安全注意事项

⚠️ **重要提醒**:
- 请妥善保管私钥和助记词，建议使用硬件钱包
- 建议先在测试网络进行测试，熟悉系统操作
- 设置合理的风险控制参数，不要投入超过承受能力的资金
- 定期备份配置和数据
- 监控系统运行状态，及时处理异常情况

## 常见问题

### Q: 如何添加新的区块链支持？
A: 在 `app/core/config.py` 中添加新链配置，并在相应服务中实现接口。

### Q: 如何自定义交易策略？
A: 继承 `BaseStrategy` 类，实现 `analyze` 方法，并在策略管理器中注册。

### Q: 系统支持哪些DEX？
A: 目前支持 Uniswap、PancakeSwap、Raydium 等主流DEX。

### Q: 如何设置通知？
A: 在配置文件中设置 Telegram Bot 或邮件通知参数。

## 技术支持

如有问题或建议，请：
1. 查看日志文件 `logs/trading_agent.log`
2. 检查配置文件是否正确
3. 确认网络连接和API密钥有效性

## 免责声明

本软件仅供学习和研究使用。加密货币交易存在高风险，可能导致资金损失。使用本软件进行实际交易的风险由用户自行承担。开发者不对任何损失负责。

## 许可证

MIT License
