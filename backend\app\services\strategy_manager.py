"""
策略管理器
"""

import asyncio
import json
import yaml
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from abc import ABC, abstractmethod
from loguru import logger
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
import ta

from app.core.config import settings
from app.models.trading import TradingSignal, Token, ChainType
from app.models.base import SessionLocal


class BaseStrategy(ABC):
    """策略基类"""

    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.is_active = True

    @abstractmethod
    async def analyze(self, token_data: Dict[str, Any]) -> Optional[TradingSignal]:
        """分析代币并生成交易信号"""
        pass

    @abstractmethod
    def get_required_data(self) -> List[str]:
        """获取策略所需的数据字段"""
        pass


class MomentumStrategy(BaseStrategy):
    """动量策略"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__("momentum", config)
        self.rsi_period = config.get("rsi_period", 14)
        self.rsi_oversold = config.get("rsi_oversold", 30)
        self.rsi_overbought = config.get("rsi_overbought", 70)
        self.volume_threshold = config.get("volume_threshold", 1.5)

    async def analyze(self, token_data: Dict[str, Any]) -> Optional[TradingSignal]:
        """动量分析"""
        try:
            prices = token_data.get("prices", [])
            volumes = token_data.get("volumes", [])

            if len(prices) < self.rsi_period:
                return None

            df = pd.DataFrame({
                'price': prices,
                'volume': volumes
            })

            # 计算技术指标
            df['rsi'] = ta.momentum.RSIIndicator(df['price'], window=self.rsi_period).rsi()
            df['volume_ratio'] = df['volume'] / df['volume'].rolling(20).mean()

            current_rsi = df['rsi'].iloc[-1]
            current_volume_ratio = df['volume_ratio'].iloc[-1]
            current_price = df['price'].iloc[-1]

            # 生成信号
            signal_type = None
            confidence = 0.0

            if current_rsi < self.rsi_oversold and current_volume_ratio > self.volume_threshold:
                signal_type = "buy"
                confidence = min(0.9, (self.rsi_oversold - current_rsi) / self.rsi_oversold +
                               (current_volume_ratio - 1) * 0.2)
            elif current_rsi > self.rsi_overbought:
                signal_type = "sell"
                confidence = min(0.9, (current_rsi - self.rsi_overbought) / (100 - self.rsi_overbought))

            if signal_type:
                return TradingSignal(
                    signal_id=f"{self.name}_{token_data['address']}_{int(datetime.utcnow().timestamp())}",
                    strategy_name=self.name,
                    chain=ChainType(token_data['chain']),
                    token_address=token_data['address'],
                    token_symbol=token_data['symbol'],
                    signal_type=signal_type,
                    confidence=confidence,
                    current_price=current_price,
                    analysis_data=json.dumps({
                        "rsi": current_rsi,
                        "volume_ratio": current_volume_ratio,
                        "indicators": {
                            "rsi_period": self.rsi_period,
                            "rsi_oversold": self.rsi_oversold,
                            "rsi_overbought": self.rsi_overbought
                        }
                    })
                )

            return None

        except Exception as e:
            logger.error(f"动量策略分析失败: {e}")
            return None

    def get_required_data(self) -> List[str]:
        return ["prices", "volumes", "timestamps"]


class MeanReversionStrategy(BaseStrategy):
    """均值回归策略"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__("mean_reversion", config)
        self.bollinger_period = config.get("bollinger_period", 20)
        self.bollinger_std = config.get("bollinger_std", 2)
        self.min_deviation = config.get("min_deviation", 0.02)

    async def analyze(self, token_data: Dict[str, Any]) -> Optional[TradingSignal]:
        """均值回归分析"""
        try:
            prices = token_data.get("prices", [])

            if len(prices) < self.bollinger_period:
                return None

            df = pd.DataFrame({'price': prices})

            # 计算布林带
            bollinger = ta.volatility.BollingerBands(
                df['price'],
                window=self.bollinger_period,
                window_dev=self.bollinger_std
            )

            df['bb_upper'] = bollinger.bollinger_hband()
            df['bb_lower'] = bollinger.bollinger_lband()
            df['bb_middle'] = bollinger.bollinger_mavg()

            current_price = df['price'].iloc[-1]
            bb_upper = df['bb_upper'].iloc[-1]
            bb_lower = df['bb_lower'].iloc[-1]
            bb_middle = df['bb_middle'].iloc[-1]

            # 计算偏离程度
            upper_deviation = (current_price - bb_upper) / bb_upper
            lower_deviation = (bb_lower - current_price) / bb_lower

            signal_type = None
            confidence = 0.0

            if current_price <= bb_lower and lower_deviation >= self.min_deviation:
                signal_type = "buy"
                confidence = min(0.9, lower_deviation * 5)
            elif current_price >= bb_upper and upper_deviation >= self.min_deviation:
                signal_type = "sell"
                confidence = min(0.9, upper_deviation * 5)

            if signal_type:
                return TradingSignal(
                    signal_id=f"{self.name}_{token_data['address']}_{int(datetime.utcnow().timestamp())}",
                    strategy_name=self.name,
                    chain=ChainType(token_data['chain']),
                    token_address=token_data['address'],
                    token_symbol=token_data['symbol'],
                    signal_type=signal_type,
                    confidence=confidence,
                    current_price=current_price,
                    target_price=bb_middle,
                    analysis_data=json.dumps({
                        "bb_upper": bb_upper,
                        "bb_lower": bb_lower,
                        "bb_middle": bb_middle,
                        "deviation": upper_deviation if signal_type == "sell" else lower_deviation
                    })
                )

            return None

        except Exception as e:
            logger.error(f"均值回归策略分析失败: {e}")
            return None

    def get_required_data(self) -> List[str]:
        return ["prices", "timestamps"]


class MLStrategy(BaseStrategy):
    """机器学习策略"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__("ml_strategy", config)
        self.model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.is_trained = False
        self.feature_columns = [
            'rsi', 'macd', 'bb_position', 'volume_ratio',
            'price_change_1h', 'price_change_24h'
        ]

    async def analyze(self, token_data: Dict[str, Any]) -> Optional[TradingSignal]:
        """机器学习分析"""
        try:
            if not self.is_trained:
                await self._train_model()

            features = await self._extract_features(token_data)
            if features is None:
                return None

            # 预测
            prediction = self.model.predict_proba([features])[0]

            # 0: sell, 1: hold, 2: buy
            max_prob_idx = np.argmax(prediction)
            confidence = prediction[max_prob_idx]

            if confidence < 0.6:  # 置信度阈值
                return None

            signal_type = ["sell", "hold", "buy"][max_prob_idx]

            if signal_type != "hold":
                return TradingSignal(
                    signal_id=f"{self.name}_{token_data['address']}_{int(datetime.utcnow().timestamp())}",
                    strategy_name=self.name,
                    chain=ChainType(token_data['chain']),
                    token_address=token_data['address'],
                    token_symbol=token_data['symbol'],
                    signal_type=signal_type,
                    confidence=confidence,
                    current_price=token_data['prices'][-1],
                    analysis_data=json.dumps({
                        "prediction_probabilities": prediction.tolist(),
                        "features": dict(zip(self.feature_columns, features))
                    })
                )

            return None

        except Exception as e:
            logger.error(f"机器学习策略分析失败: {e}")
            return None

    async def _train_model(self):
        """训练模型"""
        try:
            # 这里应该从历史数据训练模型
            # 为了演示，我们跳过实际训练
            self.is_trained = True
            logger.info("机器学习模型训练完成")
        except Exception as e:
            logger.error(f"模型训练失败: {e}")

    async def _extract_features(self, token_data: Dict[str, Any]) -> Optional[List[float]]:
        """提取特征"""
        try:
            prices = token_data.get("prices", [])
            volumes = token_data.get("volumes", [])

            if len(prices) < 50:  # 需要足够的历史数据
                return None

            df = pd.DataFrame({
                'price': prices,
                'volume': volumes
            })

            # 计算技术指标
            df['rsi'] = ta.momentum.RSIIndicator(df['price']).rsi()
            macd = ta.trend.MACD(df['price'])
            df['macd'] = macd.macd_diff()

            bollinger = ta.volatility.BollingerBands(df['price'])
            df['bb_position'] = (df['price'] - bollinger.bollinger_lband()) / (
                bollinger.bollinger_hband() - bollinger.bollinger_lband()
            )

            df['volume_ratio'] = df['volume'] / df['volume'].rolling(20).mean()
            df['price_change_1h'] = df['price'].pct_change(periods=12)  # 假设5分钟数据
            df['price_change_24h'] = df['price'].pct_change(periods=288)

            # 获取最新特征
            latest_features = []
            for col in self.feature_columns:
                value = df[col].iloc[-1]
                if pd.isna(value):
                    return None
                latest_features.append(float(value))

            return latest_features

        except Exception as e:
            logger.error(f"特征提取失败: {e}")
            return None

    def get_required_data(self) -> List[str]:
        return ["prices", "volumes", "timestamps"]


class StrategyManager:
    """策略管理器"""

    def __init__(self):
        self.strategies: Dict[str, BaseStrategy] = {}
        self.is_active = False
        self.analysis_tasks = []

        # 加载策略配置
        self._load_strategies()

    def _load_strategies(self):
        """加载策略配置"""
        try:
            import os
            # 构建配置文件的绝对路径
            config_path = settings.STRATEGY_CONFIG_PATH
            if not os.path.isabs(config_path):
                # 相对于项目根目录
                project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
                config_path = os.path.join(project_root, config_path)

            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            # 初始化策略
            for strategy_name, strategy_config in config.get('strategies', {}).items():
                if strategy_config.get('enabled', False):
                    if strategy_name == "momentum":
                        self.strategies[strategy_name] = MomentumStrategy(strategy_config)
                    elif strategy_name == "mean_reversion":
                        self.strategies[strategy_name] = MeanReversionStrategy(strategy_config)
                    elif strategy_name == "ml_strategy":
                        self.strategies[strategy_name] = MLStrategy(strategy_config)

            logger.info(f"加载了 {len(self.strategies)} 个策略")

        except Exception as e:
            logger.error(f"加载策略配置失败: {e}")
            # 使用默认策略
            self.strategies["momentum"] = MomentumStrategy({})

    async def start(self):
        """启动策略管理器"""
        if self.is_active:
            return

        self.is_active = True

        # 启动分析任务
        self.analysis_tasks = [
            asyncio.create_task(self._run_strategy_analysis())
        ]

        logger.info("策略管理器启动成功")

    async def stop(self):
        """停止策略管理器"""
        if not self.is_active:
            return

        self.is_active = False

        for task in self.analysis_tasks:
            task.cancel()

        await asyncio.gather(*self.analysis_tasks, return_exceptions=True)

        logger.info("策略管理器已停止")

    async def _run_strategy_analysis(self):
        """运行策略分析"""
        while self.is_active:
            try:
                # 获取活跃代币
                db = SessionLocal()
                try:
                    tokens = db.query(Token).filter(Token.is_active == True).all()

                    for token in tokens:
                        token_data = await self._get_token_data(token)
                        if token_data:
                            await self._analyze_token(token_data)

                finally:
                    db.close()

            except Exception as e:
                logger.error(f"策略分析失败: {e}")

            await asyncio.sleep(60)  # 每分钟分析一次

    async def _get_token_data(self, token: Token) -> Optional[Dict[str, Any]]:
        """获取代币数据"""
        # 这里应该从数据源获取代币的历史价格、交易量等数据
        # 为了演示，返回模拟数据
        return {
            "address": token.address,
            "symbol": token.symbol,
            "chain": token.chain.value,
            "prices": [100.0] * 100,  # 模拟价格数据
            "volumes": [1000.0] * 100,  # 模拟交易量数据
            "timestamps": list(range(100))
        }

    async def _analyze_token(self, token_data: Dict[str, Any]):
        """分析代币"""
        for strategy_name, strategy in self.strategies.items():
            try:
                signal = await strategy.analyze(token_data)
                if signal:
                    await self._save_signal(signal)

            except Exception as e:
                logger.error(f"策略 {strategy_name} 分析失败: {e}")

    async def _save_signal(self, signal: TradingSignal):
        """保存交易信号"""
        db = SessionLocal()
        try:
            db.add(signal)
            db.commit()
            logger.info(f"生成交易信号: {signal.signal_type} {signal.token_symbol} (置信度: {signal.confidence:.2f})")
        finally:
            db.close()
